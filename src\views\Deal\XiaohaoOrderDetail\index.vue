<template>
  <div class="xiaohao-order-detail-page">
    <nav-bar-2 :title="$t('订单详情')" :border="true"></nav-bar-2>
    <div class="main">
      <!-- <div class="top-content" @click.capture.stop="toXiaohaoDetail"> -->
      <div class="top-content">
        <buy-item
          :info="orderInfo"
          :isDetail="true"
          @afterDelete="afterDelete"
        ></buy-item>
      </div>
      <div class="bottom-content">
        <div class="order-info">
          <p>
            {{ $t('订单号') }}：<span>{{ orderInfo.order_id }}</span>
          </p>
          <p>
            {{ $t('商品编号') }}：<span>{{
              orderInfo.trade_snapshot.trade_id
            }}</span>
          </p>
          <p>
            {{ $t('创建时间') }}：<span>{{ orderInfo.create_time }}</span>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import buyItem from '../components/buy-item';
export default {
  name: 'XiaohaoOrderDetail',
  components: {
    buyItem,
  },
  data() {
    return {
      orderInfo: {},
    };
  },
  created() {
    this.orderInfo = this.$route.params.info;
  },
  methods: {
    afterDelete() {
      this.$router.go(-1);
    },
    toXiaohaoDetail() {
      this.toPage(
        'XiaohaoDetail',
        {
          id: this.orderInfo.trade_snapshot.trade_id,
        },
        1,
      );
    },
  },
};
</script>

<style lang="less" scoped>
.xiaohao-order-detail-page {
  .main {
    .top-content {
      padding-bottom: 5 * @rem;
      /deep/ .deal-item-component {
        .deal-item {
          width: unset;
          box-shadow: unset;
          margin-top: 10 * @rem;
          padding: 10 * @rem 18 * @rem;
          border-bottom: 0;
          .top-content {
            border-bottom: 0;
            .top-right {
              height: unset;
            }
          }
        }
      }
    }
    .bottom-content {
      border-top: 10 * @rem solid #f6f6f6;
      padding: 14 * @rem;
      .order-info {
        p {
          font-size: 14 * @rem;
          color: #000000;
          line-height: 22 * @rem;
          span {
            font-size: 14 * @rem;
            color: #666666;
          }
        }
      }
    }
  }
}
</style>
