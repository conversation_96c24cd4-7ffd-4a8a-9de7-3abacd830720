// 广告弹窗工具函数
import { ApiV2024IndexGetPopUp } from '@/api/views/system.js';
import useAdActivityPopup from '@/components/ad-activity-popup/index.js';
import useCloudNoticePopup from '@/components/cloud-notice-popup/index.js';
import BASEPARAMS from '@/utils/baseParams';

let popup_config_name = 'POPUP_CONFIG';
let now_popup_config_name = 'NOW_POPUP_CONFIG';

/**
 * 获取广告弹窗数据并显示
 * @param {Object} options - 配置选项
 * @param {Number} options.box_position - 弹窗位置ID，必传
 * @param {Object} options.userInfo - 用户信息，可选
 * @returns {Promise<Object>} - 弹窗数据
 */
export async function getAdPopupData(options) {
  if (!options || !options.box_position) {
    return false;
  }
  popup_config_name = `${popup_config_name}_${options.box_position}`;
  now_popup_config_name = `${now_popup_config_name}_${options.box_position}`;

  try {
    const res = await ApiV2024IndexGetPopUp({
      box_position: options.box_position,
    });

    // 首页云挂机通知弹窗
    if (
      res.data.popup_config[0]?.type == 8 &&
      res.data.popup_config[0]?.action?.action_code == 29 &&
      options.userInfo.token) {
      const today = new Date().toDateString();
      const userId = options.userInfo.user_id;
      if (sessionStorage.getItem(userId) === today) {
        return;
      }
      sessionStorage.setItem(userId, today);
      useCloudNoticePopup({
        content: res.data.popup_config[0]
      })
      return false
    }

    // 1. 过滤掉不合符要求的弹窗数据
    let userData = res.data.user_data;
    let popupConfig = res.data.popup_config;

    let nowDate = new Date().getDate();
    if (nowDate != localStorage.getItem('popupDay')) {
      localStorage.removeItem(now_popup_config_name);
      let localPopupList = localStorage.getItem(popup_config_name)
        ? JSON.parse(localStorage.getItem(popup_config_name))
        : [];
      if (localPopupList.length) {
        localPopupList.forEach(item => {
          if (popupConfig.length) {
            popupConfig.forEach(item1 => {
              if (item1.pop_id == item.pop_id && item1.pop_type != 5) {
                item.pop_type = item1.pop_type;
                item.times = 0;
              }
            });
          } else {
            if (item.pop_type != 5) {
              item.times = 0;
            }
          }
        });
        localStorage.setItem(popup_config_name, JSON.stringify(localPopupList));
      }
    }
    // 修改弹窗数组顺序，已弹窗的放后面
    let filterPopupConfig = filterByIndex(popupConfig);

    // 过滤掉用户不符合要求的弹窗
    let filterPopupConfig1 = filterByCondition(filterPopupConfig, userData);

    // 过滤掉本地已弹出的弹窗
    let localPopupConfigList = [];
    try {
      const localStr = localStorage.getItem(popup_config_name);
      localPopupConfigList = localStr ? JSON.parse(localStr) : [];
    } catch (e) {
      // 解析失败或其他错误时，清除本地缓存
      localStorage.removeItem(popup_config_name);
    }

    let filterPopupConfig2 = filterByLocalPopup(
      filterPopupConfig1,
      localPopupConfigList,
      options.userInfo,
    );
    const popupConfigData = filterPopupConfig2.length
      ? filterPopupConfig2[0]
      : {};
    if (popupConfigData.pop_id) {
      useAdActivityPopup({
        content: popupConfigData,
      });
    }

    localStorage.setItem('popupDay', nowDate);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * 过滤掉不符合要求的弹窗
 * @param {Array} list - 弹窗列表
 * @param {Object} userStatus - 用户状态
 * @returns {Array} - 过滤后的弹窗列表
 */
function filterByCondition(list, userStatus) {
  return list.filter(item => {
    const conditions = item.conditions;
    if (!conditions || !conditions.length) {
      return true; // 无条件 或 不过滤
    }

    // 获取要检查的字段名
    // condition_type  1-版本更新 2-签到状态 3-省钱卡状态 4-svip状态 5-游戏类型 6-游戏ID 7-游戏时长 8-游戏等级 9-登录状态
    const conditionMap = {
      2: 'is_sign',
      3: 'is_money_card',
      4: 'is_svip',
      5: 'game_type',
      6: 'game_id',
      7: 'sum_time',
      8: 'grade',
      9: 'is_login',
    };

    let result = true;
    conditions.forEach(condition => {
      const { condition_type, condition_value } = condition;
      const userField = conditionMap[condition_type];
      if (userField) {
        const userValue = userStatus[userField];

        if (condition_value == 1) {
          // 不满足中断循环
          if (userValue !== true) {
            result = false;
          }
        } else if (condition_value == 2) {
          // 不满足中断循环
          if (userValue !== false) {
            result = false;
          }
        }
      }
    });

    return result;
  });
}

/**
 * 过滤掉本地已弹出的弹窗
 * @param {Array} needFilterList - 需要过滤的弹窗列表
 * @param {Array} localPopupConfigList - 本地存储的弹窗列表
 * @param {Object} userInfo - 用户信息
 * @returns {Array} - 过滤后的弹窗列表
 */
function filterByLocalPopup(needFilterList, localPopupConfigList, userInfo) {
  const now = Math.floor(Date.now() / 1000);
  return needFilterList.filter(item1 => {
    // 找到匹配的项
    const match = localPopupConfigList.find(item2 => {
      if (userInfo && userInfo.token) {
        return (
          userInfo.user_id === item2.user_id && item1.pop_id === item2.pop_id
        );
      } else {
        return BASEPARAMS.uuid === item2.uuid && item1.pop_id === item2.pop_id;
      }
    });

    if (!match) {
      // 没有匹配项，保留
      return true;
    }

    // 每天一次
    if (match.pop_type == 1) {
      return now - match.close_time > 1 * 24 * 60 * 60;
    }

    // 仅一次
    if (match.pop_type == 3) {
      return false;
    }

    // 符合条件
    if (match.pop_type == 4 && !match.close_check) {
      return !match.close_check;
    }

    // 每次启动不考虑次数
    if (item1.pop_type != 2 && item1.times <= match.times) {
      return false;
    }

    // 当前时间减去关闭时间 小于等于 remind_days* 24 * 60 * 60 就过滤掉
    return match.close_check
      ? now - match.close_time > item1.remind_days * 24 * 60 * 60
      : true;
  });
}

/**
 * 根据权重随机选择一个弹窗
 * @param {Array} items - 弹窗列表
 * @returns {Object|null} - 选中的弹窗
 */
function weightedRandomSelect(items) {
  let weightArr = [];
  let sumWeight = 0;

  if (
    !items ||
    Object.keys(items).length === 0 ||
    items === null ||
    typeof items !== 'object'
  ) {
    return null;
  }

  try {
    weightArr = items.map(item => item.weight || 0);
    sumWeight = weightArr.reduce((sum, weight) => sum + weight, 0);
  } catch (e) {
    weightArr = [];
    sumWeight = 0;
  }

  if (!weightArr?.length || !sumWeight) {
    return items[0] || null;
  }

  let rand = Math.floor(Math.random() * sumWeight) + 1;

  let key = -1;
  for (let k = 0; k < weightArr.length; k++) {
    key = k;
    rand -= weightArr[k];
    if (rand <= 0) {
      break;
    }
  }

  if (key === -1) {
    key = Math.floor(Math.random() * items.length);
  }

  return items[key] || null;
}

/**
 * 修改弹窗顺序
 * @param {Array} popupList - 弹窗列表
 * @returns {Object|null} - 选中的弹窗
 */
function filterByIndex(list) {
  let popupList = [...list];
  if (popupList.length == 0) {
    return [];
  }
  let nowPopup = localStorage.getItem(now_popup_config_name)
    ? JSON.parse(localStorage.getItem(now_popup_config_name))
    : {};
  if (nowPopup.pop_id) {
    let index = popupList.findIndex(item => {
      return item.pop_id == nowPopup.pop_id;
    });
    if (index != -1) {
      let bPopupList = popupList.splice(0, index + 1);
      return [...popupList, ...bPopupList];
    } else {
      return popupList;
    }
  } else {
    return popupList;
  }
}
