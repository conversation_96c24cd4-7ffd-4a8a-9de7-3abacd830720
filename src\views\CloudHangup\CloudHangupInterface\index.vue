<template>
  <div class="page" ref="page">
    <!-- 加载画面 -->
    <div
      class="phoneBox"
      v-if="receiveData"
      id="phoneBox"
      ref="phoneBox"
      @touchstart="handleTouchStart"
    >
      <div class="loading-content" v-if="!isToEnterTheGame">
        <div class="loading-game">
          <div class="loading-game-img">
            <img :src="onHookGameInfo.titlepic" alt="" />
          </div>
          <div class="loading-game-title">
            <div class="game-title-box">
              <span class="game-main-title">{{
                onHookGameInfo.main_title
              }}</span>
              <span class="game-subtitle" v-if="onHookGameInfo.subtitle">
                <div
                  :class="{
                    'text-scroll': onHookGameInfo.subtitle.length > 5,
                  }"
                >
                  {{ onHookGameInfo.subtitle }}
                </div>
              </span>
            </div>
            <div class="loading-des">{{ this.loading_tip }}</div>
          </div>
        </div>

        <div class="loading-prompt" v-show="isShowProgress">
          <div class="Percentage">
            {{ cloudInfo.loading_text }}（{{ progress }}%）
          </div>
          <div class="progress">
            <Progress :width="315" :progress="progress" :strokeWidth="10" />
          </div>
          <div class="tips">
            {{ loadingTip }}
          </div>
        </div>
        <div class="backstage-loading" @click="clickBsLoading">
          <span>{{ $t('后台加载') }}</span>
          <p>
            <img src="~@/assets/images/cloudHangup/cloud-union.png" alt=""
          /></p>
        </div>
      </div>
      <div
        id="cloudVideoContainer"
        class="cloudVideoContainer"
        v-if="isToEnterTheGame && cloud_type == 2"
        ref="cloudVideoContainer"
      ></div>

      <div
        id="device_widgets"
        class="device_widgets"
        :class="{
          'portrait': cloudRotation == 1,
          'left-24': cloudRotation != 1,
        }"
        v-if="isDeviceWidgets && isToEnterTheGame"
      >
        <div class="device_widgets_title">
          <div class="title_name">
            {{ $t('设备名') }}:<span> {{ checkCloudDeviceItem.title }}</span>
          </div>
          <div class="title_close" @click.stop="isDeviceWidgets = false">
            <span>{{ $t('收起') }}</span>
            <span
              ><img src="~@/assets/images/cloudHangup/down-close.png" alt=""
            /></span>
          </div>
        </div>
        <div class="device_widgets_nav">
          <div class="nav_item" @click.stop.prevent="backCloudHangup()">
            <span
              ><img src="~@/assets/images/cloudHangup/fh-img.png" alt=""
            /></span>
            <div>{{ $t('盒子') }}</div>
          </div>
          <div class="nav_item">
            <span
              ><img src="~@/assets/images/cloudHangup/yc-img.png" alt=""
            /></span>
            <div>{{ cloudRunInformation.netDelay || 0 }}ms</div>
          </div>
          <!-- 分辨率 -->
          <!-- <div
            class="nav_item"
            style="position: relative;"
            v-if="isToEnterTheGame"
            @click="showPixelList = !showPixelList"
          >
            <span><img :src="checkPixelClarityItem.logoUrl" alt=""/></span>
            <div>{{ checkPixelClarityItem.title }}</div>
            <div class="select-screen-list" v-if="showPixelList">
              <div class="screen-list-content">
                <div
                  v-for="(item, index) in pixelClarityList"
                  :key="index"
                  class="screen-list-title"
                  :class="{ active: checkPixelClarityItem.id == item.id }"
                  @click="selectClarity(item)"
                >
                  {{ item.title }}
                </div>
              </div>
            </div>
          </div> -->
          <!-- 全屏 -->
          <div
            class="nav_item"
            style="position: relative"
            v-if="isToEnterTheGame"
            @click="showScreenList = !showScreenList"
          >
            <span
              ><img src="~@/assets/images/cloudHangup/qp-img.png" alt=""
            /></span>
            <div>{{ checkScreenItem.title }}</div>
            <transition name="slide-fade">
              <div class="select-screen-list" v-if="showScreenList">
                <div class="screen-list-content">
                  <div
                    v-for="(item, index) in screenList"
                    :key="index"
                    class="screen-list-title"
                    :class="{ active: checkScreenItem.id == item.id }"
                    @click="selectScreen(item)"
                  >
                    {{ item.title }}
                  </div>
                </div>
              </div></transition
            >
          </div>
          <div
            class="nav_item"
            v-if="isToEnterTheGame"
            @click.stop="refreshTheGame()"
          >
            <span
              ><img src="~@/assets/images/cloudHangup/sx-img.png" alt=""
            /></span>
            <div>{{ $t('刷新') }}</div>
          </div>
          <div
            v-if="isShowHomeBtn"
            class="nav_item"
            @click.stop="backToTheDesktop()"
          >
            <span
              ><img src="~@/assets/images/cloudHangup/zm-img.png" alt=""
            /></span>
            <div>{{ $t('桌面') }}</div>
          </div>
        </div>
      </div>
      <VueDragResize
        class="drag"
        :class="{ portrait: cloudRotation == 1 }"
        ref="drag"
        :isActive="true"
        :isResizable="false"
        :isDraggable="true"
        :parentLimitation="true"
        :w="46"
        :h="46"
        :x="customX"
        :y="customY"
        @resizing="resize"
        @dragging="resize"
        @dragstop="onDragstop"
        v-if="!isDeviceWidgets && isToEnterTheGame"
      >
        <img src="~@/assets/images/cloudHangup/xzj-logo.png" alt="" />
      </VueDragResize>
    </div>

    <!-- 云挂机弹窗 -->
    <on-hook-popup
      :class="{ portrait: cloudRotation == 1 }"
      :errMsg="errMsg"
      :type="type"
      :show.sync="onHookPopupShow"
      @send-cancelData="receiveCancelData"
      @send-confirmData="receiveConfirmData"
    ></on-hook-popup>
    <!-- 重新加载 -->
    <van-dialog
      v-model="reloadPopupShow"
      :showConfirmButton="false"
      :lockScroll="false"
      :overlay-style="{ 'z-index': '2996' }"
      class="onhook-popup"
    >
      <div class="title">{{ $t('提示') }}</div>
      <div class="content" v-if="!isTimeOut">
        <span v-if="errCode">
          错误码： {{ errCode }}
          <br />
        </span>
        <span>
          {{ $t('连接失败，是否重新连接？') }}
        </span>
      </div>
      <div class="content" v-else>
        {{
          cloudInfo.timeout_tip ||
          '由于您长时间未操作已断开云挂机画面连接，此操作不影响您的挂机进程，是否重新连接？'
        }}
      </div>
      <div class="btn-info">
        <div class="cancel" @click.stop="reloadCancel()">{{ $t('取消') }}</div>
        <div class="confirm" @click.stop="reloadConfirm()">
          {{ $t('确定') }}
        </div>
      </div>
    </van-dialog>
    <!-- 启动失败提示 -->
    <van-dialog
      v-model="failurePromptPopupShow"
      :showConfirmButton="false"
      :lockScroll="false"
      :overlay-style="{ 'z-index': '2996' }"
      class="onhook-popup"
    >
      <div class="title">{{ $t('提示') }}</div>
      <div class="content">{{ errPopupMsg }}</div>
      <div class="btn-info btn-info-24">
        <div class="confirm cancel-text" @click.stop="returnToGameBox()">
          {{ $t('确定') }}
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import {
  ApiCloudLoadPage,
  ApiCloudAppOperate,
  ApiCloudUpdateGameVer,
  ApiCloudList,
} from '@/api/views/upCloud.js';
import CloudGamePopup from '../components/cloud-game-popup/index.vue';
import OnHookPopup from '../components/on-hook-popup';
import Progress from '@/components/progress-item/index.vue';
import VueDragResize from 'vue-drag-resize';
import h5Page from '@/utils/h5Page';
import {
  BOX_openInNewNavWindow,
  BOX_openInBrowser,
  BOX_openInNewWindow,
} from '@/utils/box.uni.js';
import { ApiUserInfoEx } from '@/api/views/users';
import { mapGetters } from 'vuex';
import { handleActionCode } from '@/utils/actionCode.js';
import BASEPARAMS from '@/utils/baseParams';
import { sdkUrlList, formatHashPathToRouteName } from '@/utils/tools.js';
export default {
  name: 'CloudHangupInterface',
  components: {
    CloudGamePopup,
    VueDragResize,
    OnHookPopup,
    Progress,
  },
  data() {
    return {
      isDeviceWidgets: false, //小部件
      reloadPopupShow: false, // 重新加载
      isTimeOut: false, // 长时间未操作提示文案
      failurePromptPopupShow: false, //启动失败提示
      onHookPopupShow: false,
      onHookGameInfo: [], // 需要挂机游戏信息
      checkCloudDeviceItem: [], // 当前设备
      // isFromBoxIn: false,
      reload: false,
      isEnterGame: false, // 安装 - 进入游戏
      uuid: '',
      serverToken: '',
      isShowHomeBtn: false,
      width: 0,
      height: 0,
      top: 0,
      left: 0,
      customX: 31,
      customY: 40,
      errMsg: '',
      cloudRotation: 0, // 0竖屏 1横屏
      phoneBoxClientWidth: 0,
      phoneBoxClientHeight: 0,
      type: 0, // 0 返回 1 更新
      progress: 0, // 进度条
      // asyncOperationExecuted: false,
      progressBarTimer: null,
      timerProgress: null,
      timer1: null,
      timer2: null,
      isShowProgress: true,
      showPixelList: false,
      showScreenList: false,
      isToEnterTheGame: false,
      receiveData: [], // 云挂机游戏
      pixelClarityList: [
        {
          id: 1,
          title: this.$t('自动'),
          value: 'hdr',
          logoUrl: require('@/assets/images/cloudHangup/hdr-logo.png'),
        },
        {
          id: 2,
          title: this.$t('流畅'),
          value: 'lc',
          logoUrl: require('@/assets/images/cloudHangup/lc-logo.png'),
        },
        {
          id: 3,
          title: this.$t('标清'),
          value: 'bq',
          logoUrl: require('@/assets/images/cloudHangup/bq-logo.png'),
        },
        {
          id: 4,
          title: this.$t('高清'),
          value: 'hd',
          logoUrl: require('@/assets/images/cloudHangup/hd-logo.png'),
        },
      ],
      screenList: [
        {
          id: 1,
          title: this.$t('自动'),
        },
        {
          id: 2,
          title: this.$t('全屏'),
        },
      ],
      checkScreenItem: {
        id: 1,
        title: this.$t('自动'),
      },

      checkPixelClarityItem: {
        // 当前清晰度
        id: 4,
        title: this.$t('高清'),
        value: 'hd',
        logoUrl: require('@/assets/images/cloudHangup/hd-logo.png'),
      },
      cloudRunInformation: {
        netDelay: '',
      },
      loading_tip: '',
      loadingTip: '',
      cloudInfo: {
        ask_times: 0, // 轮询次数
        error_msg: '', // 安装失败提示
        ask_interval: 1, // 轮询间隔时间 单位：秒
        timeout_tip: '', // 连接超时文案
        loading_text: '游戏加载中...', // 加载提示
        is_reset_progress: true, // 是否重置进度条
        loading_end: '', // 安装结束提示词
        loading_config_list: [], //安装配置信息
      },
      ask_interval_sum: 0,
      loading_config_list_number: 0, //安装配置信息下标,
      times: 1, //times,
      totalProgress: 99, //进度条总进度,
      progressIncrement: 0, //进度增量 * 1000,
      finishConnect: false, // 网络断开重新连接
      errPopupMsg: this.$t('启动失败,请稍后重试!'),
      errCode: 0,
      cloudDeviceInfo: {}, // 云设备信息
      MEDIASDK: null, //云设备实例
      videoQuality: '超清', // 画质
      msgTransmissionInfo: {}, //消息透传文本
    };
  },
  created() {
    try {
      if (window.rtc_sdk && window.rtc_sdk.MediaSdk) {
        const MediaSdk = window.rtc_sdk.MediaSdk;
        this.MEDIASDK = new MediaSdk();
      } else {
        // 检查当前云平台类型并尝试加载
        if (this.cloud_type == 0) {
          // 加载臂云SDK
          const sdkUrl = sdkUrlList[this.cloud_type];
          const script = document.createElement('script');
          script.src = sdkUrl;
          script.setAttribute('data-sdk-type', this.cloud_type);
          document.head.appendChild(script);
        }
        if (this.cloud_type == 2) {
          // 加载臂云SDK
          const sdkUrl = sdkUrlList[this.cloud_type];
          const script = document.createElement('script');
          script.src = sdkUrl;
          script.setAttribute('data-sdk-type', this.cloud_type);
          document.head.appendChild(script);

          script.onload = () => {
            if (window.rtc_sdk && window.rtc_sdk.MediaSdk) {
              const MediaSdk = window.rtc_sdk.MediaSdk;
              this.MEDIASDK = new MediaSdk();
            }
          };
        }
      }
    } catch (error) {
      // console.error('初始化MediaSdk失败:', error);
    }
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('html')[0].style.background = '#fff';
    next();
  },
  beforeRouteEnter(to, from, next) {
    console.log('beforeRouteEnter:', from);
    next(vm => {
      const fromRouteInfo = {
        path: from.path,
        params: from.params,
      };
      sessionStorage.setItem('previousRoute', JSON.stringify(fromRouteInfo));
      // sdk中新增了code 跳转页面 这边也需要加
      // if (
      //   from.fullPath === '/platform_coin' ||
      //   from.fullPath === '/savings_card' ||
      //   from.fullPath === '/gold_coin_center' ||
      //   from.fullPath === '/clock_in' ||
      //   from.fullPath === '/turn_table' ||
      //   from.fullPath === '/gold_coin_exchange' ||
      //   from.fullPath === '/bind_we_chat' ||
      //   from.fullPath === '/svip'
      // ) {
      //   vm.isFromBoxIn = true;
      // }
      if (
        from.fullPath === '/cloud_hangup' ||
        from.name == 'UpDetail' ||
        from.fullPath.startsWith('/external_game_detail') ||
        from.name == 'MyGame'
      ) {
        vm.reload = true;
        vm.isToEnterTheGame = false;
      } else {
        vm.reload = false;
      }
    });
    next();
  },
  activated() {
    // if (this.isFromBoxIn) {
    //   this.isToEnterTheGame = true;
    //   this.$nextTick(() => {
    //     if (this.cloud_type == 0) {
    //       window.BgsSdk.webrtcRePlay();
    //     }
    //     document.getElementsByTagName('html')[0].style.background = '#000';
    //   });
    //   return;
    // }
    document.getElementsByTagName('html')[0].style.background = '#000';
    if (this.cloud_type == 0 && !this.reload) {
      if (window.BgsSdk) {
        window.BgsSdk.webrtcRePlay();
      } else {
        const sdkUrl = sdkUrlList[this.cloud_type];
        const script = document.createElement('script');
        script.src = sdkUrl;
        script.setAttribute('data-sdk-type', this.cloud_type);
        document.head.appendChild(script);
        script.onload = () => {
          window.BgsSdk.webrtcRePlay();
        };
      }
      return false;
    }
    if (this.cloud_type == 2 && !this.reload) {
      this.$toast.loading({
        duration: 0,
        message: this.$t('游戏重连中'),
        className: this.cloudRotation ? 'portrait' : '',
      });
      // 重新初始化视频流
      if (this.MEDIASDK && this.isToEnterTheGame) {
        // 先断开连接
        this.MEDIASDK.disconnect();

        // 重新初始化视频流
        this.byCloudStartPhone();
      }
      return false;
    }

    this.times = 0;
    this.cloudRotation = 0;
    this.customX = 31;
    this.customY = 40;
    this.failurePromptPopupShow = false; // 弹窗关闭
    this.reloadPopupShow = false; // 弹窗关闭
    this.isTimeOut = false;
    this.onHookPopupShow = false; // 弹窗关闭
    this.isDeviceWidgets = false; //隐藏小挂件
    this.progress = 0; // 进度条置空
    this.showScreenList = false;
    if (this.reload) {
      const { checkCloudDeviceItem, receiveData } = this.$route.params;
      if (checkCloudDeviceItem) {
        this.checkCloudDeviceItem = checkCloudDeviceItem;
      }
      if (receiveData) {
        this.receiveData = receiveData;
        this.openProgressBar(this.receiveData);
        return;
      }
      if (this.receiveDataInfo) {
        this.openProgressBar(this.receiveDataInfo);
      }
      if (this.checkCloudDeviceItemInfo) {
        this.checkCloudDeviceItem = this.checkCloudDeviceItemInfo;
      }
    }
  },
  deactivated() {
    document.getElementsByTagName('html')[0].style.background = '#fff';
    // this.isFromBoxIn = false;
    this.onHookPopupShow = false;
    this.clearAllTimers();
  },
  mounted() {
    document.getElementsByTagName('html')[0].style.background = '#000';
    // 监听网络状态
    this.checkNetworkStatus();
    this.getApiCloudMountedListInfo();
    if (!this.reload) {
      const { checkCloudDeviceItem, receiveData } = this.$route.params;

      if (checkCloudDeviceItem) {
        this.checkCloudDeviceItem = checkCloudDeviceItem;
      }
      if (receiveData) {
        this.receiveData = receiveData;
        this.openProgressBar(this.receiveData);
        return;
      }
      if (this.checkCloudDeviceItemInfo) {
        this.checkCloudDeviceItem = this.checkCloudDeviceItemInfo;
      }
      if (this.receiveDataInfo) {
        this.receiveData = this.receiveDataInfo;
        this.openProgressBar(this.receiveDataInfo);
      }
    }
  },
  beforeDestroy() {
    this.clearAllTimers();
  },
  methods: {
    handleActionCode,
    async getApiCloudMountedListInfo() {
      const res = await ApiCloudList();
      this.loadingTip = res.data.loading_tip;
    },
    handleTouchStart(event) {
      const deviceWidgetsElement = document.getElementById('device_widgets');
      if (
        this.isDeviceWidgets &&
        !deviceWidgetsElement.contains(event.target)
      ) {
        this.isDeviceWidgets = false;
        this.showScreenList = false;
      }
    },
    // 打开进度条 安装
    async openProgressBar(data) {
      this.clearAllTimers(); // 先清理所有定时器，确保干净的开始
      this.cloudRotation = 0;
      this.customX = 31;
      this.customY = 40;
      this.onHookGameInfo = data;
      this.isShowProgress = true;
      this.progress = 0;
      this.loading_config_list_number = 0;
      await this.executeApiCloudMountedLoadPage(data);
    },
    // 执行云挂机加载页面
    async executeApiCloudMountedLoadPage(data) {
      try {
        const res = await ApiCloudLoadPage({
          equip_id: this.checkCloudDeviceItem.id,
          app_id: data.id,
          times: this.times,
        });

        this.errMsg = res.data.error_msg;

        if (this.receiveData?.cloud_status == 4 && res.data?.info) {
          this.isShowHomeBtn = res.data.is_show_home_btn;
          this.startProgressTo99(res.data.info);
          return;
        }
        if (
          res.data?.info?.expire_code === 1 ||
          res.data?.loading_config_list == undefined
        ) {
          this.isShowHomeBtn = res.data.is_show_home_btn;
          this.startProgressTo99(res.data.info);
        } else {
          console.log(
            '-------loading_config_list_number---------:' +
              this.loading_config_list_number,
          );
          console.log('-------times---------:' + this.times);
          this.times++;
          this.cloudInfo = res.data;
          if (
            res.data.loading_config_list &&
            res.data.loading_config_list.length > 0
          ) {
            this.cloudInfo.loading_text =
              res.data.loading_config_list[
                this.loading_config_list_number
              ].loading_text;
            this.loading_tip =
              res.data.loading_config_list[
                this.loading_config_list_number
              ].loading_tip;
          }
          this.handleAskInterval(res.data, data);
        }
      } catch (err) {
        this.progress = 99;
        if (!navigator.onLine) {
          this.errPopupMsg = this.$t('网络已断开');
          this.failurePromptPopupShow = true;
        }
      }
    },
    // 进度条拉满 进入游戏
    startProgressTo99(info) {
      // 清除视频元素上的旧样式
      const videoContainer = document.getElementById('cloudVideoContainer');
      if (videoContainer) {
        const oldVideo = videoContainer.querySelector('video');
        if (oldVideo) {
          oldVideo.classList.remove('isContain', 'isFill');
          oldVideo.style.transform = '';
          oldVideo.style.height = '';
        }
      }

      const increment = 1;
      const interval = 20;
      this.progressBarTimer = setInterval(() => {
        this.progress = Math.min(this.progress + increment, 99);
        if (this.progress >= 99) {
          clearInterval(this.progressBarTimer);
          this.uuid = info.uid;
          this.serverToken = info.server_token;
          this.cloudDeviceInfo = info;
          this.startPhone();
        }
      }, interval);
    },
    // 处理询问间隔
    handleAskInterval(askData, data) {
      this.totalAskIntervals = askData.loading_config_list.reduce(
        (sum, item) => sum + item.ask_interval,
        0,
      );
      this.progressIncrement = 99 / this.totalAskIntervals; // 将进度条的总增量分摊到每个 ask_interval
      this.updateProgress(askData, data);
    },
    // 更新进度条 定时器加载游戏
    updateProgress(askData, data) {
      clearInterval(this.timerProgress);
      clearInterval(this.timer1);
      if (
        this.loading_config_list_number < askData.loading_config_list.length
      ) {
        this.cloudInfo.loading_text =
          askData.loading_config_list[
            this.loading_config_list_number
          ].loading_text;
        this.loading_tip =
          askData.loading_config_list[
            this.loading_config_list_number
          ].loading_tip;
        const currentConfig =
          askData.loading_config_list[this.loading_config_list_number];
        const intervalCount = currentConfig.ask_interval;
        let intervalProgress = 0;

        this.timerProgress = setInterval(() => {
          if (this.progress >= 99) {
            clearInterval(this.timerProgress);
            return;
          }
          this.progress = Number(
            Math.min(this.progress + this.progressIncrement, 99).toFixed(2),
          );
          intervalProgress++;
          if (intervalProgress >= intervalCount) {
            clearInterval(this.timerProgress);
          }
        }, 1000); // 每秒钟更新一次进度

        this.timer1 = setTimeout(async () => {
          try {
            const res = await ApiCloudLoadPage({
              equip_id: this.checkCloudDeviceItem.id,
              app_id: data.id,
              times: this.times,
            });
            this.errMsg = res.data.error_msg;
            if (
              res.data?.info?.expire_code === 1 ||
              res.data?.loading_config_list == undefined
            ) {
              clearTimeout(this.timer1);
              clearInterval(this.timerProgress);
              this.progress = 99;
              this.uuid = res.data.info.uid;
              this.serverToken = res.data.info.server_token;
              this.cloudDeviceInfo = res.data.info;
              this.isShowHomeBtn = res.data.is_show_home_btn;
              this.isEnterGame = true;
              this.startPhone();
            } else {
              console.log(
                '-------loading_config_list_number---------:' +
                  this.loading_config_list_number,
              );
              console.log('-------times---------:' + this.times);
              this.loading_config_list_number++;
              this.times++;
              this.updateProgress(res.data, data);
            }
          } catch (err) {
            // console.error('Error during API call:', err);
            if (!navigator.onLine) {
              this.errPopupMsg = this.$t('网络已断开');
              this.failurePromptPopupShow = true;
            }
          }
        }, intervalCount * 1000);
      } else {
        this.clearAllTimers();
        this.handleLoadingComplete(askData);
      }
    },
    // 加载游戏失败弹窗
    handleLoadingComplete(askData) {
      this.isEnterGame = false;
      this.errMsg = askData.error_msg;
      this.type = 2;
      this.onHookPopupShow = true;
    },
    // 清除定时器
    clearAllTimers() {
      try {
        if (this.progressBarTimer) {
          clearInterval(this.progressBarTimer);
          this.progressBarTimer = null;
        }
        if (this.timerProgress) {
          clearInterval(this.timerProgress);
          this.timerProgress = null;
        }
        if (this.timer1) {
          clearTimeout(this.timer1);
          this.timer1 = null;
        }
        if (this.timer2) {
          clearTimeout(this.timer2);
          this.timer2 = null;
        }
      } catch (error) {
        // console.error('清除定时器出错:', error);
      }
    },
    onDragstop() {
      const left = this.$refs.drag.left;
      const top = this.$refs.drag.top;
      if (left == this.customX && top == this.customY) {
        this.isDeviceWidgets = !this.isDeviceWidgets;
      } else {
        this.customX = left;
        this.customY = top;
      }
    },
    resize(newRect) {
      this.width = newRect.width;
      this.height = newRect.height;
      this.top = newRect.top;
      this.left = newRect.left;
    },
    // 退出挂机页面的方法
    exitHangupPage() {
      this.stopPhone();
      this.cloudRotation = 0;
      this.customX = 31;
      this.customY = 40;
      this.failurePromptPopupShow = false; // 弹窗关闭
      this.reloadPopupShow = false; // 弹窗关闭
      this.isTimeOut = false;
      this.onHookPopupShow = false; // 弹窗关闭
      this.isToEnterTheGame = false; // 退出加载游戏
      this.isDeviceWidgets = false; //隐藏小挂件
      this.progress = 0; // 进度条置空
      this.checkScreenItem = {
        id: 1,
        title: this.$t('自动'),
      };
      const previousRoute = JSON.parse(sessionStorage.getItem('previousRoute'));
      if (previousRoute) {
        if (previousRoute.path.startsWith('/up_detail/')) {
          // 解析路径中的 ID
          const id = previousRoute.params.id;
          this.$router.replace({
            name: 'UpDetail',
            params: {
              id,
            },
            query: { replace: true },
          });
        } else {
          this.$router.replace({
            name: 'CloudHangup',
            query: { replace: true },
          });
        }
      } else {
        // 如果没有之前的路由信息，则回到首页或其他默认页面
        this.$router.replace({
          name: 'CloudHangup',
          query: { replace: true },
        });
      }
    },
    clickBsLoading() {
      this.exitHangupPage();
    },
    backCloudHangup() {
      this.type = 0;
      const onHookPopupHide = JSON.parse(
        localStorage.getItem('ON_HOOK_POPUP_HIDE'),
      );
      if (!onHookPopupHide) {
        this.onHookPopupShow = true;
        return;
      }
      this.$nextTick(() => {
        this.exitHangupPage();
      });
    },

    returnToGameBox() {
      this.exitHangupPage();
    },
    reloadCancel() {
      this.exitHangupPage();
    },
    reloadConfirm() {
      this.reloadPopupShow = false;
      this.$toast.loading({
        duration: 0,
        message: this.$t('重连中,请稍后'),
      });
      this.openProgressBar(this.receiveData);
    },
    receiveCancelData(value, type) {
      if (value && type === 1) {
        this.exitHangupPage();
      }
      if (value && type === 2) {
        this.exitHangupPage();
      }
    },

    receiveConfirmData(value, type) {
      this.onHookPopupShow = false;
      if (value && type === 0) {
        this.exitHangupPage();
      }
      if (value && type === 1 && !this.isEnterGame) {
        this.isShowProgress = false;
        this.openProgressBar(this.receiveData);
      }
      if (value && type === 1 && this.isEnterGame) {
        this.startPhone();
      }
      if (value && type === 2) {
        this.openProgressBar(this.receiveData);
      }
    },
    selectClarity(item) {
      this.$toast.loading(this.$t('加载中...'));
      this.checkPixelClarityItem = item;
    },
    // 刷新游戏
    refreshTheGame() {
      this.$toast.loading({
        message: this.$t('刷新中...'),
        className: this.cloudRotation ? 'portrait' : '',
      });
      ApiCloudAppOperate({
        equip_id: this.checkCloudDeviceItem.id,
        game_package_name: this.receiveData.package_name,
      });
    },
    // 回到桌面
    backToTheDesktop() {
      this.sendKeyCommand(3);
    },
    // 通用按键发送方法
    sendKeyCommand(keyValue) {
      if (this.cloud_type == 0) {
        // 百度云
        switch (keyValue) {
          case 3: // HOME键
            window.BgsSdk.sendCommand(redfinger.KEY_TYPE.KEY_HOMEPAGE);
            break;
          default:
        }
      } else {
        // 臂云
        if (this.MEDIASDK) {
          this.MEDIASDK.sendKey(keyValue);
        }
      }
    },
    selectScreen(item) {
      this.checkScreenItem = item;
      let phoneVideo;
      if (this.cloud_type == 0) {
        phoneVideo = document.getElementById('phoneVideo');
        if (this.checkScreenItem.id !== 1) {
          // 设置为全屏显示
          phoneVideo.classList.add('isFullScreen');
        } else {
          // 如果id为1，重置样式 恢复默认样式
          phoneVideo.classList.remove('isFullScreen');
        }
      }
      if (this.cloud_type == 2) {
        phoneVideo = document
          .getElementById('cloudVideoContainer')
          .querySelector('video');

        // 横屏时总是使用isContain
        if (this.cloudRotation === 1) {
          if (this.checkScreenItem.id !== 1) {
            phoneVideo.classList.add('isContainType');
            phoneVideo.classList.remove('isFill');
            phoneVideo.classList.remove('isContain');
          } else {
            phoneVideo.classList.add('isContain');
            phoneVideo.classList.remove('isFill');
            phoneVideo.classList.remove('isContainType');
          }
        } else {
          // 竖屏时根据模式决定
          if (this.checkScreenItem.id !== 1) {
            // 设置为全屏显示
            phoneVideo.classList.add('isFill');
            phoneVideo.classList.remove('isContain');
            phoneVideo.classList.remove('isContainType');
          } else {
            // 自动模式
            phoneVideo.classList.add('isContain');
            phoneVideo.classList.remove('isFill');
            phoneVideo.classList.remove('isContainType');
          }
        }

        // 应用计算的样式
        this.applyVideoStyle(phoneVideo);
      }
    },
    getServerToken() {
      // 开始链接
      window.BgsSdk.startPhone(this.serverToken);
    },
    startPhone() {
      if (this.cloud_type == 0) {
        this.baiduCloudStartPhone();
      } else {
        this.byCloudStartPhone();
      }
    },
    baiduCloudStartPhone() {
      let that = this;
      // 初始化参数
      var params = {
        viewId: 'phoneBox', // 必填
        uuid: that.uuid, // 必填
        fps: 60,
        callbacks: {
          // 初始化失败回调
          onInitFail: function (code, msg) {
            console.log('on init code:' + code + ' msg:' + msg);
          },
          onInitSuccess: function () {
            that.$toast.clear();
            console.log('on init success:');
          },
          // 链接成功回调
          onConnectSuccess: function () {
            that.isToEnterTheGame = true;
            // that.isFromBoxIn = false;
            that.$toast.clear();
            console.log('on success');
          },
          // 链接失败回调
          onConnectFail: function (code, msg) {
            console.log('on fail code:' + code + msg);
            that.errCode = code;
            that.$toast.clear();
            that.stopPhone();
            that.isTimeOut = false;
            that.reloadPopupShow = true;
          },
          // 播放出现异常时触发
          onErrorMessage: function (code) {
            console.log('onErrorMessage:' + code);
            that.errCode = code;
            that.stopPhone();
            that.isTimeOut = false;
            that.reloadPopupShow = true;
          },
          // 结束回调
          onStoped: async function () {
            if (that.type == 0 || that.type == 1) {
              return;
            }
            that.stopPhone();
            that.isTimeOut = false;
            that.reloadPopupShow = true;
            // 已断开云手机
            console.log('on stoped');
          },
          // 当前运行信息回调
          onRunInformation: function (type, info) {
            that.cloudRunInformation = info;
          },
          // 投屏分辨率发生变化时触发
          onChangeResolution: function (width, height) {
            console.log('分辨率:');
            console.log(width, height);
          },
          // 当横竖屏发生变化时触发
          onRotation: function (code, result) {
            that.cloudRotation = result;
            that.$toast.clear();
            if (result) {
              that.phoneBoxClientHeight = that.$refs.phoneBox.clientHeight;
              that.phoneBoxClientWidth = that.$refs.phoneBox.clientWidth;
              that.customX = that.phoneBoxClientWidth - 40 - 46;
              that.customY = 0;
            } else {
              that.customX = 31;
              that.customY = 40;
            }
          },
          // 当收到云端app透传的消息时的触发
          onTransparentMsg: function (type, msg, service) {
            that.onTransparentMsg(type, JSON.parse(msg), service);
          },
          // 当透传给云端的消息失败时触发
          onTransparentMsgFailed: function (type, msg, service) {
            console.log('onTransparentMsgFailed:' + type, msg, service);
          },
          // onUnisibilityTime: function(duration) {
          //   console.log("-onUnisibilityTime-")
          //   console.log(duration)
          // },
          onReconnect: async function (cnt) {
            that.$toast.loading({
              duration: 0,
              message: that.$t('重连中,请稍后'),
              className: that.cloudRotation ? 'portrait' : '',
            });
            if (!that.finishConnect) {
              that.reconnectTheScreen();
            } else {
              return;
            }
          },
        },
      };
      // 开始初始化
      let initRet = window.BgsSdk.initPhone(params);
      if (initRet) {
        // 获取serverToken
        this.getServerToken();
      }
    },
    byCloudStartPhone() {
      this.isToEnterTheGame = true;
      this.$nextTick(() => {
        setTimeout(() => {
          // 获取容器实际尺寸
          let containerWidth = this.$refs.phoneBox
            ? this.$refs.phoneBox.clientWidth
            : window.innerWidth;
          // let containerHeight = this.$refs.phoneBox
          //   ? this.$refs.phoneBox.clientHeight
          //   : window.innerHeight;
          let containerHeight = Math.round(containerWidth * (16 / 9));
          //配置初始化参数
          const options = {
            mount: document.getElementById('cloudVideoContainer'), // 挂载到专用容器
            displaySize: {
              width: containerWidth, // 使用容器实际宽度
              height: containerHeight, // 使用容器实际高度
            },
            topic: this.cloudDeviceInfo?.instance_code, // 实例SN号 必填
            url: this.cloudDeviceInfo?.signal_wss_address + '/nats', //信令服务地址  必填 wss
            ICEServerUrl: [
              {
                CMNET: `${this.cloudDeviceInfo?.ice_server}`,
              },
              {
                'CHINANET-GD': `${this.cloudDeviceInfo?.ice_server}`,
              },
              {
                'UNICOM-GD': `${this.cloudDeviceInfo?.ice_server}`,
              },
            ], // 三网地址  必填
            authToken: this.cloudDeviceInfo?.stream_token, //拉流鉴权 token 必填
            forwardServerAddress:
              `wss://` + this.cloudDeviceInfo?.forwardserver_wss_address, // 转发服务器地址wss
            ip: this.cloudDeviceInfo?.ip, // 实例ip
            controlToken: this.cloudDeviceInfo?.control_token, // 代理控制token
            width: 720, // 推流视频宽度 使用容器实际宽度
            height: 1280, // 推流视频高度 使用容器实际高度
            cardWidth: '0', // 云机系统分辨率 宽 必填 （默认填0不处理）
            cardHeight: '0', // 云机系统分辨率 高 必填 （默认填0不处理）
            cardDensity: '0', // 云机系统显示 密度 必填 （默认填0不处理）
            quality: this.videoQuality, //画质 必填
            fps: 60, //帧率 必填
            videoCodec: 'H264 High', // 编码方式 必填 'H264 High' || 'VP8'
            videoCodecMethod: 'false', // 视频格式 硬编true | 软编false
            isMuted: false, // 是否开启静音  默认false
            isAllowedOpenCamera: false, // 是否允许开启摄像头权限  必填
            sendFollow: true, // 是否允许主控转发文本到实例
            scaleType: 'fill', //cover:参按比例缩放并裁剪 fill:铺满 contain:按比例显示
            callback: this.statusCallBack,
          };
          console.log('---------------初始化参数----------------- ');
          console.log(options);

          //开始初始化
          this.MEDIASDK.RtcEngine(options);

          const eventHandlers = {
            DECODING_STATUS: this.handleDecodingStatus,
            CONNECT_SUCCESS: r => {
              //  连接成功
              this.$toast.clear();
              console.log('Player => CONNECT_SUCCESS', r);
              switch (r.code) {
                case 10010:
                  // 开启消息订阅
                  this.MEDIASDK?.openSubscribeTopic({
                    ...this.cloudBaseConfig,
                    topic: ['message_transmission', 'screen_orientation'],
                    listener: this.OperatorListener,
                  });

                  break;
                case 1001:
                  break;
                default:
                  break;
              }
            },
            RECEIVE_STREAM_STATUS: r => {
              // 开启/暂停 推流
              console.log('Player => RECEIVE_STREAM_STATUS', r);
            },
            CONNECT_ERROR: r => {
              // 连接异常
              console.log('Player => CONNECT_ERROR', r);
              this.errCode = r.code;
              this.stopPhone();
              this.isTimeOut = false;
              this.reloadPopupShow = true;
            },
            RECEIVE_MESSAGETRANSMISSION_STATUS: r => {
              // 文本透传
              console.log('Player => RECEIVE_MESSAGETRANSMISSION_STATUS', r);
            },
            RECEIVE_RESOLUTION: r => {
              // 分辨率大小发生改变
              console.log('Player => RECEIVE_RESOLUTION', r);
              // 0竖屏 1横屏
              if (r.width > r.height) {
                this.cloudRotation = 1;
              } else {
                this.cloudRotation = 0;
              }

              this.$toast.clear();
              if (this.cloudRotation) {
                this.phoneBoxClientHeight = this.$refs.phoneBox.clientHeight;
                this.phoneBoxClientWidth = this.$refs.phoneBox.clientWidth;
                this.customX = this.phoneBoxClientWidth - 40 - 46;
                this.customY = 0;
              } else {
                this.customX = 31;
                this.customY = 40;
              }

              // 应用计算的样式
              let phoneVideo = document
                .getElementById('cloudVideoContainer')
                .querySelector('video');
              if (phoneVideo) {
                // 横屏时总是使用isContain
                if (this.cloudRotation === 1) {
                  if (this.checkScreenItem.id !== 1) {
                    phoneVideo.classList.add('isContainType');
                    phoneVideo.classList.remove('isFill');
                    phoneVideo.classList.remove('isContain');
                  } else {
                    phoneVideo.classList.add('isContain');
                    phoneVideo.classList.remove('isFill');
                    phoneVideo.classList.remove('isContainType');
                  }
                } else {
                  // 竖屏时根据模式决定
                  if (this.checkScreenItem.id !== 1) {
                    // 全屏模式
                    phoneVideo.classList.add('isFill');
                    phoneVideo.classList.remove('isContain');
                    phoneVideo.classList.remove('isContainType');
                  } else {
                    // 自动模式
                    phoneVideo.classList.add('isContain');
                    phoneVideo.classList.remove('isFill');
                    phoneVideo.classList.remove('isContainType');
                  }
                }

                this.applyVideoStyle(phoneVideo);
              }
            },
            keyboardFeedbackBean: r => {
              // 接收到的键盘透传响应
              console.log('Player => keyboardFeedbackBean', r);
            },
            NETWORK_STATS: r => {
              // 网络连接统计信息
              this.cloudRunInformation.netDelay = r.currentRoundTripTime;
              // console.log('Player => NETWORK_STATS', r);
            },
          };

          Object.entries(eventHandlers).forEach(([event, handler]) => {
            this.MEDIASDK.on(event, handler);
          });
        }, 100);
      });
    },
    // 处理解码状态事件
    handleDecodingStatus(r) {
      console.log('---------------首帧解码时间-----------------', r);
      // 只处理首帧解码状态
      if (r.type === 'FirstDecoding_status' && r.code == 1007) {
        if (this.MEDIASDK) {
          let phoneVideo = document
            .getElementById('cloudVideoContainer')
            .querySelector('video');
          // 使用计算属性设置样式
          this.applyVideoStyle(phoneVideo);

          this.setupCloudConnections();
        }
      }
    },

    // 应用视频样式的辅助方法
    applyVideoStyle(videoElement) {
      if (!videoElement) return;

      const style = this.videoStyle;
      Object.keys(style).forEach(key => {
        videoElement.style[key] = style[key];
      });
    },
    // 设置云连接相关的订阅和消息透传
    setupCloudConnections() {
      // 准备消息透传内容
      this.msgTransmissionInfo = {
        msg_type: '104',
        msg_content: {
          name: 'cloud_gamebox_info',
          timestamp: new Date().getTime(),
          value: {
            from: `${BASEPARAMS.from}`,
          },
        },
      };

      // 消息透传（向应用发送文本消息）
      this.MEDIASDK?.messageTransmissionApp({
        ...this.cloudBaseConfig,
        packageName: 'com.xm.sqss.cloudmsg',
        message: JSON.stringify(this.msgTransmissionInfo),
        listener: this.OperatorListener,
      });

      // 设置云机键盘 (1真机键盘 2云机键盘)
      this.MEDIASDK?.setCloudKeyboard(2);

      this.MEDIASDK?.sysGetScreenOrientation({
        ...this.cloudBaseConfig,
        listener: this.OperatorListener,
      });
    },
    // 回调
    OperatorListener(e) {
      console.log('-----------OperatorListener-----------');
      console.log(e);

      switch (e.type) {
        case 'publish_topic':
          // 监听透传
          if (e.content.topic == 'message_transmission') {
            const message = JSON.parse(e.content.data.message);
            let { msg_type, msg_content, msg_sender } = message;
            if (msg_content) {
              msg_content = JSON.parse(msg_content);
            }

            this.onTransparentMsg(msg_type, msg_content, msg_sender);
          }
          break;
        case 'heartbeat':
          break;
        case 'subscribe_topic':
          break;
        case 'app_message_transmission':
          break;
        case 'sys_get_screen_orientation':
          // console.log(e.content.orientation);
          // // 0竖屏 1横屏
          // this.cloudRotation = e.content.orientation;
          // this.$toast.clear();
          // if (e.content.orientation) {
          //   this.phoneBoxClientHeight = this.$refs.phoneBox.clientHeight;
          //   this.phoneBoxClientWidth = this.$refs.phoneBox.clientWidth;
          //   this.customX = this.phoneBoxClientWidth - 40 - 46;
          //   this.customY = 0;
          // } else {
          //   this.customX = 0;
          //   this.customY = 40;
          // }
          break;
        default:
          break;
      }
    },
    onTransparentMsg(type, msg, service) {
      switch (parseInt(type)) {
        case 90:
          // 刷新
          ApiCloudAppOperate({
            equip_id: this.checkCloudDeviceItem.id,
            game_package_name: this.receiveData.package_name,
          });
          break;
        case 91:
          // 采集游戏快速实名 身份证号和姓名只有未成年才需要传
          ApiUserInfoEx().then(res => {
            let params = {
              auth_status: Number(res.data.auth_status),
              msg_receive_action: msg.action,
              msg_package_name: msg.packageName,
              ic: res.data.id_card,
              rn: res.data.real_name,
            };
            if (res.data.is_adult == 1) {
              delete params.ic;
              delete params.rn;
              if (this.cloud_type == 0) {
                window.BgsSdk.sendTransparentMsg(
                  type,
                  JSON.stringify(params),
                  'com.xm.sqss.cloudmsg/.SqssCloudService',
                );
              }
              if (this.cloud_type == 2) {
                this.msgTransmissionInfo = {
                  msg_type: '91',
                  msg_content: {
                    ...params,
                  },
                };
                this.MEDIASDK?.messageTransmissionApp({
                  ...this.cloudBaseConfig,
                  packageName: 'com.xm.sqss.cloudmsg',
                  message: JSON.stringify(this.msgTransmissionInfo),
                  listener: this.OperatorListener,
                });
              }
            } else if (res.data.is_adult == 0) {
              if (this.cloud_type == 0) {
                window.BgsSdk.sendTransparentMsg(
                  type,
                  JSON.stringify(params),
                  'com.xm.sqss.cloudmsg/.SqssCloudService',
                );
              }
              if (this.cloud_type == 2) {
                this.msgTransmissionInfo = {
                  msg_type: '91',
                  msg_content: {
                    ...params,
                  },
                };
                this.MEDIASDK?.messageTransmissionApp({
                  ...this.cloudBaseConfig,
                  packageName: 'com.xm.sqss.cloudmsg',
                  message: JSON.stringify(this.msgTransmissionInfo),
                  listener: this.OperatorListener,
                });
              }
            }
          });
          break;
        case 100:
          if (msg.action_code == 59) {
            BOX_openInBrowser({ h5_url: msg.web_url }, { url: msg.web_url });
            return false;
          }

          // 通过action_code跳转指定页面
          switch (msg.h5_action_code || msg.action_code) {
            case 17: //辅助空间
              this.$toast({
                message: this.$t('暂无功能'),
                className: this.cloudRotation ? 'portrait' : '',
              });
              break;
            default:
              // 部分安卓数据未配置web_page 只配置了web_url 手动转name(web_url后缀地址和路由名不一样就有问题)
              if (
                (msg.web_url && msg.web_url.indexOf('game.3733.com') > -1) ||
                msg.h5_action_code
              ) {
                const name = formatHashPathToRouteName(msg.web_url);
                BOX_openInNewWindow(
                  {
                    name,
                  },
                  {
                    url: msg.web_url,
                  },
                );
                return false;
              }
              this.handleActionCode(msg);
              if (msg.web_url == 'Welfare') {
                this.MEDIASDK.disconnect();
              }
              break;
          }
          break;
        case 101:
          let params = {
            token: this.userInfo.token,
            msg_receive_action: service,
          };
          // 云手机游戏授权登录
          if (this.cloud_type == 0) {
            window.BgsSdk.sendTransparentMsg(
              type,
              JSON.stringify(params),
              'com.xm.sqss.cloudmsg/.SqssCloudService',
            );
          }
          if (this.cloud_type == 2) {
            this.msgTransmissionInfo = {
              msg_type: '101',
              msg_content: {
                ...params,
              },
            };
            this.MEDIASDK?.messageTransmissionApp({
              ...this.cloudBaseConfig,
              packageName: 'com.xm.sqss.cloudmsg',
              message: JSON.stringify(this.msgTransmissionInfo),
              listener: this.OperatorListener,
            });
          }
          break;
        case 102:
          // 关闭云手机画面
          this.exitHangupPage();
          break;
        case 103:
          this.$toast.loading(this.$t('请稍等...'));
          // 更新云手机游戏
          ApiCloudUpdateGameVer({
            equip_id: this.checkCloudDeviceItem.id,
            app_id: this.receiveData.id,
            msg_package_name: msg.package_name || '',
            msg_apk_version_code: msg.version_code || '',
            msg_apk_signature: msg.signature || '',
          });
          setTimeout(() => {
            this.$toast.clear();
            this.exitHangupPage();
          }, 1000);
          break;
        default:
      }
    },
    // 初始化回调
    statusCallBack(data) {
      // console.log('初始化回调:' + data);
      switch (data.type) {
        case 'screenChange': //安卓卡竖屏
          break;
        case 'StreamStates': //流状态
          break;
        case 'DataChannelMessage': //通道信息
          break;
        default:
          break;
      }
    },
    // 重连画面
    async reconnectTheScreen() {
      try {
        const res = await ApiCloudLoadPage({
          equip_id: this.checkCloudDeviceItem.id,
          app_id: this.receiveData.id,
          times: this.times,
        });
        if (
          res.data?.info?.expire_code === 1 ||
          res.data?.loading_config_list == undefined
        ) {
          this.finishConnect = true;
          window.BgsSdk?.stopPhone();
          setTimeout(() => {
            this.uuid = res.data.info.uid;
            this.serverToken = res.data.info.server_token;
            this.cloudDeviceInfo = res.data.info;
            this.isShowHomeBtn = res.data.is_show_home_btn;
            this.isDeviceWidgets = false;
            this.startPhone();
          }, 200);
        }
        this.finishConnect = false;
      } catch {
        this.finishConnect = false;
      }
    },
    checkNetworkStatus() {
      window.addEventListener('offline', () => {
        document.getElementsByTagName('html')[0].style.background = '#fff';
        this.stopPhone();
      });
    },
    stopPhone() {
      this.isToEnterTheGame = false;
      if (this.cloud_type == 0) {
        window.BgsSdk?.stopPhone();
      } else {
        // 关闭消息订阅
        this.MEDIASDK.unsubscribeTopic({
          ...this.cloudBaseConfig,

          listener: this.OperatorListener,
        });
        // 主动断开连接
        this.MEDIASDK.disconnect();

        // 清空臂云推流容器
        const container = document.getElementById('cloudVideoContainer');
        if (container) {
          // 清除视频元素上的样式
          const oldVideo = container.querySelector('video');
          if (oldVideo) {
            oldVideo.classList.remove('isContain', 'isFill');
            oldVideo.style.transform = '';
            oldVideo.style.height = '';
          }

          while (container.firstChild) {
            container.removeChild(container.firstChild);
          }
        }
      }
    },
  },
  computed: {
    ...mapGetters({
      receiveDataInfo: 'cloud_hangup/receiveData',
      checkCloudDeviceItemInfo: 'cloud_hangup/checkCloudDeviceItem',
      initData: 'system/initData',
      cloud_type: 'system/cloud_type',
    }),
    // 公共云设备配置信息
    cloudBaseConfig() {
      if (!this.cloudDeviceInfo || !this.MEDIASDK) return null;

      const { instance_code, forwardserver_wss_address, ip, control_token } =
        this.cloudDeviceInfo;
      const deviceId = this.MEDIASDK.deviceUUID();
      const wssUrl = `wss://${forwardserver_wss_address}`;

      return {
        instanceId: instance_code,
        url: wssUrl,
        ip,
        token: control_token,
        deviceId,
      };
    },
    // 根据屏幕方向和显示模式计算视频样式
    videoStyle() {
      // 根据当前的显示模式和旋转状态返回不同的样式
      const isAutoMode = this.checkScreenItem.id === 1;

      if (isAutoMode) {
        // 自动模式
        if (this.cloudRotation === 1) {
          // 横屏
          return {
            transform: 'translate(-50%, -50%) rotate(90deg)',
            height: '',
          };
        } else {
          // 竖屏
          return {
            transform: 'translate(-50%, -50%) rotate(0deg)',
            height: 'auto',
          };
        }
      } else {
        // 全屏模式
        if (this.cloudRotation === 1) {
          // 横屏全屏 - 现在与竖屏处理方式一致
          return {
            transform: 'translate(-50%, -50%) rotate(90deg)',
            height: '',
          };
        } else {
          // 竖屏
          return {
            transform: 'none',
            height: '100%',
          };
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
html,
body {
  height: 100%;
  max-width: 450px;
  margin: 0 auto;
}
.page {
  position: relative;
  .right-FAQ {
    background: url('~@/assets/images/cloudHangup/cloud_FAQ.png') no-repeat 0 0;
    background-size: 18 * @rem 18 * @rem;
    width: 18 * @rem;
    height: 18 * @rem;
  }
  .loading-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .phoneBox {
    // background-color: grey;
    overflow: hidden;
    width: 100%;
    height: 100%;
    margin-top: @safeAreaTopEnv;
    position: relative;
    box-sizing: border-box;
    // background: #000;
    .drag {
      z-index: 9 !important;
      &.portrait {
        transform: rotate(90deg);
      }
    }
    .device_widgets {
      z-index: 9 !important;
      position: absolute;
      top: 48 * @rem;

      width: 242 * @rem;
      height: 91 * @rem;
      display: flex;
      flex-direction: column;
      &.left-24 {
        left: 24 * @rem;
      }
      &.portrait {
        transform: rotate(90deg);
        margin-top: 45 * @rem;
        right: 0;
      }
      .device_widgets_title {
        height: 33 * @rem;
        background: #22272e;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 9 * @rem 12 * @rem;
        box-sizing: border-box;
        border-radius: 14 * @rem 14 * @rem 0 0;
        .title_name {
          height: 11 * @rem;
          font-size: 11 * @rem;
          color: rgba(255, 255, 255, 0.8);
          line-height: 11 * @rem;
          text-align: center;
          overflow: hidden;
          > span {
            color: #35b86a;
          }
        }
        .title_close {
          padding: 3 * @rem 7 * @rem;
          white-space: nowrap;
          box-sizing: border-box;
          height: 16 * @rem;
          background: rgba(255, 255, 255, 0.14);
          border-radius: 18 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          > span:first-of-type {
            height: 10 * @rem;
            font-size: 10 * @rem;
            color: rgba(255, 255, 255, 0.6);
            line-height: 10 * @rem;
            text-align: center;
          }
          > span:last-of-type {
            margin-left: 4 * @rem;
            width: 8 * @rem;
            height: 8 * @rem;
          }
        }
      }
      .device_widgets_nav {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;
        background: #43484c;
        border-radius: 0 0 14 * @rem 14 * @rem;
        .nav_item {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          position: relative;
          > span {
            width: 19 * @rem;
            height: 19 * @rem;
          }
          > div {
            margin-top: 6 * @rem;
            font-size: 11 * @rem;
          }
          &:nth-of-type(2) div {
            color: #35b86a;
          }
          &:not(:nth-of-type(2)) div {
            color: rgba(255, 255, 255, 0.8);
          }
          .select-screen-list {
            position: absolute;
            right: 0;
            top: 60 * @rem;
            width: 62 * @rem;
            z-index: 9;
            left: 50%;
            transform: translateX(-50%);
            height: 100 * @rem;
            .screen-list-content {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              top: 6 * @rem;
              box-shadow: 0 0 10 * @rem 0 rgba(0, 0, 0, 0.1);
              position: relative;
              &::before {
                content: '';
                width: 0;
                height: 0;
                border: 6px solid;
                border-color: transparent transparent rgba(34, 39, 46, 0.8)
                  transparent;
                position: absolute;
                top: -12px;
                left: 50%;
                transform: translateX(-50%);
              }
              .screen-list-title {
                position: relative;
                width: 62 * @rem;
                height: 46 * @rem;
                line-height: 46 * @rem;
                text-align: center;
                background: rgba(34, 39, 46, 0.8);
                box-shadow: 0 1 * @rem 3 * @rem 0 rgba(0, 0, 0, 0.21);
                font-size: 11 * @rem;
                color: rgba(255, 255, 255, 0.8);
                &:first-of-type {
                  border-top-left-radius: 10 * @rem 10 * @rem;
                  border-top-right-radius: 10 * @rem 10 * @rem;
                }
                &:last-of-type {
                  border-bottom-left-radius: 10 * @rem 10 * @rem;
                  border-bottom-right-radius: 10 * @rem 10 * @rem;
                }
                &:not(:first-of-type)::before {
                  content: '';
                  position: absolute;
                  left: 50%;
                  transform: translateX(-50%);
                  width: 40 * @rem;
                  height: 0;
                  border: 1 * @rem solid rgba(255, 255, 255, 0.4);
                }
                &.active {
                  background: #22ae6b;
                  color: #fff;
                  &::before {
                    display: none;
                  }
                  &.active + .screen-list-title::before {
                    display: none;
                  }
                }
              }
            }
          }
          .slide-fade-enter-active,
          .slide-fade-leave-active {
            transition: all 0.3s ease-out;
            overflow: hidden;
          }

          .slide-fade-enter,
          .slide-fade-leave-to {
            height: 0;
            opacity: 0;
          }
        }
      }
    }
    .loading-content {
      background-color: #fff;
      overflow: hidden;
      width: 100%;
      height: 100%;
      text-align: center;
      position: relative;
      .loading-game {
        position: absolute;
        top: 40%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        .loading-game-img {
          width: 125 * @rem;
          height: 125 * @rem;
          border-radius: 26 * @rem;
          overflow: hidden;
        }
        .loading-game-title {
          margin-top: 16 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          position: relative;
          .game-title-box {
            display: flex;
            align-items: center;
            .game-main-title {
              height: 14 * @rem;
              font-size: 14 * @rem;
              color: #111111;
              line-height: 14 * @rem;
              text-align: center;
              overflow: hidden;
              white-space: nowrap;
            }
            .game-subtitle {
              margin-left: 8 * @rem;
              max-width: 60 * @rem;
              box-sizing: border-box;
              padding: 2 * @rem 4 * @rem;
              height: 17 * @rem;
              background: #f5f5f6;
              border-radius: 4 * @rem;
              font-weight: 400;
              font-size: 10 * @rem;
              color: #808080;
              line-height: 17 * @rem;
              text-align: center;
              font-style: normal;
              text-transform: none;
              display: flex;
              align-items: center;
              justify-content: center;
              overflow: hidden;
              > div {
                &.text-scroll {
                  flex-shrink: 0;
                  flex-grow: 1;
                  white-space: nowrap;
                  animation: scroll-left 4s linear forwards infinite;
                }
                @keyframes scroll-left {
                  0% {
                    transform: translateX(100%);
                  }
                  100% {
                    transform: translateX(-100%);
                  }
                }
              }
            }
          }
          .loading-des {
            position: absolute;
            margin-top: 105 * @rem;
            width: 270 * @rem;
            height: 64 * @rem;
            line-height: 32 * @rem;
            font-size: 18 * @rem;
            color: #5b5b5b;
            text-align: center;
          }
        }
      }

      .loading-prompt {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        margin-bottom: 59 * @rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .Percentage {
          height: 14 * @rem;
          font-size: 14 * @rem;
          color: #888888;
          line-height: 14 * @rem;
          text-align: center;
        }
        .progress {
          height: 64 * @rem;
          line-height: 64 * @rem;
          padding: 0 30 * @rem;
        }
        .tips {
          height: 14 * @rem;
          font-size: 14 * @rem;
          color: #888888;
          line-height: 20 * @rem;
          text-align: center;
        }
      }
      .backstage-loading {
        position: absolute;
        margin-top: calc(15 * @rem + @safeAreaTop);
        margin-top: calc(15 * @rem + @safeAreaTopEnv);
        margin-right: 16 * @rem;
        top: 0;
        right: 0;
        min-width: 96 * @rem;
        height: 30 * @rem;
        background: rgba(19, 19, 19, 0.5);
        border-radius: 54 * @rem;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        span {
          min-width: 52 * @rem;
          white-space: nowrap;
          font-size: 13 * @rem;
          color: #ffffff;
          text-align: center;
        }
        p {
          width: 14 * @rem;
          height: 14 * @rem;
        }
      }
    }
    .cloudVideoContainer {
      position: relative;
      overflow: hidden;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
    }
    /deep/.isFullScreen {
      height: 100% !important;
      width: 100vw !important;
      margin: 0 !important;
      object-fit: fill !important;
    }
    /deep/.isContain {
      object-fit: contain !important;
      left: 50% !important;
      top: 50% !important;
    }
    /deep/.isContainType {
      left: 50% !important;
      top: 50% !important;
      width: 100vh !important;
      height: 100vw !important;
      width: calc(
        100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom)
      ) !important;
      height: calc(
        100vw - env(safe-area-inset-left) - env(safe-area-inset-right)
      ) !important;
    }
    /deep/.isFill {
      object-fit: fill !important;
    }
  }
  .onhook-popup {
    box-sizing: border-box;
    width: 300 * @rem;
    padding: 20 * @rem 15 * @rem 25 * @rem 15 * @rem;
    background-size: 300 * @rem auto;
    border-radius: 20 * @rem;
    z-index: 2996 !important;
    .title {
      text-align: center;
      font-weight: bold;
    }
    .content {
      line-height: 17 * @rem;
      font-size: 14 * @rem;
      color: #777777;
      padding: 0 25 * @rem;
      margin-top: 22 * @rem;
      text-align: center;
    }
    .btn-info {
      display: flex;
      align-items: center;
      justify-content: space-around;
      margin: 12 * @rem 0 0;
      .cancel,
      .confirm {
        width: 126 * @rem;
        height: 40 * @rem;
        border-radius: 30 * @rem;

        font-size: 13 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .cancel {
        color: #7d7d7d;
        background: #f2f2f2;
      }
      .confirm {
        margin-left: 17 * @rem;
        color: #ffffff;
        background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
      }
      .cancel-text {
        margin-left: 0;
        width: 100%;
      }
    }
    .btn-info-24 {
      margin: 24 * @rem 0 0;
    }

    .tips {
      display: flex;
      align-items: center;
      margin: 10 * @rem auto 0;
      padding: 0 25 * @rem;
      .gou {
        width: 12 * @rem;
        height: 12 * @rem;
        border-radius: 12 * @rem;
        border: 1px solid #7d7d7d;
        &.remember {
          background: url('~@/assets/images/cloudHangup/bzts-img.png') no-repeat
            0 0;
          background-size: 14 * @rem 14 * @rem;
          width: 14 * @rem;
          height: 14 * @rem;
          border: none;
        }
      }
      .tips-text {
        font-size: 12 * @rem;
        color: #999999;
        margin-left: 6 * @rem;
      }
    }
    &.portrait {
      top: 50%;
      transform: translate(-50%, -50%) rotate(90deg);
    }
  }
  .vant-popup {
    border-radius: 24 * @rem 24 * @rem 0 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    .popup-title {
      width: 375 * @rem;
      height: 61 * @rem;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 18 * @rem;
      box-sizing: border-box;
      font-size: 16 * @rem;
      color: #333333;
      font-weight: bold;
    }
    .popup-text {
      padding: 0 18 * @rem;
      box-sizing: border-box;
      font-size: 14 * @rem;
      color: #333333;
      text-align: justify;
    }
    .game-list-search {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 0 18 * @rem;
      box-sizing: border-box;
      .search-bar {
        box-sizing: border-box;
        padding: 0 16 * @rem 0 16 * @rem;
        width: 100%;
        height: 33 * @rem;
        border-radius: 17 * @rem;
        display: flex;
        align-items: center;
        background-color: #f5f5f5;
        .search-input {
          flex: 1;
          height: 33 * @rem;
          margin-left: 7 * @rem;
          form {
            border: 0;
            outline: 0;
            display: block;
            width: 100%;
            height: 100%;
          }
          input {
            border: 0;
            outline: 0;
            display: block;
            width: 100%;
            height: 100%;
            background-color: transparent;
            font-size: 14 * @rem;
            color: #333;
          }
        }
        .input-clear {
          width: 18 * @rem;
          height: 18 * @rem;
          .image-bg('~@/assets/images/input-clear.png');
          margin: 0 12 * @rem;
        }
      }
    }
    .popup-btn {
      padding: 0 18 * @rem;
      width: 100%;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-around;
      .cancel,
      .exit {
        margin: 36 * @rem 0;
        width: 126 * @rem;
        height: 40 * @rem;
        line-height: 40 * @rem;
        border-radius: 30 * @rem;
        font-size: 15 * @rem;
        text-align: center;
      }
      .cancel {
        background: #f2f2f2;
        color: #7d7d7d;
      }
      .exit {
        background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
        color: #ffffff;
        &.disable-btn {
          background: #c1c1c1;
        }
      }
    }
  }
}
.vdr.active:before {
  outline: none;
}
</style>
