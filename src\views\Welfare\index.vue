<template>
  <div class="switch-page">
    <rubber-band :topColor="'#E8FCDA'" :bottomColor="'#F7F8FA'">
      <div class="main">
        <div class="gold-bar">
          <div class="left">
            <div class="left-title">当前金币</div>
            <div class="left-content" @click="toGoldCoinDetail">
              <div class="left-num">{{
                userInfo.token ? userInfo.gold : '--'
              }}</div>
              <div class="right-icon"></div>
            </div>
          </div>
          <div class="right">
            <div class="right-img"></div>
          </div>
        </div>
        <div class="savings-card-bar" v-if="!userInfo.is_sqk_member">
          <div class="savings-card-icon"></div>
          <div class="text">开通省钱卡立返全额，每日再领1000金币</div>
          <div class="btn" @click="toSavingsCard">{{ $t('立即开通') }}</div>
        </div>
        <!-- 金刚区 -->
        <div
          class="section-container gold-container"
          id="navListSwiper"
          v-if="tabList.length"
        >
          <swiper
            class="select-list"
            :options="swiperOption"
            :auto-update="true"
            v-if="tabList.length > 0"
          >
            <swiper-slide
              class="select-item btn"
              v-for="(item, index) in tabList"
              :key="index"
            >
              <div class="select-icon" v-if="item.icon_lottie">
                <yy-lottie
                  class="lottie-icon"
                  :width="48"
                  :height="48"
                  :options="{
                    autoplay: true,
                    loop: true,
                    path: item.icon_lottie,
                  }"
                  v-if="item.icon_lottie"
                ></yy-lottie>
              </div>
              <div class="select-icon" v-else>
                <img :src="item.icon_url" alt="" />
              </div>
              <div class="select-text">
                <div class="select-name">{{ item.text1 }}</div>
              </div>
            </swiper-slide>
          </swiper>
          <div class="swiper-scrollbar"></div>
        </div>
        <div
          class="tab-bar"
          :class="{ active: nav == 1, fixed: navFixed }"
          :style="{ top: navFixed ? navTop + 'px' : 0 }"
        >
          <div
            class="tab-item"
            v-for="(item, index) in navList"
            :key="index"
            @click="changeTab(item)"
          ></div>
        </div>
        <div class="tab-bar-placeholder" v-if="navFixed"></div>
        <div class="safeAreaHeight"></div>
        <router-view class="nav-page" ref="childPage"></router-view>
      </div>
    </rubber-band>
  </div>
</template>

<script>
import { ApiCoinCenterTabAction } from '@/api/views/welfare';
import { remNumberLess } from '@/common/styles/_variable.less';

import {
  platform,
  BOX_showActivityByAction,
  BOX_openInNewWindow,
} from '@/utils/box.uni.js';
import { mapGetters, mapMutations } from 'vuex';
export default {
  name: 'Welfare',
  data() {
    let that = this;
    return {
      platform,
      navbarOpacity: 0,
      bgStyle: 'transparent-white',
      nav: 0,
      navList: [
        {
          title: '福利中心',
          id: 0,
          pathName: 'Welfare',
          topColor: '#7CC1DE',
          bottomColor: '#A2E7F8',
        },
        {
          title: '金币商城',
          id: 1,
          pathName: 'WelfareGoldCoinExchange',
          topColor: '#FF6B51',
          bottomColor: '#FEEED6',
        },
      ],
      navTop: 0,
      tabClientTop: 0,
      navFixed: false,
      // 金刚区
      tabList: [],
      swiperOption: {
        slidesPerView: 'auto',
        freeMode: true,
        freeModeMomentumRatio: 0.3, // 运动惯量
        observer: true, //开启动态检查器，监测swiper和slide
        observeSlideChildren: true, //监测Swiper的子元素wrapper、pagination、navigation、scrollbar或其他一级子元素
        scrollbar: {
          el: '.swiper-scrollbar',
        },
        on: {
          click: function () {
            that.CLICK_EVENT(that.tabList[this.clickedIndex]?.click_id);
            BOX_showActivityByAction(that.tabList[this.clickedIndex]);
          },
        },
      },
    };
  },
  mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    this.refreshCurrent();
    window.addEventListener('scroll', this.handleScroll);
  },
  beforeDestroy() {
    // 清除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);
  },
  watch: {
    $route(route) {
      this.refreshCurrent();
    },
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    next(true);
  },
  async created() {
    if (this.platform == 'android') {
      if (this.onActionData.web_url) {
        if (this.onActionData.web_url == this.$route.name) {
          await this.$router.replace({
            params: this.onActionData,
            query: { random: Math.random() },
          });
        } else {
          await this.toPage(this.onActionData.web_url, this.onActionData);
        }
        this.setOnActionData({});
      } else {
        this.$router.replace({ query: { random: Math.random() } });
      }
    }
    await this.getWelfareIndex();
    this.$nextTick(() => {
      // 解决金刚区点击穿透的问题
      document
        .querySelector('#navListSwiper')
        .addEventListener('touchstart', function (e) {
          e.preventDefault();
        }),
        {
          passive: false,
        };
    });
  },
  computed: {
    ...mapGetters({
      onActionData: 'system/onActionData',
    }),
  },
  methods: {
    async onResume() {
      this.freshAndroidToken();
      if (this.onActionData.web_url) {
        if (this.onActionData.web_url == this.$route.name) {
          await this.$router.replace({
            params: this.onActionData,
            query: { random: Math.random() },
          });
        } else {
          await this.toPage(this.onActionData.web_url, this.onActionData);
        }
        this.setOnActionData({});
      } else {
        this.$router.replace({ query: { random: Math.random() } });
      }
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (scrollTop > this.tabClientTop - this.navTop) {
        this.navFixed = true;
      } else {
        this.navFixed = false;
      }
    },
    changeTab(item) {
      this.toPage(item.pathName);
    },
    refreshCurrent() {
      this.nav = this.navList.findIndex(item => {
        if (item.pathName) {
          return item.pathName == this.$route.name;
        }
      });
    },
    async getWelfareIndex() {
      let res = await ApiCoinCenterTabAction();
      let { tab } = res.data;
      this.tabList = tab;
      this.$nextTick(() => {
        this.navTop =
          document.querySelectorAll('.gold-bar')[0].offsetHeight - 1;
        this.tabClientTop = document.querySelectorAll('.tab-bar')[0].offsetTop;
      });
    },
    toSavingsCard() {
      BOX_openInNewWindow(
        { name: 'SavingsCard' },
        { url: `https://${this.$h5Page.env}game.3733.com/#/savings_card` },
      );
    },
    toGoldCoinDetail() {
      BOX_openInNewWindow(
        { name: 'GoldCoinDetail' },
        { url: `https://${this.$h5Page.env}game.3733.com/#/gold_coin_detail` },
      );
    },
    ...mapMutations({
      setOnActionData: 'system/setOnActionData',
      freshAndroidToken: 'user/freshAndroidToken',
    }),
  },
};
</script>

<style lang="less" scoped>
.switch-page {
  .main {
    padding-top: 115 * @rem;
    padding-top: calc(115 * @rem + @safeAreaTop);
    padding-top: calc(115 * @rem + @safeAreaTopEnv);
    background: #f7f8fa url(~@/assets/images/welfare/welfare-page-main-bg.png)
      no-repeat;
    background-size: 100% 370 * @rem;
    background-attachment: fixed;
    position: relative;
  }

  .gold-bar {
    .fixed-center;
    box-sizing: border-box;
    padding: 0 0 0 20 * @rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 115 * @rem;
    height: calc(115 * @rem + @safeAreaTop);
    height: calc(115 * @rem + @safeAreaTopEnv);
    padding-top: @safeAreaTop;
    padding-top: @safeAreaTopEnv;
    background: #f7f8fa url(~@/assets/images/welfare/welfare-page-main-bg.png)
      no-repeat;
    background-size: 100% 370 * @rem;
    position: fixed;
    top: 0;
    z-index: 10;

    .left {
      .left-title {
        font-size: 14 * @rem;
        color: @themeColor;
        line-height: 18 * @rem;
        font-weight: bold;
      }
      .left-content {
        margin-top: 8 * @rem;
        display: flex;
        align-items: center;
        .left-num {
          font-size: 32 * @rem;
          color: @themeColor;
          font-weight: bold;
        }
        .right-icon {
          width: 16 * @rem;
          height: 16 * @rem;
          background: url(~@/assets/images/welfare/arrow-right-green.png)
            no-repeat;
          background-size: 16 * @rem 16 * @rem;
          margin-left: 4 * @rem;
        }
      }
    }
    .right {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .right-img {
        background: url(~@/assets/images/welfare/welfare-page-main-jxw.png)
          no-repeat;
        background-size: 158 * @rem 118 * @rem;
        width: 158 * @rem;
        height: 118 * @rem;
        position: absolute;
        right: 0;
        top: 3 * @rem;
        top: calc(3 * @rem + @safeAreaTop);
        top: calc(3 * @rem + @safeAreaTopEnv);
      }
    }
  }

  .savings-card-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 351 * @rem;
    height: 48 * @rem;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 12 * @rem;
    padding: 0 10 * @rem;
    box-sizing: border-box;
    position: relative;
    z-index: 2;

    .savings-card-icon {
      width: 32 * @rem;
      height: 26 * @rem;
      background: url(~@/assets/images/welfare/savings-card-icon1.png) no-repeat;
      background-size: 32 * @rem 26 * @rem;
      margin-right: 6 * @rem;
    }

    .text {
      flex: 1;
      min-width: 0;
      height: 15 * @rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 12 * @rem;
      color: #30343b;
      line-height: 15 * @rem;
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60 * @rem;
      height: 26 * @rem;
      background: #ff7847;
      border-radius: 47 * @rem;
      margin-left: 10 * @rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 10 * @rem;
      color: #ffffff;
      line-height: 13 * @rem;
      text-align: center;
    }
  }

  .gold-container {
    background: #fff;
    width: 351 * @rem;
    border-radius: 12 * @rem;
    margin: 12 * @rem auto 0;
    padding-bottom: 7 * @rem;
    margin-bottom: 17 * @rem;
    overflow: hidden;
    &::-webkit-scrollbar {
      display: none;
    }
    .select-list {
      width: 100%;
      margin: 0 auto;
      .select-item {
        width: 70 * @rem;
        box-sizing: border-box;
        position: relative;
        height: 100 * @rem;

        background-color: #ffffff;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .select-icon {
          img {
            width: auto;
            height: 51 * @rem;
            display: block;
            margin: 0 auto;
          }
        }
        .select-text {
          box-sizing: border-box;
          .select-name {
            font-size: 12 * @rem;
            font-weight: 500;
            color: #000000;
            text-align: center;
          }
        }
      }
    }
    .swiper-scrollbar {
      margin: -5 * @rem auto 0;
      width: 18 * @rem;
      height: 3 * @rem;
      background-color: #e3e5e8;
      position: relative;
      z-index: 2;
    }
    /deep/ .swiper-scrollbar-drag {
      background: #93999f;
    }
  }

  .tab-box {
    width: 100%;
    height: 53 * @rem;
  }
  .tab-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 53 * @rem;
    background: url(~@/assets/images/welfare/tab-change1.png) no-repeat bottom;
    background-size: 100% 53 * @rem;

    &.fixed {
      .fixed-center;
      position: fixed;
      z-index: 10;
    }

    &.active {
      background-image: url(~@/assets/images/welfare/tab-change2.png);
    }

    .tab-item {
      width: 50%;
      height: 53 * @rem;
      font-size: 16 * @rem;
      color: #ffffff;
      line-height: 53 * @rem;
      padding: 0 10 * @rem;
      position: relative;

      &:last-of-type {
        margin-right: 0;
      }

      &.active {
        font-weight: bold;

        &::before {
          content: '';
          display: block;
          width: 20 * @rem;
          height: 4 * @rem;
          background-color: #fff;
          border-radius: 4 * @rem;
          position: absolute;
          bottom: 5 * @rem;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }
  .tab-bar-placeholder {
    width: 100%;
    height: 53 * @rem;
    margin-top: 17 * @rem;
  }
  .safeAreaHeight {
    width: 100%;
    height: @safeAreaTop;
    height: @safeAreaTopEnv;
    position: fixed;
    top: 0;
    left: 0;
    opacity: 0;
  }
  .nav-page {
    position: relative;
  }
}
</style>
