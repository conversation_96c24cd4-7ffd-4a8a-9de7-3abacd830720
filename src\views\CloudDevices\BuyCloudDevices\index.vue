<template>
  <div class="buy-page" @click="devicesListShow = false">
    <nav-bar-2 :title="equip_id ? '续费设备' : '购买设备'" :azShow="true">
      <template #right>
        <div class="rule-btn btn" @click="toRuler"></div>
      </template>
    </nav-bar-2>
    <div class="meal-list" v-if="mealList.length">
      <div
        class="meal-item"
        :class="{
          active: meal.id == selectedMeal.id,
          first: first && meal.id == 1,
        }"
        v-for="(meal, index) in mealList"
        :key="index"
        @click="selectedMeal = meal"
      >
        <div class="title">{{ meal.title }}</div>
        <div class="price"><span>￥</span>{{ meal.current_price }}</div>
        <div class="old-price">￥{{ meal.original_price }}</div>
        <div class="price-per-day">
          <span v-if="!first || meal.id != 1">￥</span> {{ meal.ftitle }}
        </div>
        <div
          class="corner"
          v-if="meal.corner_script && (!first || meal.id != 1)"
        >
          {{ meal.corner_script }}
        </div>
        <div class="first-flag" v-if="first && meal.id == 1"></div>
      </div>
    </div>
    <div class="pay-info">
      <div class="info-item" v-if="equip_id">
        <span class="title">续费设备</span>
        <div class="choose-devices info">
          <div class="devices-name">{{ selectedDevice.title }}</div>
          <div
            class="select-icon"
            :class="{ active: devicesListShow }"
            v-if="devicesList.length > 1"
            @click.stop="devicesListShow = !devicesListShow"
          ></div>
          <div class="devices-list-box" v-if="devicesListShow">
            <div class="devices-list">
              <div
                class="devices-item"
                :class="{ active: device.id == equip_id }"
                v-for="(device, index) in devicesList"
                :key="index"
                @click="chooseDevice(device)"
              >
                {{ device.title }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="info-item" v-else>
        <span class="title">购买设备台数（台）</span>
        <div class="operation info">
          <div
            class="minus operation-btn"
            v-if="count > 1"
            @click="count--"
          ></div>
          <div class="not-minus operation-btn" v-else></div>
          <div class="count">{{ count }}</div>
          <div
            class="add operation-btn"
            v-if="limit - count > 0"
            @click="count++"
          ></div>
          <div class="not-add operation-btn" v-else></div>
        </div>
      </div>

      <div class="info-item">
        <span class="title">需支付</span>
        <div class="price info">￥{{ totalPrice }}</div>
      </div>
    </div>
    <div class="pay-way-box">
      <div class="title">选择支付方式</div>
      <div class="pay-way-list" v-if="payWayList.length">
        <div
          class="pay-way-item"
          v-for="(way, index) in payWayList"
          :key="index"
          :class="{ selected: way.key == selectedPayType }"
          @click="selectedPayType = way.key"
        >
          <div class="icon">
            <img :src="way.icon" alt="" />
          </div>
          <div class="info">
            <div class="name">
              <span>{{ way.name }}</span>
              <div class="is-recom" v-if="way.tj"></div>
            </div>
            <div class="tip" v-if="way.tip">{{ way.tip }}</div>
          </div>
          <div class="choose" v-if="way.key == selectedPayType"></div>
        </div>
      </div>
    </div>
    <div class="buy-btn" @click="handlePay">确认购买</div>

    <!-- 云挂机升级通知弹窗 -->
    <van-dialog
      v-model="popShow"
      :showConfirmButton="false"
      :lockScroll="false"
      class="popup-dialog"
    >
      <div class="popup-container">
        <div class="content-box">
          <div class="title">{{ pop_config.title }}</div>
          <div class="content">{{ pop_config.txt1 }}</div>
          <div class="tips">{{ pop_config.txt2 }}</div>
        </div>
        <div class="btn-box">
          <div class="close btn" @click="popShow = false">取消</div>
          <div class="confirm btn" @click="toUpgradeApp()">升级app</div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import {
  ApiCloudEquipList,
  ApiCloudBugEquip,
  ApiCloudBuyOrder,
  ApiCloudBuyOrderAgain,
  ApiCloudCharge,
  ApiCloudList,
} from '@/api/views/upCloud.js';
import { mapActions, mapGetters, mapMutations } from 'vuex';
import { getQueryVariable } from '@/utils/function.js';
import { ApiGetPayUrl } from '@/api/views/recharge.js';
import {
  platform,
  BOX_openInNewWindow,
  BOX_checkInstall,
  BOX_close,
  Box_downloadApk,
  BOX_openInBrowser,
  authInfo,
} from '@/utils/box.uni.js';
export default {
  data() {
    return {
      first: false,
      equip_id: 0,
      mealList: [],
      selectedMeal: {},
      currentLimit: 20,
      limit: 20,
      devicesList: [],
      selectedDevice: {},
      devicesListShow: false,
      count: 1,
      payWayList: [],
      selectedPayType: 'wx',
      isFromDetail: false, //来自详情页进入云挂机
      idleEquipmentList: [], //闲置设备
      popShow: false, //云挂机升级通知弹窗
      pop_config: {}, //云挂机升级通知弹窗配置
      cloudVersionCode: 4313, //云机需升级新版的版本号
    };
  },
  computed: {
    totalPrice() {
      return this.selectedMeal.current_price
        ? this.count * this.selectedMeal.current_price
        : 0;
    },
    ...mapGetters({
      initData: 'system/initData',
      receiveDataInfo: 'cloud_hangup/receiveData',
      checkCloudDeviceItemInfo: 'cloud_hangup/checkCloudDeviceItem',
    }),
  },
  watch: {
    equip_id(newVal, oldVal) {
      this.devicesList.forEach(item => {
        if (item.id == newVal) {
          this.selectedDevice = item;
        }
      });
    },
    // selectedMeal(newVal, oldVal) {
    //   if (newVal.id == 1 && this.first) {
    //     if (this.currentLimit) {
    //       this.limit = this.count = 1;
    //     } else {
    //       this.limit = 0;
    //       this.count = 0;
    //     }
    //   } else {
    //     this.limit = this.currentLimit;
    //   }
    // },
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (from.name === 'UpDetail' || from.name === 'ExternalGameDetail') {
        vm.isFromDetail = true;
      } else {
        vm.isFromDetail = false;
      }
    });
  },
  created() {
    if (platform == 'android') {
      if (authInfo.versionCode < this.cloudVersionCode) {
        this.setCloudType(0);
      } else {
        let cloud_type;
        try {
          cloud_type = BOX.getCurrentCloudPlatform();
        } catch (error) {
          cloud_type = 0;
        }
        this.setCloudType(cloud_type);
      }
    }
  },
  mounted() {
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
  },
  async created() {
    this.$toast.loading({
      forbidClick: true,
    });
    try {
      // 获取云挂载设备列表
      await this.getCloudMountedEquipList();

      if (platform === 'android') {
        this.equip_id = Number(getQueryVariable('equip_id')) || this.equip_id;
      } else {
        this.equip_id = Number(this.$route.params.equip_id) || this.equip_id;
      }
      const existsEquip_id = this.devicesList.some(
        item => item.id == this.equip_id,
      );
      this.equip_id = existsEquip_id ? this.equip_id : 0;
      await this.getMeal();
    } finally {
      this.$toast.clear();
    }
  },
  methods: {
    toUpgradeApp() {
      this.popShow = false;
      this.$nextTick(() => {
        BOX.checkUpdate();
      });
    },
    toRuler() {
      BOX_openInNewWindow(
        { name: 'BuyCloudDevicesRuler' },
        {
          url: `https://${this.$h5Page.env}game.3733.com/#/buy_cloud_devices_ruler`,
        },
      );
    },
    async onResume() {
      await this.getCloudMountedEquipList();
      await this.getMeal();
    },
    async getCloudMountedEquipList() {
      const res = await ApiCloudEquipList({
        need_game: 0,
      });

      this.devicesList = res.data.list;
    },
    // flag购买失败时传递参数
    async getMeal(flag = true) {
      const res = await ApiCloudBugEquip();
      this.mealList = res.data.list;
      this.selectedMeal = this.mealList[0];
      this.payWayList = res.data.payWayArr;
      this.selectedPayType = this.payWayList[0].key;
      this.limit = this.currentLimit =
        res.data.limit > res.data.current_limit
          ? res.data.current_limit
          : res.data.limit;
      this.first = res.data.new_user;
      this.pop_config = res.data?.pop_config || {};
      if (this.equip_id) {
        this.count = 1;
        return false;
      }
      // if (this.first && this.limit) {
      //   this.limit = 1;
      // }
      if (this.limit == 0) {
        this.count = 0;
      } else {
        if (flag) {
          this.count = 1;
        } else {
          this.count = this.count > this.limit ? this.limit : this.count;
        }
      }
    },
    chooseDevice(item) {
      this.equip_id = item.id;
      this.devicesListShow = false;
    },
    async handlePay() {
      if (this.count == 0) {
        return false;
      }
      if (
        platform == 'android' &&
        authInfo.versionCode < this.cloudVersionCode
      ) {
        this.popShow = true;
        return false;
      }
      this.$toast.loading({
        message: '支付中...',
        forbidClick: true,
      });
      const orderParams = {
        num: this.count,
        day: this.selectedMeal.period,
        amount: this.totalPrice,
        payWay: this.selectedPayType,
      };
      if (this.equip_id) {
        //续费
        orderParams.equip_id = this.equip_id;
        let res = await ApiCloudBuyOrderAgain(orderParams)
          .then(async orderRes => {
            await ApiGetPayUrl({
              orderId: orderRes.data.orderId,
              orderType: 106,
              payWay: this.selectedPayType,
              packageName: '',
            }).then(res => {
              // 订单完成返回上一页
              if (platform === 'android') {
                BOX_close();
              } else {
                this.$router.go(-1);
              }
            });
            // 订单完成后刷新设备数量
            this.getCloudMountedEquipList();
          })
          .catch(e => {
            // console.log(e);
            this.getMeal(false);
          });
      } else {
        //购买
        let res = await ApiCloudBuyOrder(orderParams)
          .then(async orderRes => {
            await ApiGetPayUrl({
              orderId: orderRes.data.orderId,
              orderType: 106,
              payWay: this.selectedPayType,
              packageName: '',
            }).then(res => {
              // 订单完成返回上一页
              if (platform === 'android') {
                BOX_close();
              } else {
                if (this.isFromDetail) {
                  this.checkOrderPaymentStatus(orderRes.data.orderId);
                } else {
                  this.$router.go(-1);
                }
              }
            });
            // 订单完成后刷新设备数量
            this.getCloudMountedEquipList();
            this.getMeal();
          })
          .catch(e => {
            // console.log(e);
            this.getMeal(false);
          });
      }
    },
    // 查询订单支付状态
    async checkOrderPaymentStatus(order_id = '') {
      this.idleEquipmentList = [];
      const res = await ApiCloudCharge({ order_id });
      const { status } = res.data;
      if (status === 2) {
        const res1 = await ApiCloudList();
        const { list } = res1.data;
        list.forEach(device => {
          switch (device.expire_code) {
            case 0:
              this.idleEquipmentList.push(device);
              break;
            default:
              break;
          }
        });
        if (this.idleEquipmentList.length) {
          this.setReceiveData(this.receiveDataInfo);
          this.setCheckCloudDeviceItem(this.idleEquipmentList[0]);
          this.toPage('CloudHangupInterface', {
            receiveData: this.receiveDataInfo,
            checkCloudDeviceItem: this.idleEquipmentList[0],
          });
        }
      }
    },
    ...mapMutations({
      setReceiveData: 'cloud_hangup/setReceiveData',
      setCheckCloudDeviceItem: 'cloud_hangup/setCheckCloudDeviceItem',
      setCloudType: 'system/setCloudType',
    }),
  },
};
</script>

<style lang="less" scoped>
.buy-page {
  overflow: hidden;
  .rule-btn {
    width: 18 * @rem;
    height: 18 * @rem;
    background: url(~@/assets/images/recharge/buy-cloud-devices/ruler.png)
      no-repeat center center;
    background-size: 18 * @rem 18 * @rem;
  }

  .meal-list {
    display: flex;
    align-items: flex-start;
    overflow-x: auto;
    padding: 20 * @rem 18 * @rem 0;

    &::-webkit-scrollbar {
      width: 0;
      display: none;
    }

    .meal-item {
      flex-shrink: 0;
      width: 99 * @rem;
      border-radius: 14 * @rem;
      border: 1 * @rem solid #e1e1e1;
      margin-right: 8 * @rem;
      position: relative;
      box-sizing: border-box;

      &:last-of-type {
        margin-right: 0;
      }

      .title {
        height: 20 * @rem;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #414141;
        line-height: 20 * @rem;
        text-align: center;
        margin: 19 * @rem auto 0;
      }

      .price {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 2 * @rem auto 0;
        height: 50 * @rem;
        font-weight: 600;
        font-size: 34 * @rem;
        color: #ff471f;
        line-height: 50 * @rem;
        text-align: center;

        span {
          height: 25 * @rem;
          font-weight: 600;
          font-size: 20 * @rem;
          color: #ff471f;
          line-height: 25 * @rem;
          text-align: center;
          margin-top: 4 * @rem;
        }
      }

      .old-price {
        height: 13 * @rem;
        font-weight: 400;
        font-size: 13 * @rem;
        color: rgba(18, 18, 18, 0.5);
        line-height: 13 * @rem;
        text-align: center;
        text-decoration: line-through;
        margin-top: -5 * @rem;
      }

      .price-per-day {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 26 * @rem;
        line-height: 26 * @rem;
        background: rgba(39, 192, 129, 0.09);
        font-weight: 500;
        font-size: 12 * @rem;
        color: #27c081;
        text-align: center;
        border-bottom-left-radius: 12 * @rem;
        border-bottom-right-radius: 12 * @rem;
        margin-top: 10 * @rem;
      }

      .corner {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 8 * @rem;
        height: 18 * @rem;
        font-weight: 600;
        font-size: 12 * @rem;
        color: #ffffff;
        line-height: 18 * @rem;
        text-align: center;
        border-top-left-radius: 14 * @rem;
        border-bottom-right-radius: 14 * @rem;
        background: #48b2ff;
        position: absolute;
        top: -1 * @rem;
        left: -1 * @rem;
      }

      .first-flag {
        width: 69 * @rem;
        height: 32 * @rem;
        display: block;
        background: url(~@/assets/images/recharge/buy-cloud-devices/first.png)
          no-repeat center center;
        background-size: 69 * @rem 32 * @rem;
        position: absolute;
        top: -11 * @rem;
        left: -2 * @rem;
      }

      &.active {
        background: rgba(22, 149, 88, 0.04);
        border: 2 * @rem solid rgba(22, 149, 88, 0.23);

        .title {
          color: #169558;
          margin-top: 18 * @rem;
        }

        .price-per-day {
          width: 99 * @rem;
          background-color: #27c081;
          color: #fff;
          margin: 10 * @rem -2 * @rem -2 * @rem;
        }

        .corner {
          top: -2 * @rem;
          left: -2 * @rem;
        }
      }

      &.first {
        border: 2 * @rem solid #ffe4d5;
        height: 136 * @rem;

        .title {
          color: #771500;
          margin-top: 16 * @rem;
        }

        .price-per-day {
          width: 99 * @rem;
          margin: 12 * @rem -2 * @rem -2 * @rem;
          background: #ff784d;
          color: #fff;
        }

        &.active {
          background: linear-gradient(to left, #ffd6be, #ffedc1);
          border-color: #f7cca9;
        }
      }
    }
  }

  .pay-info {
    margin: 44 * @rem 18 * @rem 0;
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32 * @rem;
      padding-bottom: 21 * @rem;
      border-bottom: 1 * @rem solid #efefef;

      .title {
        height: 14 * @rem;
        font-weight: bold;
        font-size: 14 * @rem;
        color: #222222;
        line-height: 14 * @rem;
      }

      .info {
        flex: 1;
        min-width: 0;
        text-align: right;
      }

      .choose-devices {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        position: relative;

        .devices-name {
          height: 14 * @rem;
          font-weight: 400;
          font-size: 14 * @rem;
          color: #222222;
          line-height: 14 * @rem;
          overflow: hidden;
        }

        .select-icon {
          display: block;
          width: 24 * @rem;
          height: 24 * @rem;
          background: url(~@/assets/images/recharge/buy-cloud-devices/select-icon.png)
            no-repeat center center;
          background-size: 24 * @rem 24 * @rem;
          margin-left: 3 * @rem;
          transition: all 0.3s ease;

          &.active {
            transform: rotate(-180deg);
          }
        }
        .devices-list-box {
          max-width: 100%;
          background: #ffffff;
          box-shadow: 0 * @rem 1 * @rem 4 * @rem 0 * @rem rgba(0, 0, 0, 0.21);
          position: absolute;
          top: 30 * @rem;
          right: 0;
          border-radius: 12 * @rem;
          &::before {
            content: '';
            width: 31 * @rem;
            height: 14 * @rem;
            background: url(~@/assets/images/recharge/buy-cloud-devices/triangle.png)
              no-repeat center center;
            background-size: 31 * @rem 14 * @rem;
            position: absolute;
            top: -14 * @rem;
            left: 50%;
            transform: translateX(-50%);
            z-index: 2;
          }
        }
        .devices-list {
          display: block;
          min-width: 100 * @rem;
          max-height: 200 * @rem;
          overflow-y: auto;

          .devices-item {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 46 * @rem;
            line-height: 46 * @rem;
            font-size: 14 * @rem;
            color: #333333;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            position: relative;

            &::after {
              content: '';
              display: block;
              width: 50%;
              height: 1 * @rem;
              background-color: #d9d9d9;
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
            }
            &:first-of-type {
              border-top-right-radius: 12 * @rem;
              border-top-left-radius: 12 * @rem;
            }
            &:last-of-type {
              border-bottom-right-radius: 12 * @rem;
              border-bottom-left-radius: 12 * @rem;
              &::after {
                display: none;
              }
            }

            &.active {
              background-color: #27c081;
              color: #fff;
              &::after {
                display: none;
              }
            }
          }
        }
      }

      .operation {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .operation-btn {
          width: 18 * @rem;
          height: 18 * @rem;

          &.minus {
            background: url(~@/assets/images/recharge/buy-cloud-devices/minus-icon.png)
              no-repeat center center;
            background-size: 18 * @rem 18 * @rem;
          }
          &.not-minus {
            background: url(~@/assets/images/recharge/buy-cloud-devices/not-minus-icon.png)
              no-repeat center center;
            background-size: 18 * @rem 18 * @rem;
            opacity: 0.7;
          }
          &.add {
            background: url(~@/assets/images/recharge/buy-cloud-devices/add-icon.png)
              no-repeat center center;
            background-size: 18 * @rem 18 * @rem;
          }
          &.not-add {
            background: url(~@/assets/images/recharge/buy-cloud-devices/not-add-icon.png)
              no-repeat center center;
            background-size: 18 * @rem 18 * @rem;
            opacity: 0.7;
          }
        }

        .count {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32 * @rem;
          height: 20 * @rem;
          line-height: 20 * @rem;
          font-weight: 400;
          font-size: 14 * @rem;
          color: #222222;
          text-align: center;
          margin: 0 2 * @rem;
          background: #f4f4f4;
          border-radius: 4 * @rem;
        }
      }

      .price {
        height: 20 * @rem;
        font-weight: 500;
        font-size: 20 * @rem;
        color: #ff471f;
        line-height: 20 * @rem;
      }
    }
  }

  .pay-way-box {
    margin: 0 18 * @rem;

    .title {
      display: flex;
      align-items: center;
      height: 14 * @rem;
      font-weight: bold;
      font-size: 14 * @rem;
      color: #222222;
      line-height: 14 * @rem;
      margin-bottom: 16 * @rem;

      span {
        height: 10 * @rem;
        font-weight: 400;
        font-size: 10 * @rem;
        color: #999999;
        line-height: 10 * @rem;
      }
    }
    .pay-way-list {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;

      .pay-way-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 161 * @rem;
        height: 52 * @rem;
        border-radius: 6 * @rem;
        border: 1 * @rem solid #e1e1e1;
        margin-bottom: 12 * @rem;
        position: relative;

        &.selected {
          border-color: #21b98a;
        }

        .icon {
          width: 24 * @rem;
          height: 24 * @rem;
          margin-left: 8 * @rem;
          margin-right: 5 * @rem;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .info {
          flex: 1;
          min-width: 0;

          .name {
            display: flex;
            align-items: center;

            span {
              display: block;
              height: 20 * @rem;
              line-height: 20 * @rem;
              font-size: 14 * @rem;
              color: #000000;
              overflow: hidden;
              text-align: left;
            }

            .is-recom {
              flex-shrink: 0;
              display: block;
              width: 39 * @rem;
              height: 17 * @rem;
              background: url(~@/assets/images/recharge/buy-cloud-devices/recom-icon.png)
                no-repeat center;
              background-size: 39 * @rem 17 * @rem;
              margin-left: 2 * @rem;
            }
          }

          .tip {
            height: 15 * @rem;
            font-weight: 400;
            font-size: 11 * @rem;
            color: #999999;
            line-height: 15 * @rem;
            margin-top: 2 * @rem;
          }
        }

        .choose {
          width: 26 * @rem;
          height: 24 * @rem;
          background: url(~@/assets/images/recharge/buy-cloud-devices/choose-icon.png)
            no-repeat center;
          background-size: 26 * @rem 24 * @rem;
          position: absolute;
          bottom: -1 * @rem;
          right: -1 * @rem;
        }
      }
    }
  }

  .buy-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 335 * @rem;
    height: 50 * @rem;
    line-height: 50 * @rem;
    font-weight: 600;
    font-size: 18 * @rem;
    color: #ffffff;
    text-align: center;
    background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
    border-radius: 29 * @rem;
    margin: 42 * @rem auto 25 * @rem;
  }

  .popup-dialog {
    width: 300 * @rem;
    height: 232 * @rem;
    background: #ffffff;
    border-radius: 16 * @rem;
    z-index: 3001 !important;
    .popup-container {
      height: 232 * @rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .content-box {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        .title {
          padding: 28 * @rem 0 0 0;
          font-weight: 600;
          font-size: 18 * @rem;
          color: #191b1f;
        }
        .content {
          padding: 28 * @rem 0 0 0;
          width: 238 * @rem;
          font-weight: 400;
          font-size: 15 * @rem;
          color: #60666c;
          text-align: center;
        }
        .tips {
          padding: 21 * @rem 0 0 0;
          font-weight: 400;
          font-size: 13 * @rem;
          color: #93999f;
        }
      }

      .btn-box {
        margin-top: 21 * @rem;
        border-top: 1px solid #f0f1f5;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 48 * @rem;
        width: 100%;
        .close {
          flex: 1;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 16 * @rem;
          color: #93999f;
        }
        .confirm {
          flex: 1;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 16 * @rem;
          color: #191b1f;
        }
      }
    }
  }
}
</style>
