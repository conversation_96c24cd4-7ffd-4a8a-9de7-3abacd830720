<template>
  <div class="search-page page">
    <nav-bar-2 :border="true">
      <template #center>
        <div class="search-bar">
          <div class="search-input">
            <form @submit.prevent="submitSearch(inputText)">
              <input
                v-focus
                v-model.trim="inputText"
                type="text"
                :placeholder="placeholder"
                @input="inputListener"
                :maxlength="15"
              />
            </form>
          </div>
          <div
            class="input-clear"
            v-if="inputClearShow"
            @click="clearInput"
          ></div>
          <!-- <div class="search-icon"></div> -->
        </div>
      </template>
      <template #right>
        <div class="right-btn" @click="clickSearch">
          <!-- {{ isResult ? $t('取消') : $t('搜索') }} -->
          {{ $t('搜索') }}
        </div>
      </template>
    </nav-bar-2>
    <div class="search-container">
      <div class="search-list" v-show="!isResult">
        <div
          class="search-item"
          v-for="(item, index) in searchGuideList"
          :key="index"
          @click="clickSearchItem(item)"
        >
          <div class="search-icon">
            <div class="icon">
              <img :src="item.iconPath" alt="" />
            </div>
            <div class="title">{{ item.title }}</div>
          </div>
          <div class="search-text"> {{ item.text }} </div>
        </div>
      </div>
      <div class="search-index" v-show="!isResult">
        <!-- 搜索历史 -->
        <div class="search-history" v-if="historyList.length">
          <div class="section-title">
            <div class="title-text">
              <span>{{ $t('搜索历史') }}</span>
            </div>
            <div class="clear-history btn" @click="clearHistory">
              <i></i>
            </div>
          </div>
          <div class="word-list">
            <div
              class="word-item btn"
              v-for="(item, index) in historyList"
              :key="index"
              @click="handleRecordSearch(item)"
            >
              <img src="~@/assets/images/search/search_wyx_icon.png" alt="" />
              {{ item }}
            </div>
            <div
              class="word-item btn search-arrow-down"
              id="wordItemDown"
              :class="{ rotate_180_degrees: isDeleteShow1 }"
              @click="open1"
            >
              <img src="~@/assets/images/search/search-arrow-down.png" alt="" />
            </div>
          </div>
        </div>
        <!-- 猜你喜欢 -->
        <div class="search-youLike" v-if="guessYouLike && guessYouLike.length">
          <div class="section-title">
            <div class="title-text">
              <i></i>
              <span>{{ $t('猜你喜欢') }}</span>
            </div>
            <div class="change-batch btn" @click="randomSortGuessYouLike()">
              <i
                :class="{ 'rotate-icon': isRotated }"
                @animationend="animationEnd"
              ></i>
              <span>换一换</span>
            </div>
          </div>
          <div class="section-box">
            <div
              class="section-item"
              v-for="(item, index) in guessYouLike"
              :key="index"
              @click="handleGuessYouLikeIt(item)"
            >
              {{ item.keyword || item.title }}
              <img v-if="item.img" :src="item.img" alt="" />
            </div>
            <div
              class="section-item search-up-down"
              :class="{ rotate_180_degrees: !isDeleteShow2 }"
              id="sectionUp"
              @click="open2"
            >
              <img src="~@/assets/images/search/search-up-down.png" alt="" />
            </div>
          </div>
        </div>
        <!-- 热门模块 -->
        <van-loading
          class="hot-module-loading"
          v-if="!hotModuleLoadSuccess"
          size="24*@rem"
          >{{ $t('加载中...') }}</van-loading
        >
        <div
          class="tap-search-ranking"
          v-if="hotModuleLoadSuccess && isHasHotModuleList"
          ref="tapContainer"
        >
          <div class="tap-slide">
            <div class="tap-slide-wrap">
              <div class="tap-slide-content">
                <span
                  class="tap-search-ranking-slide-item"
                  v-for="(tap, index) in tap_list"
                  :key="tap.id"
                  ref="tabItems"
                  :class="{ tapActive: tabCurrent === tap.id }"
                  @click="clickTab(tap.id, index)"
                >
                  {{ tap.title }}</span
                >

                <span
                  class="tap-slide-line"
                  :style="{
                    left: `${lineLeft}px`,
                  }"
                >
                </span>
              </div>
            </div>
          </div>
          <div class="coupon-swiper">
            <template>
              <swiper :options="couponSwiperOption" ref="couponSwiper">
                <swiper-slide
                  class="swiper-slide"
                  v-for="(hotItem, index) in hotModuleListEntries"
                  :key="index"
                >
                  <div class="list">
                    <div class="hot-module-box">
                      <div
                        v-for="(item, index1) in hotItem.value"
                        :key="index1"
                        class="hot-module-item"
                        @click="jumpDifferentPage(item, hotItem.key)"
                      >
                        <div
                          class="hot-module-num"
                          :class="{
                            no1: index1 === 0,
                            no2: index1 === 1,
                            no3: index1 === 2,
                          }"
                          v-if="index1 < 3"
                        >
                        </div>
                        <div class="hot-module-num" v-else>
                          {{ index1 + 1 }}
                        </div>
                        <div
                          class="hot-module-img"
                          v-if="hotItem.key == 'game'"
                        >
                          <img :src="item.titlepic" alt="" />
                        </div>
                        <div
                          class="hot-module-title"
                          :class="{
                            mar_left_6: hotItem.key != 'game',
                          }"
                          >{{ item.title }}</div
                        >
                        <div class="hot-module-icon" v-if="item.img">
                          <img :src="item.img" alt="" />
                        </div>
                      </div>
                    </div>
                    <div class="hot-bg-h94"></div>
                  </div>
                </swiper-slide>
              </swiper>
            </template>
          </div>
        </div>

        <!-- 热门搜索 -->
        <!-- <div class="hot-search">
          <div class="section-title">
            <div class="hot-search-title"></div>
          </div>
          <div class="rank-list">
            <div
              class="rank-item btn"
              v-for="(item, index) in initData.configs
                ? initData.configs.hot_search_game
                : []"
              :key="index"
              @click="handleHotGameSearch(item)"
            >
              <div class="num" :class="`num${index + 1}`">{{ index + 1 }}</div>
              <div class="game-icon">
                <img :src="item.titlepic" :alt="item.title" />
              </div>
              <div class="game-name">
                {{ item.main_title
                }}<span class="game-subtitle" v-if="item.subtitle">{{
                  item.subtitle
                }}</span>
              </div>
            </div>
          </div>
        </div> -->
        <!-- 热门游戏分类 -->
        <!-- <div class="hot-word" v-if="categoryList.length">
          <div class="section-title">
            <div class="title-text">{{ $t('热门游戏分类') }}</div>
          </div>
          <div class="word-list1">
            <div
              class="word-item1 btn"
              v-for="(item, index) in categoryList"
              :key="index"
              @click="toPage('Category', { info: item })"
            >
              {{ item.title }}
            </div>
          </div>
        </div> -->
      </div>
      <div class="search-result" v-show="isResult">
        <!-- <div class="tab-bar">
          <div
            class="tab-item btn"
            v-for="(tab, tabIndex) in tabList"
            :key="tabIndex"
            :class="{ on: current === tabIndex }"
            @click="tapNav(tabIndex)"
          >
            {{ tab.name }}
          </div>
          <p class="empty-msg" v-if="this.emptyMsgInfo">
            {{ this.emptyMsgInfo }}
          </p>
        </div> -->

        <!-- <content-empty
          v-if="empty"
          class="empty-box"
          :tips="$t('咦，什么都没找到哦~')"
        ></content-empty> -->
        <yy-list
          class="game-list-box"
          :class="{ 'margin-top-70': this.emptyMsgInfo }"
          v-model="loadingObj"
          :finished="finished"
          @refresh="onRefresh()"
          @loadMore="loadMore()"
        >
          <div
            class="game-list"
            v-if="resultList.length"
            :class="{ 'aggregate-cart': tabList[current].type == 701 }"
          >
            <div
              class="game-item"
              :class="{
                'coupon-item': tabList[current].type == 701,
                'btn': tabList[current].type == 1,
                'padding-10':
                  tabList[current].type == 101 || tabList[current].type == 4,
              }"
              v-for="(item, index) in resultList"
              :key="index"
              @click="clickGame(item)"
            >
              <div class="game-item-box" v-if="tabList[current].type == 1">
                <game-item-4
                  :gameInfo="item"
                  :showHot="true"
                  @child-click="clickGame(item)"
                ></game-item-4>
                <div class="game-btn">
                  <yy-download-btn :gameInfo="item"></yy-download-btn>
                </div>
              </div>
              <div class="gift-pack-list" v-if="tabList[current].type == 101">
                <gift-pack-item
                  :couponItem="item"
                  :isShowTitlePic="isShowTitlePic"
                ></gift-pack-item>
              </div>
              <div
                class="aggregate-list"
                @click="toGameCollect(item)"
                v-if="tabList[current].type == 701"
              >
                <div class="aggregate-item">
                  <div class="aggregate-img">
                    <img :src="item.list_banner" alt="" />
                  </div>
                  <title class="aggregate-title">{{ item.title }}</title>
                </div>
              </div>
              <div class="other-list" v-if="tabList[current].type == 4">
                <div class="coupon-item1">
                  <coupon-item :key="item.id" :couponItem="item"></coupon-item>
                </div>
              </div>
            </div>
          </div>
        </yy-list>
      </div>
    </div>
    <van-popup
      v-model="showSug"
      position="top"
      :close-on-click-overlay="false"
      duration="0"
      style="height: 100%"
      class="sug-container"
    >
      <div class="sug-list">
        <!-- 加载中 -->
        <van-loading
          v-if="!loadSugSuccess && !defaultPageShow"
          class="sug-loading"
          vertical
          size="24*@rem"
          >正在加载...</van-loading
        >
        <!-- 网络异常 -->
        <div class="sug-loading" v-else-if="!loadSugSuccess && defaultPageShow">
          <default-error-page
            errorTitle="网络异常"
            errorBtn="重试"
            @callback="parentErrorCallback"
          />
        </div>
        <!-- 暂无搜索结果 -->
        <div
          class="sug-loading"
          v-else-if="loadSugSuccess && defaultPageShow && !sugList.length"
        >
          <default-not-found-page />
        </div>
        <!-- 搜索结果 -->
        <div class="sug-info" v-else>
          <SugInfoItem :sugList="sugList"></SugInfoItem>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  ApiSearchIndex,
  ApiCouponCouponList,
  ApiSearchGetHotKey,
  ApiCwbIndexSearch,
  ApiCwbIndexGetSearchHotData,
  ApiV2024SearchIndex,
  ApiV2024SearchSug,
  ApiV2024SearchChangeYourLike,
  ApiV2024SearchHotList,
  ApiV2024SearchTerms,
} from '@/api/views/search.js';
import { ApiGameCate } from '@/api/views/game.js';
import { mapGetters } from 'vuex';
import { remNumberLess } from '@/common/styles/_variable.less';
import CouponItem from './components/coupon-item/index.vue';
import GameItem from './components/game-item/index.vue';
import GiftPackItem from './components/gift-pack-item/index.vue';
import SugInfoItem from './components/sug-info-item/index.vue';
import { BOX_goToGame } from '@/utils/box.uni.js';
import { navigateToGameDetail } from '@/utils/function';
import search_zyx_icon from '@/assets/images/search/search_zyx_icon.png';
import search_lfl_icon from '@/assets/images/search/search_lfl_icon.png';
import search_gbd_icon from '@/assets/images/search/search_gbd_icon.png';
export default {
  name: 'Search',
  components: {
    CouponItem,
    GameItem,
    GiftPackItem,
    SugInfoItem,
  },
  data() {
    let that = this;
    return {
      remNumberLess,
      isResult: false,
      inputText: '',
      resultList: [], // 结果列表
      keyword: this.$t('游戏'),
      fromAction: 1,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      search_ver: 2, //6.5.1版本时为2
      empty: false,
      timer: null,
      historyList: [],
      categoryList: [
        {
          title: this.$t('精选'),
        },
      ],
      fallbackLists: {
        1: {
          dataKey: 'game_list',
          emptyKey: 'empty_game_list',
          emptyMsg: 'empty_msg',
        },
        101: {
          dataKey: 'card_list',
          emptyKey: 'empty_card_list',
          emptyMsg: 'empty_msg',
        },
        701: {
          dataKey: 'collect_list',
          emptyKey: 'empty_collect_list',
          emptyMsg: 'empty_msg',
        },
        4: {
          dataKey: 'list',
          emptyKey: 'empty_coupon_list',
          emptyMsg: 'empty_msg',
        },
      },
      guessYouLike: [], // 猜你喜欢
      isRotated: false, // 换一批
      isShowTitlePic: false, // 是否显示titlePic
      hotModuleList: [], // 热门模块
      activeModule: 0,
      tabList: [
        { name: this.$t('游戏'), type: 1 },
        { name: this.$t('礼包'), type: 101 },
        // { name: this.$t('分类'), type: 901 },
        { name: this.$t('合集'), type: 701 },
        // { name: this.$t('综合'), type: 1000 },
        { name: this.$t('其他'), type: 4 },
      ],
      current: 0,
      emptyMsgInfo: '',
      placeholder: '搜你想玩的游戏',
      searchGuideList: [
        {
          id: 1,
          iconPath: search_zyx_icon,
          title: '找游戏',
          text: '入口一键直达',
        },
        {
          id: 2,
          iconPath: search_lfl_icon,
          title: '领福利',
          text: '超值游戏福利',
        },
        {
          id: 3,
          iconPath: search_gbd_icon,
          title: '逛榜单',
          text: 'get热门游戏',
        },
      ],
      lineLeft: 0,
      tabCurrent: 0,
      resizeObserver: null, // ResizeObserver 实例
      tap_list: [
        {
          id: 0,
          title: '热门游戏',
        },
        {
          id: 1,
          title: '热门分类',
        },
        {
          id: 2,
          title: '热门合集',
        },
      ], // 热门模块名称
      hotModuleLoadSuccess: false,
      isHasHotModuleList: false,
      hotModuleListEntries: [],
      couponSwiper: null,
      couponSwiperOption: {
        observer: true,
        slidesPerView: 1,
        observerSlideChildren: true,
        observerParents: true,
        on: {
          slideChange: function (e) {
            setTimeout(() => {
              that.tabCurrent = this.activeIndex;
              that.handleResize();
            }, 0);
          },
        },
      },

      hideIndex1: 0,
      hideIndex2: 0,
      isDeleteShow1: true,
      isDeleteShow2: true,
      showSug: false, // 显示搜索建议
      loadSugSuccess: false, // 搜索建议loading
      defaultPageShow: false, // 网络错误占位符
      sugList: [], // 搜索建议信息
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
      searchHotText: 'system/searchHotText',
    }),
    inputClearShow() {
      return this.inputText.length ? true : false;
    },
  },
  directives: {
    focus: {
      inserted(el) {
        el.focus();
      },
    },
  },
  async created() {
    // this.inputText = this.searchHotText;
    this.placeholder = this.searchHotText;
    this.updateHistory();
    await this.getHotKey();
    await this.getGuessYouLike();
    await this.getHotModuleList();
  },
  mounted() {
    this.$nextTick(() => {
      this.hide1();
    });
  },
  beforeDestroy() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  },
  methods: {
    jumpDifferentPage(item, key) {
      console.log(item, key);
      switch (key) {
        case 'game':
          navigateToGameDetail(item);
          break;
        case 'type':
          this.toPage('Category', { info: item });
          break;
        case 'collect':
          this.toPage('ExternalGameCollect', { id: item.id, info: item });
          break;

        default:
          break;
      }
    },
    clickSearchItem(item) {
      console.log(item);
      switch (item.id) {
        case 1:
          break;
        case 2:
          this.toPage('Welfare');
          break;
        case 3:
          this.$router.push({
            path: '/home/<USER>',
          });
          break;
        default:
          break;
      }
    },
    async parentErrorCallback() {
      this.loadSugSuccess = false;
      this.defaultPageShow = false;
      this.getSearchSug();
    },
    hide1(first = true) {
      let line = 0,
        offsetLeft = 0;
      const tags = document.querySelectorAll('.word-list > .word-item');
      // 计算行数 往后的行数第一个offsetLeft跟第一个标签的offsetLeft一样
      tags.forEach((item, index) => {
        if (index === 0) {
          line = 1;
          offsetLeft = item?.offsetLeft;
        } else if (item?.offsetLeft === offsetLeft) {
          line++;
        }
        if (line === 3 && first) {
          // 第3行的时候 最后一个的下标
          this.hideIndex1 = index - 1;
        }
        if (line > 2) {
          item.style.display = 'none';
          if (item.id === 'wordItemDown') {
            item.style.display = 'flex';
            tags[this.hideIndex1].style.display = 'none';
          }
        }
      });
    },
    openAll1() {
      const tags = document.querySelectorAll('.word-list > .word-item');
      tags.forEach(item => {
        item.style.display = 'flex';
      });
    },
    open1() {
      this.isDeleteShow1 ? this.openAll1() : this.hide1(false);
      this.isDeleteShow1 = !this.isDeleteShow1;
    },
    hide2(first = true) {
      let line = 0,
        offsetLeft = 0;
      const tags = document.querySelectorAll('.section-box > .section-item');
      // 计算行数 往后的行数第一个offsetLeft跟第一个标签的offsetLeft一样
      tags.forEach((item, index) => {
        if (index === 0) {
          line = 1;
          offsetLeft = item?.offsetLeft;
        } else if (item?.offsetLeft === offsetLeft) {
          line++;
        }
        if (line === 3 && first) {
          // 第3行的时候 最后一个的下标
          this.hideIndex2 = index - 1;
        }
        if (line > 2) {
          item.style.display = 'none';
          if (item.id === 'sectionUp') {
            item.style.display = 'flex';
            tags[this.hideIndex2].style.display = 'none';
          }
        }
      });
    },
    openAll2() {
      const tags = document.querySelectorAll('.section-box > .section-item');
      tags.forEach(item => {
        item.style.display = 'flex';
      });
    },
    open2() {
      this.isDeleteShow2 ? this.openAll2() : this.hide2(false);
      this.isDeleteShow2 = !this.isDeleteShow2;
    },
    async getSearchSug() {
      try {
        const res = await ApiV2024SearchSug({
          keyword: this.inputText,
        });
        const { list, top } = res.data;
        this.sugList = [top, ...list];
        this.loadSugSuccess = true;
        this.defaultPageShow = true;
      } catch (error) {
        if (
          error.toString().includes('code 500') ||
          error.toString().includes('code 404')
        ) {
          this.loadSugSuccess = false;
          this.defaultPageShow = true;
        }
        if (!navigator.onLine) {
          this.loadSugSuccess = false;
          this.defaultPageShow = true;
        }
      } finally {
      }
    },
    clickTab(id, index) {
      this.tabCurrent = id;
      this.$nextTick(() => {
        this.updateLinePosition(index);
      });

      if (this.couponSwiper) {
        this.couponSwiper.slideTo(index);
      }
    },
    handleResize() {
      this.$nextTick(() => {
        this.updateLinePosition();
      });
    },
    updateLinePosition(index = this.tabCurrent) {
      // 获取所有 Tab 元素
      const tabItems = this.$refs.tabItems;
      if (!tabItems || !tabItems[index]) return;

      const selectedTab = tabItems[index].getBoundingClientRect();
      const container =
        this.$refs.tabItems[0].parentElement.getBoundingClientRect();

      this.lineLeft = selectedTab.left - container.left; // 相对父容器的偏移
    },
    async getHotKey() {
      const res = await ApiSearchGetHotKey();
      this.categoryList = res.data.key_list;
    },
    clearInput() {
      this.inputText = '';
      this.showSug = false;
      this.isResult = false;
      this.$nextTick(() => {
        document.querySelector('.search-input input').focus();
      });
    },
    clearHistory() {
      this.$dialog
        .confirm({
          message: this.$t('确认删除？'),
          lockScroll: false,
        })
        .then(() => {
          localStorage.setItem('HISTORY_LIST', '');
          this.updateHistory();
          this.$toast(this.$t('历史记录已删除'));
        });
    },
    addHistoryItem(keyword) {
      if (!keyword) return;
      let index = this.historyList.findIndex(item => item == keyword);
      if (index != -1) {
        this.historyList.splice(index, 1);
      }
      this.historyList.unshift(keyword);
      if (this.historyList.length > 10) {
        this.historyList.pop();
      }
      this.updateHistory(this.historyList);
    },
    updateHistory(historyList) {
      if (historyList) {
        localStorage.setItem('HISTORY_LIST', JSON.stringify(historyList));
      }

      let historyListStr = localStorage.getItem('HISTORY_LIST');
      if (historyListStr) {
        this.historyList = JSON.parse(historyListStr);
      } else {
        this.historyList = [];
      }
    },
    async onRefresh() {
      this.finished = false;
      await this.handleSearch(this.inputText, 1, 4);
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.resultList.length) {
        await this.handleSearch(this.inputText, 1, 5);
      } else {
        await this.handleSearch(this.inputText, 2, 5);
      }
    },
    clickSearch() {
      this.showSug = false;
      this.$nextTick(() => {
        document.querySelector('.search-input input').focus();
      });
      if (!this.inputText) {
        this.inputText = this.searchHotText;
      }
      this.submitSearch(this.inputText);
      return;
      if (this.isResult === false) {
        this.showSug = false;
        this.$nextTick(() => {
          document.querySelector('.search-input input').focus();
        });
        if (!this.inputText) {
          this.inputText = this.searchHotText;
        }
        this.submitSearch(this.inputText);
      } else {
        if (this.inputText) {
          this.inputText = '';
          this.resultList = [];
          this.showSug = false;
        }
        this.current = 0;
        this.isResult = false;
      }
    },
    submitSearch(keyword) {
      this.loadingObj.loading = true;
      this.addHistoryItem(keyword);
      this.handleSearch(keyword);
    },
    inputListener() {
      if (!this.inputText) {
        this.loadSugSuccess = false;
        this.showSug = false;
      }
      if (this.timer || !this.inputText) {
        clearTimeout(this.timer);
        this.timer = null;
      }

      if (this.inputText) {
        this.showSug = true;
        this.loadSugSuccess = false;
        this.defaultPageShow = false;
        this.timer = setTimeout(() => {
          this.sugList = [];
          this.getSearchSug();
        }, 500);
      }
    },
    // inputListener() {
    //   if (this.timer || !this.inputText) {
    //     clearTimeout(this.timer);
    //     this.timer = null;
    //   }
    //   if (this.inputText) {
    //     this.timer = setTimeout(() => {
    //       this.resultList = [];
    //       this.loadingObj.loading = true;
    //       this.handleSearch(this.inputText);
    //     }, 500);
    //   }
    // },
    handleSearch(keyword, action = 1, from = 1, type = 1) {
      this.loadingObj.loading = true;
      this.isShowTitlePic = false;
      clearTimeout(this.timer);
      this.timer = null;
      keyword = keyword.trim();
      if (!keyword) {
        this.$toast(this.$t('请输入搜索词'));
        return;
      }

      if (action === 1) {
        this.resetSearchState();
      } else {
        this.page++;
      }

      type = this.tabList[this.current].type;

      const apiFunction =
        type === 4 ? ApiCouponCouponList : ApiV2024SearchIndex;
      const params = {
        keyword,
        type,
        fromAction: from,
        page: this.page,
        listRows: this.listRows,
        search_ver: this.search_ver,
      };
      if (type === 4) {
        delete params.type;
      }
      apiFunction(params)
        .then(res => {
          let dataList = [];
          this.emptyMsgInfo = '';
          if (this.fallbackLists[type]) {
            const { dataKey, emptyKey, emptyMsg } = this.fallbackLists[type];
            dataList = res.data[dataKey]?.length
              ? res.data[dataKey]
              : res.data[emptyKey] || [];

            this.emptyMsgInfo = res.data[dataKey]?.length
              ? ''
              : res.data[emptyMsg];
          }

          if (type === 101 && res.data['empty_card_list']) {
            this.isShowTitlePic = true;
          }

          if (action === 1 || this.page === 1) {
            this.resultList = [];
            this.empty = dataList.length === 0;
          }
          this.resultList.push(...dataList);
          this.loadingObj.loading = false;
          this.finished = dataList.length < this.listRows;
        })
        .catch(e => {
          if (e.code == 500) {
            this.empty = true;
          }
        });
    },
    resetSearchState() {
      this.isResult = true;
      this.empty = false;
      this.finished = false;
      this.page = 1;
    },

    handleRecordSearch(keyword) {
      this.resultList = [];
      this.inputText = keyword;
      this.addHistoryItem(this.inputText);
      this.handleSearch(this.inputText, 1, 3);
    },
    handleGuessYouLikeIt(item) {
      this.resultList = [];
      this.inputText = item.keyword || item.title;
      this.addHistoryItem(this.inputText);
      this.handleSearch(this.inputText, 1, 3);
    },
    handleHotKeywordSearch(keyword) {
      this.resultList = [];
      this.inputText = keyword;
      this.addHistoryItem(this.inputText);
      this.handleSearch(this.inputText, 1, 2);
    },
    handleHotGameSearch(item) {
      this.addHistoryItem(item.main_title);
      this.$router.push({
        name: 'GameDetail',
        params: {
          id: item.id,
        },
      });
    },
    clickGame(item) {
      this.addHistoryItem(item.main_title);
    },
    // 猜你喜欢
    async getGuessYouLike() {
      try {
        // const res = await ApiCwbIndexSearch();
        const res = await ApiV2024SearchChangeYourLike();
        this.guessYouLike = res.data.guess || [];
        // this.guessYouLike = this.mergeHotSearchAndGame(res.data);
      } catch (error) {
      } finally {
        this.$nextTick(() => {
          this.hide2();
        });
      }
    },
    // 打乱猜你喜欢
    randomSortGuessYouLike() {
      this.isRotated = true;
      this.guessYouLike = this.randomSortArray(this.guessYouLike);
    },
    animationEnd() {
      this.isRotated = false;
    },
    mergeHotSearchAndGame(data) {
      const hotSearch = data.hot_search || [];
      const hotSearchGame = data.hot_search_game || [];
      return [...hotSearch, ...hotSearchGame];
    },
    randomSortArray(arr) {
      var stack = [];
      while (arr.length) {
        var index = parseInt(Math.random() * arr.length);
        stack.push(arr[index]);
        arr.splice(index, 1);
      }
      return stack;
    },
    // 热门模块
    async getHotModuleList() {
      try {
        // const res = await ApiCwbIndexGetSearchHotData();
        const res = await ApiV2024SearchHotList();
        this.hotModuleList = res.data;
        this.hotModuleListEntries = Object.entries(this.hotModuleList).map(
          ([key, value]) => ({
            key,
            value,
          }),
        );
      } catch (error) {
      } finally {
        this.hotModuleLoadSuccess = true;
        this.isHasHotModuleList = Object.values(this.hotModuleList).some(
          array => array.length > 0,
        );
        if (!this.isHasHotModuleList) return;
        this.$nextTick(() => {
          const container = this.$refs.tapContainer;
          if (container) {
            this.resizeObserver = new ResizeObserver(() => {
              this.handleResize();
            });
            this.resizeObserver.observe(container);
          }
          this.couponSwiper = this.$refs?.couponSwiper?.$swiper;
        });
      }
    },
    async tapNav(index) {
      if (this.current != index) {
        this.current = index;
        this.resultList = [];
        this.emptyMsgInfo = '';
        await this.handleSearch(
          this.inputText,
          1,
          4,
          this.tabList[this.current].type,
        );
      }
    },
    // 打开详情页
    toDetail(item) {
      // 2024年11月11日16:18:54 game-item-4 组件中有跳转 不需要这边触发点击
      this.CLICK_EVENT(item.click_id);
      this.addHistoryItem(item.main_title);
      // classid 115 跳转外部cps详情页 40up资源游戏详情页
      const pageMap = {
        40: 'UpDetail',
        // 115: 'ExternalGameDetail',
      };
      const targetPage = pageMap[Number(item.classid)] || 'GameDetail';
      this.toPage(targetPage, { id: item.id });
    },
    // 打开合集页
    toGameCollect(item) {
      this.addHistoryItem(item.title);
      const { id } = item;
      // this.toPage('GameCollect', { id, info: item });
      this.toPage('ExternalGameCollect', { id, info: item });
    },
    // 跳转分类页
    toCategory(item) {
      this.addHistoryItem(item.main_title);
      this.toPage('Category', { info: item });
      // classid 115 跳转外部cps详情页 40up资源游戏详情页
      const pageMap = {
        40: 'UpDetail',
        // 115: 'ExternalGameDetail',
      };
      const targetPage = pageMap[Number(item.classid)] || 'GameDetail';
      this.toPage(targetPage, { id: item.id });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .van-nav-bar__title {
  max-width: 237 * @rem;
  margin: 0 90 * @rem 0 auto;
}
/deep/.nav-bar-component {
  z-index: 99999;
}
.search-page {
  .search-bar {
    box-sizing: border-box;
    padding: 0 12 * @rem 0 18 * @rem;
    width: 237 * @rem;
    height: 33 * @rem;
    border-radius: 17 * @rem;
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border: 1 * @rem solid #0fb089;
    .search-icon {
      width: 16 * @rem;
      height: 16 * @rem;
      background: url(~@/assets/images/search/search_ss.png) center center
        no-repeat;
      background-size: 16 * @rem 16 * @rem;
    }
    .search-input {
      flex: 1;
      height: 33 * @rem;
      margin-left: 7 * @rem;
      form {
        border: 0;
        outline: 0;
        display: block;
        width: 100%;
        height: 100%;
      }
      input {
        border: 0;
        outline: 0;
        display: block;
        width: 100%;
        height: 100%;
        background-color: transparent;
        font-size: 14 * @rem;
        color: #333;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .input-clear {
      width: 16 * @rem;
      height: 16 * @rem;
      .image-bg('~@/assets/images/search/search_xx.png');
      // margin: 0 12 * @rem;
    }
  }
  .right-btn {
    width: 60 * @rem;
    text-align: center;
    height: 32 * @rem;
    line-height: 32 * @rem;
    background: linear-gradient(270deg, #6ddc8c 0%, #21b98a 99%);
    border-radius: 28 * @rem;
    font-weight: 500;
    font-size: 14 * @rem;
    color: #ffffff;
  }
  .search-list {
    margin-top: 16 * @rem;
    padding: 0 18 * @rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .search-item {
      // flex: 1;
      width: 108 * @rem;
      height: 54 * @rem;
      line-height: 54 * @rem;
      background: #eefff5;
      border-radius: 8 * @rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border: 1px solid rgba(33, 185, 138, 0.12);
      .search-icon {
        display: flex;
        align-items: center;
        .icon {
          width: 16 * @rem;
          height: 14 * @rem;
        }
        .title {
          margin-left: 4 * @rem;
          height: 16 * @rem;
          font-weight: 600;
          font-size: 13 * @rem;
          color: #222222;
          line-height: 16 * @rem;
        }
      }
      .search-text {
        margin-top: 4 * @rem;
        height: 14 * @rem;
        font-weight: 400;
        font-size: 11 * @rem;
        color: #999999;
        line-height: 14 * @rem;
      }
      &:not(:first-child) {
        margin-left: 8 * @rem;
      }
    }
  }
  .search-index {
    padding-bottom: 24 * @rem;
    .section-title {
      padding: 10 * @rem 18 * @rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title-text {
        display: flex;
        align-items: center;
        > i {
          width: 18 * @rem;
          height: 18 * @rem;
          background: url(~@/assets/images/search/search_ssls.png) center center
            no-repeat;
          background-size: 13 * @rem auto;
        }
        span {
          // margin-left: 4 * @rem;
          font-size: 17 * @rem;
          color: #333333;
          font-weight: bold;
        }
      }
      .clear-history {
        display: flex;
        align-items: center;
        > i {
          width: 16 * @rem;
          height: 16 * @rem;
          background: url(~@/assets/images/search/search_clear.png) center
            center no-repeat;
          background-size: 16 * @rem auto;
        }
      }
      .hot-search-title {
        width: 79 * @rem;
        height: 20 * @rem;
        .image-bg('~@/assets/images/hot-search-title.png');
      }
    }
    .search-history {
      padding: 15 * @rem 0 5 * @rem;
      .section-title {
        padding: 10 * @rem 18 * @rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title-text {
          display: flex;
          align-items: center;
          > i {
            width: 18 * @rem;
            height: 18 * @rem;
            background: url(~@/assets/images/search/search_ssls.png) center
              center no-repeat;
            background-size: 14 * @rem auto;
          }
          span {
            // margin-left: 4 * @rem;
            font-size: 17 * @rem;
            color: #333333;
            font-weight: bold;
          }
        }
        .clear-history {
          display: flex;
          align-items: center;
          > i {
            width: 16 * @rem;
            height: 16 * @rem;
            background: url(~@/assets/images/search/search_clear.png) center
              center no-repeat;
            background-size: 16 * @rem auto;
          }
        }
        .hot-search-title {
          width: 79 * @rem;
          height: 20 * @rem;
          .image-bg('~@/assets/images/hot-search-title.png');
        }
      }
    }
    .search-youLike {
      // padding: 15 * @rem 0 5 * @rem;
      .section-title {
        padding: 10 * @rem 18 * @rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title-text {
          display: flex;
          align-items: center;
          > i {
            width: 18 * @rem;
            height: 18 * @rem;
            background: url(~@/assets/images/search/search_cnxh.png) center
              center no-repeat;
            background-size: 13 * @rem auto;
          }
          span {
            margin-left: 4 * @rem;
            font-size: 17 * @rem;
            color: #333333;
            font-weight: bold;
          }
        }
        .change-batch {
          display: flex;
          align-items: center;
          > i {
            width: 12 * @rem;
            height: 12 * @rem;
            background: url(~@/assets/images/search/search_sync.png) center
              center no-repeat;
            background-size: 12 * @rem auto;
            transition: transform 0.3s ease;
            &.rotate-icon {
              animation: rotateAnimation 0.3s linear forwards;
            }
          }
          span {
            margin-left: 5 * @rem;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #adafb8;
          }
        }
        @keyframes rotateAnimation {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
        .hot-search-title {
          width: 79 * @rem;
          height: 20 * @rem;
          .image-bg('~@/assets/images/hot-search-title.png');
        }
      }
      .section-box {
        padding: 10 * @rem 18 * @rem;
        // display: grid;
        // grid-template-columns: repeat(2, 1fr);
        // gap: 15 * @rem;
        display: flex;
        flex-wrap: wrap;
        .section-item {
          height: 33 * @rem;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 12 * @rem;
          background: #ffffff;
          margin-right: 10 * @rem;
          margin-bottom: 11 * @rem;
          border-radius: 16 * @rem;
          border: 0.5px solid #e9e9e9;
          font-weight: 400;
          color: #555555;
          text-align: left;
          img {
            margin-left: 4 * @rem;
            width: 14 * @rem;
            height: 14 * @rem;
          }
        }
        .search-up-down {
          padding: 0;
          border: 0;
          img {
            margin-left: 0;
            width: 29 * @rem;
            height: 29 * @rem;
          }
        }
      }
    }
    .hot-module-loading {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .tap-search-ranking {
      padding: 0 18 * @rem;
      overflow-x: hidden;
      .tap-slide {
        .tap-slide-wrap {
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          overflow-x: hidden;
          overflow-y: visible;
          height: 100%;
          display: -webkit-box;
          display: flex;
          -webkit-box-align: center;
          align-items: center;
          .tap-slide-content {
            display: inline-flex;
            position: relative;
            .tap-search-ranking-slide-item {
              position: relative;
              z-index: 1;
              display: -webkit-box;
              display: flex;
              -webkit-box-align: center;
              align-items: center;
              -webkit-box-pack: center;
              justify-content: center;
              font-size: 14 * @rem;
              line-height: 32 * @rem;
              color: #666666;
              &:not(:first-child) {
                margin-left: 26 * @rem;
              }
              &.tapActive {
                font-size: 16 * @rem;
                font-weight: 600;
                color: #222222;
              }
            }
            .tap-slide-line {
              position: absolute;
              bottom: 4 * @rem;
              width: 64 * @rem;
              height: 12 * @rem;
              background: linear-gradient(
                89deg,
                rgba(109, 255, 148, 0.47) 0%,
                rgba(147, 255, 109, 0.06) 99%
              );
              border-radius: 4 * @rem;
              -webkit-transform: translateX(-50%);
              transform: translate(-50%);
              -webkit-transition-duration: 0.3s;
              transition-duration: 0.3s;
              transform: translate3d(calc(-50% + 32 * @rem), 0px, 0px);
            }
          }
        }
      }
      .tap-box {
      }
      .coupon-swiper {
        padding: 16 * @rem 0 0 0;
        position: relative;
        width: auto;
        /deep/.swiper-container {
          width: 260 * @rem;
          margin-left: 0;
          margin-right: 0;
          position: relative;
          overflow: inherit;
          list-style: none;
          padding: 0;
          z-index: 1;
        }
        .list {
          width: 260 * @rem;
          height: auto;
          border-radius: 10 * @rem;
          padding: 16 * @rem 15 * @rem;
          box-sizing: border-box;

          border: 1px solid rgba(60, 210, 121, 0.1);
          .hot-module-box {
            border-radius: 17 * @rem;
            .hot-module-item {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              &:not(:first-child) {
                margin-top: 14 * @rem;
              }
              height: 36 * @rem;
              .hot-module-num {
                width: 18 * @rem;
                height: 18 * @rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 500;
                font-size: 14 * @rem;
                color: #999999;
                text-transform: none;
                &.no1 {
                  width: 18 * @rem;
                  height: 18 * @rem;
                  background: url(~@/assets/images/search/search_no1.png) center
                    center no-repeat;
                  background-size: 18 * @rem auto;
                }
                &.no2 {
                  width: 18 * @rem;
                  height: 18 * @rem;
                  background: url(~@/assets/images/search/search_no2.png) center
                    center no-repeat;
                  background-size: 18 * @rem auto;
                }
                &.no3 {
                  width: 18 * @rem;
                  height: 18 * @rem;
                  background: url(~@/assets/images/search/search_no3.png) center
                    center no-repeat;
                  background-size: 18 * @rem auto;
                }
              }
              .hot-module-img {
                margin: 0 8 * @rem 0 13 * @rem;
                img {
                  width: 36 * @rem;
                  height: 36 * @rem;
                  border-radius: 6 * @rem;
                }
              }
              .hot-module-title {
                width: 220 * @rem;
                height: 16 * @rem;
                line-height: 16 * @rem;
                font-weight: 400;
                font-size: 13 * @rem;
                color: #111111;
                text-align: left;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                &.mar_left_6 {
                  margin-left: 6 * @rem;
                }
              }
              .hot-module-icon {
                margin-left: 4 * @rem;
                img {
                  width: 15 * @rem;
                  height: 15 * @rem;
                }
              }
              .hot-module-title-left {
                margin-left: 13 * @rem;
              }
            }
          }
          .hot-module-box1 {
            background: linear-gradient(
              180deg,
              #fae7d8 0%,
              #fcf7f5 16%,
              #fcf7f5 99%
            );
          }
          .hot-module-box2 {
            background: linear-gradient(
              180deg,
              #e7f1f7 0%,
              #f5f8fa 16%,
              #f5f8fa 99%
            );
          }
          .hot-module-box3 {
            background: linear-gradient(
              180deg,
              #e8e7f7 0%,
              #f6f5fa 16%,
              #f6f5fa 99%
            );
          }
          .hot-bg-h94 {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            z-index: -99999;
            height: 94 * @rem;
            background: linear-gradient(
              173deg,
              rgba(147, 255, 109, 0.12) 0%,
              rgba(147, 255, 109, 0) 100%
            );
          }
        }
        .swiper-slide {
          width: 260 * @rem !important;
          &:not(:last-child) {
            margin-right: 18 * @rem;
          }
        }
      }
    }
    .hot-module {
      padding: 0 15 * @rem;

      .vant-tabs {
        /deep/.van-tabs__line {
          border-radius: 3 * @rem;
          width: 12 * @rem;
          height: 6 * @rem;
          background: #32b768;
          border-radius: 16 * @rem;
        }
        /deep/.van-tab__text--ellipsis {
          width: 64 * @rem;
          height: 20 * @rem;
          font-weight: 600;
          font-size: 16 * @rem;
          color: #111111;
          line-height: 20 * @rem;
        }
      }
    }
    .hot-search {
      padding: 10 * @rem 0 5 * @rem;
    }
    .hot-word {
      padding: 15 * @rem 0 5 * @rem;
    }
    .rank-list {
      padding: 0 18 * @rem;
      .rank-item {
        display: flex;
        align-items: center;
        padding: 8 * @rem 0;
        .num {
          width: 15 * @rem;
          height: 15 * @rem;
          display: flex;
          align-items: center;
          font-size: 14 * @rem;
          font-weight: 500;
          color: #c4c4c4;
          &.num1 {
            color: #fe2b1d;
          }
          &.num2 {
            color: #f8860d;
          }
          &.num3 {
            color: #fec243;
          }
        }
        .game-icon {
          width: 30 * @rem;
          height: 30 * @rem;
          border-radius: 7 * @rem;
          overflow: hidden;
          margin-left: 5 * @rem;
        }
        .game-name {
          font-size: 14 * @rem;
          color: #000000;
          margin-left: 7 * @rem;
          display: flex;
          align-items: center;
          .game-subtitle {
            box-sizing: border-box;
            border: 1 * @rem solid fade(@themeColor, 80);
            border-radius: 3 * @rem;
            font-size: 11 * @rem;
            padding: 2 * @rem 3 * @rem;
            color: @themeColor;
            margin-left: 5 * @rem;
            vertical-align: middle;
            line-height: 1;
          }
        }
      }
    }
    .word-list {
      display: flex;
      flex-wrap: wrap;
      padding: 0 18 * @rem;
      margin-top: 10 * @rem;
      .word-item {
        height: 33 * @rem;
        padding: 0 12 * @rem;
        border-radius: 16 * @rem;
        background-color: #fafafd;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 12 * @rem;
        margin-bottom: 12 * @rem;
        font-size: 14 * @rem;
        color: #222222;
        img {
          margin-right: 4 * @rem;
          width: 15 * @rem;
          height: 15 * @rem;
        }
      }
      .search-arrow-down {
        padding: 0;
        img {
          margin-right: 0;
          width: 29 * @rem;
          height: 29 * @rem;
        }
      }
    }
    .word-list1 {
      display: flex;
      flex-wrap: wrap;
      padding: 0 18 * @rem;
      margin-top: 10 * @rem;
      .word-item1 {
        height: 32 * @rem;
        padding: 0 11 * @rem;
        border-radius: 16 * @rem;
        background-color: #f7f7f7;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 12 * @rem;
        margin-bottom: 12 * @rem;
        font-size: 14 * @rem;
        color: #232323;
      }
    }
  }

  .search-result {
    box-sizing: border-box;
    height: calc(100vh - 50 * @rem - @safeAreaTop);
    height: calc(100vh - 50 * @rem - @safeAreaTopEnv);
    display: flex;
    flex-direction: column;

    .tab-bar {
      position: fixed;
      width: 100%;
      max-width: 450px;
      height: 50 * @rem;
      z-index: 200;
      top: calc(50 * @rem + @safeAreaTop);
      top: calc(50 * @rem + @safeAreaTopEnv);
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      .tab-item {
        // width: 78 * @rem;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16 * @rem;
        font-weight: 400;
        color: #797979;
        &.on {
          font-weight: 600;
          color: #000000;
          &::after {
            content: '';
            width: 14 * @rem;
            height: 4 * @rem;
            border-radius: 2 * @rem;
            background-color: #32b768;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: 0;
          }
        }
      }
      .empty-msg {
        position: absolute;
        width: 100%;
        top: 50 * @rem;
        height: 20 * @rem;
        line-height: 20 * @rem;
        background: #fff;
        left: 18 * @rem;
        color: #ff9000;
        font-size: 13 * @rem;
        padding: 4 * @rem 0 0;
      }
    }

    .game-list-box {
      // margin-top: 50 * @rem;
      padding: 0 18 * @rem;
      &.margin-top-70 {
        margin-top: 70 * @rem;
      }
      .game-list {
        // padding: 6 * @rem 0 0;
        .game-item {
          .game-item-box {
            display: flex;
            align-items: center;
            .game-btn {
              position: relative;
              /deep/.download-btn {
                width: 64 * @rem;
                height: 30 * @rem;
                background: linear-gradient(85deg, #0fb089 0%, #5be06f 100%);
                border-radius: 29 * @rem;
                font-weight: 500;
                font-size: 14 * @rem;
                color: #ffffff;
              }
            }
          }
          .gift-pack-list {
          }
          .aggregate-list {
            height: 121 * @rem;

            .aggregate-item {
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              .aggregate-img {
                width: 160 * @rem;
                height: 80 * @rem;
                border-radius: 6 * @rem;
                background-color: #eeeeee;
                img {
                  border-radius: 6 * @rem;
                }
              }
              .aggregate-title {
                margin: 5 * @rem 4 * @rem;
                width: 149 * @rem;
                font-weight: 400;
                font-size: 14 * @rem;
                color: #333333;
                line-height: 18 * @rem;
                text-align: left;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                text-overflow: ellipsis;
              }
            }
          }
        }
        .padding-10 {
          padding: 10 * @rem 0;
        }
        .coupon-item {
          padding: 14 * @rem 0 0 0;
        }
        .coupon-item1 {
          padding: 0;
        }
        &.aggregate-cart {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 0 19 * @rem;
        }
      }
    }

    .empty-box {
      margin-top: 50 * @rem;
    }
  }
  .sug-container {
    // margin-top: 50 * @rem;
    .sug-list {
      height: 100%;
      .sug-loading {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .sug-info {
        height: 100%;
        overflow: hidden;
      }
    }
  }
  .rotate_180_degrees {
    transform: rotate(180deg);
    transform-origin: center;
  }
}
</style>
