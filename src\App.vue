<template>
  <div id="app" :style="{ '--statusHeight': statusHeight }">
    <!-- 引导 -->
    <sign-dialog></sign-dialog>
    <iphone-tips></iphone-tips>
    <!-- 接口请求超时或请求错误 -->
    <template v-if="!postError">
      <!-- 主程 -->
      <keep-alive>
        <router-view
          v-if="$route.meta.keepAlive"
          :key="$route.path"
        ></router-view>
      </keep-alive>
      <router-view
        v-if="!$route.meta.keepAlive"
        :key="$route.path"
      ></router-view>
    </template>
    <post-error-popup v-else></post-error-popup>

    <!-- 实名制弹窗 -->
    <!-- 实名弹窗暂时取消 2024年7月5日18:17:38 -->
    <!-- <RealNamePopup /> -->
    <!-- 回归弹窗 取消时间 2024年8月27日14:52:25 -->
    <!-- <regression-popup></regression-popup> -->
    <!-- 下载弹窗 -->
    <yy-download-popup></yy-download-popup>
    <!-- iosBox停用通知 -->
    <stop-using-popup></stop-using-popup>
    <!-- H5游戏 -->
    <H5GamePopup />
    <!-- H5球 -->
    <Ball />
  </div>
</template>
<script>
import { mapGetters, mapActions, mapMutations } from 'vuex';
import IphoneTips from '@/components/iphone-tips';
import { baiDu } from '@/utils/tongji';
import { isWebApp, isIos } from '@/utils/userAgent';
import H5GamePopup from '@/components/h5game-popup';
import Ball from '@/components/ball';
import signDialog from '@/components/sign-dialog';
import stopUsingPopup from '@/components/stop-using-popup';
import postErrorPopup from '@/components/post-error-popup';
import { platform, authInfo, isAndroidSdk } from '@/utils/box.uni.js';
import { handleActionCode } from '@/utils/actionCode.js';
import { ApiUserSubmitInstallInfo } from '@/api/views/users';
import {
  ApiMediumSubmitWechatMedium,
  ApiMediumSubmitMediumActivity,
  ApiV2024IndexRecordedCid,
  ApiV2024IndexGetPopUp,
} from '@/api/views/system';
import {
  getQueryVariable,
  devLog,
  saveCheatCodeCallback,
  delCheatCodeCallback,
  checkUserHasAnSvipCallback,
  getSimulatorZoneInfoCallback,
  updateEmulatorCollectStatusCallback,
  simulatorGameReportTimeCallback,
  getCloudSdkInitCallback,
  getPcCloudGameInfoCallback,
  updateGameCollectStatusCallback,
  getPriorityListInfoCallback,
  createPlayTimeReporterCallback,
} from '@/utils/function.js';
import { loadWpaAbout } from '@/utils/channel.js';
// 安卓壳顶部状态栏设置
let statusHeight = '0px';
if (['android', 'androidBox'].includes(platform)) {
  // 安卓versionCode:3600以上才隐藏状态栏
  // isAndroidSdk 是安卓原生sdk
  if ((platform == 'andriod' && authInfo.versionCode < 3600) || isAndroidSdk) {
    statusHeight = '0px';
  } else {
    window.BOX.setStatusBarVisibility(false);
    statusHeight =
      window.BOX.getStatusBarHeight() / window.devicePixelRatio + 'px';
  }

  if (isIos) {
    document.body.classList.add('ios');
  } else if (platform == 'android') {
    document.body.classList.add('android');
  }
}
export default {
  data() {
    return {
      statusHeight,
    };
  },
  computed: {
    ...mapGetters({
      kefuWyLink: 'system/kefuWyLink',
      jumpGame: 'system/jumpGame',
      userInfo: 'user/userInfo',
      accessTrackingIO: 'system/accessTrackingIO',
      accessMediumH5IO: 'system/accessMediumH5IO',
      accessMediumWechatIO: 'system/accessMediumWechatIO',
      accessMediumIO: 'system/accessMediumIO',
      postError: 'system/postError',
      pageApiMap: 'system/pageApiMap',
    }),
  },
  async created() {
    this.SET_POSTERROR_INIT();
    this.setTaskListPopupShow(false);
    this.setHereItIosBoxTaskShow(false);
    this.setInitDownloadStatus();

    // 插入包相关启动图等
    loadWpaAbout();

    if (isWebApp) {
      // 防止用户在非首页的地方添加到桌面导致webapp打开不在首屏
      if (this.$router.history.current.name !== 'QualitySelect') {
        this.toPage('QualitySelect');
      }
    }
    // 获取额外数据参数
    await this.SET_INIT_DATA();
    await this.$isResolve();
    // 获取渠道广告弹窗数据，必须在SET_INIT_DATA之后执行
    await this.SET_CHANNEL_AD_POPUP();
    // 每次启动在初始化接口完成后上报
    // 特定渠道才要上报 从store里拿accessTrackingIO
    if (
      (!!this.accessTrackingIO || !!this.accessMediumH5IO) &&
      ['iosBox', 'androidBox', 'h5'].includes(platform)
    ) {
      let plat = 'ios';
      if (platform == 'androidBox') {
        plat = 'android';
      }
      try {
        ApiUserSubmitInstallInfo({ plat });
      } catch (e) {
        devLog(e);
      }
    }

    /**
     * 自家上报
     * @date 2024年7月22日14:22:28
     *
     */
    if (!!this.accessMediumIO && ['iosBox'].includes(platform)) {
      ApiMediumSubmitMediumActivity({
        uuid_b: authInfo?.idfv ?? '',
      }).then(res => {
        const medium_device_id = res.data.medium_device_id ?? '';
        this.setMediumDeviceId(medium_device_id);
      });
    }

    /**
     * 小程序广告激活 上报
     */
    let urlQuery = getQueryVariable();
    if (
      urlQuery &&
      ((urlQuery.open_id && urlQuery.clue_token) || platform === 'iosBox') &&
      !!this.accessMediumWechatIO
    ) {
      let temp_data = {
        ...urlQuery,
      };
      if (urlQuery.clue_token) temp_data.medium_key = urlQuery.clue_token;
      ApiMediumSubmitWechatMedium(temp_data);
    }

    // 首次打开时，如果是投放推荐包，跳转相应的游戏详情页 jumpGame
    let noFirstOpen = localStorage.getItem('NO_FIRST_OPEN');
    if (
      platform != 'android' &&
      platform != 'ios' &&
      !noFirstOpen &&
      this.jumpGame?.is_open == 1 &&
      this.jumpGame?.game_id
    ) {
      this.toPage('GameDetail', { id: this.jumpGame.game_id });
    }
    if (!noFirstOpen) {
      localStorage.setItem('NO_FIRST_OPEN', 1);
    }

    // 回归用户
    this.SET_REGRESSION_POPUP();

    // 用户信息
    await this.SET_USER_INFO();
  },
  mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
      window.onAction = this.onAction;
    }
    // ios马甲包
    if (platform == 'iosBox') {
      // 模拟器游戏挂载对应方法
      window.saveCheatCode = this.saveCheatCode;
      window.delCheatCode = this.delCheatCode;
      window.userMemIsSvip = this.userMemIsSvip;
      window.getSimulatorZoneInfo = this.getSimulatorZoneInfo;
      window.updateEmulatorCollectStatus = this.updateEmulatorCollectStatus;
      window.simulatorGameReportTime = this.simulatorGameReportTime;

      // PC云游戏挂载对应方法
      window.getCloudSdkInit = this.getCloudSdkInit;
      window.getPcCloudGameInfo = this.getPcCloudGameInfo;
      window.updateGameCollectStatus = this.updateGameCollectStatus;
      window.getPriorityListInfo = this.getPriorityListInfo;
      window.clickIosBoxTaskShow = this.clickIosBoxTaskShow;
      window.createPlayTimeReporter = this.createPlayTimeReporter;

      // 消息推送
      window.messagePost = this.messagePost;
      window.notificationsPost = this.notificationsPost;
    }

    // 网易客服初始化
    this.$nextTick(() => {
      let _this = this;
      (function (w, d, n, a, j) {
        w[n] =
          w[n] ||
          function () {
            (w[n].a = w[n].a || []).push(arguments);
          };
        j = d.createElement('script');
        j.async = true;
        j.src = _this.kefuWyLink;
        d.body.appendChild(j);
      })(window, document, 'ysf');
      if (ysf && platform !== 'android' && platform !== 'androidBox') {
        ysf('onready', () => {
          if (this.userInfo.username) {
            ysf('config', {
              uid: this.userInfo.username,
              name: `${this.userInfo.username}${
                this.userInfo.is_svip ? '(SVIP)' : ''
              }`,
              mobile: this.userInfo.mobile ?? '',
              groupid: this.userInfo.is_svip ? 482269080 : 482171132,
              robotShuntSwitch: 1,
            });
          } else {
            ysf('config', {
              mobile: '',
              data: '',
            });
          }
          let result = ysf('getUnreadMsg');
          if (result) {
            this.setUnReadMesNumber(result.total);
          }
          ysf('onunread', res => {
            this.setUnReadMesNumber(res.total);
          });
        });
      }
    });
    // 添加百度统计
    baiDu.fun();
    // 百度联盟
    this.baiduAd();

    // 监听网络状态
    if (platform != 'android') {
      this.checkNetworkStatus();
    }
  },
  methods: {
    handleActionCode,
    onAction(data) {
      this.setOnActionData(data);
    },
    async messagePost(data) {
      this.setPostMessageData(data);
      data = JSON.parse(data);
      await ApiV2024IndexRecordedCid(data);
    },
    notificationsPost(data) {
      data = JSON.parse(data);
      this.handleActionCode(data);
    },
    saveCheatCode(e) {
      saveCheatCodeCallback(e);
    },
    delCheatCode(e) {
      delCheatCodeCallback(e);
    },
    userMemIsSvip() {
      checkUserHasAnSvipCallback();
    },
    getSimulatorZoneInfo(e) {
      getSimulatorZoneInfoCallback(e);
    },
    updateEmulatorCollectStatus(e) {
      updateEmulatorCollectStatusCallback(e);
    },
    simulatorGameReportTime(e) {
      simulatorGameReportTimeCallback(e);
    },
    getCloudSdkInit(e) {
      getCloudSdkInitCallback(e);
    },
    getPcCloudGameInfo() {
      getPcCloudGameInfoCallback();
    },
    updateGameCollectStatus(e) {
      updateGameCollectStatusCallback(e);
    },
    getPriorityListInfo(e) {
      getPriorityListInfoCallback(e);
    },
    createPlayTimeReporter(e) {
      createPlayTimeReporterCallback(e);
    },
    clickIosBoxTaskShow() {
      this.setHereItIosBoxTaskShow(true);
    },
    onResume() {
      this.SET_USER_INFO(false);
    },
    // 百度联盟
    baiduAd() {
      let doc = document,
        h = doc.getElementsByTagName('head')[0],
        s = doc.createElement('script');
      s.async = true;
      s.src = 'https://cpro.baidustatic.com/cpro/ui/cm.js';
      h && h.insertBefore(s, h.firstChild);
    },
    checkNetworkStatus() {
      window.addEventListener('offline', () => {
        this.setPostError(true);
      });
    },
    ...mapActions({
      SET_INIT_DATA: 'system/SET_INIT_DATA',
      SET_REGRESSION_POPUP: 'user/SET_REGRESSION_POPUP',
      SET_USER_INFO: 'user/SET_USER_INFO',
      SET_CHANNEL_AD_POPUP: 'user/SET_CHANNEL_AD_POPUP',
      SET_POSTERROR_INIT: 'system/SET_POSTERROR_INIT',
    }),
    ...mapMutations({
      setInitDownloadStatus: 'game/setInitDownloadStatus',
      setMediumDeviceId: 'system/setMediumDeviceId',
      setPostMessageData: 'system/setPostMessageData',
      setTaskListPopupShow: 'game/setTaskListPopupShow',
      setHereItIosBoxTaskShow: 'game/setHereItIosBoxTaskShow',
      setPostError: 'system/setPostError',
      setOnActionData: 'system/setOnActionData',
      setUnReadMesNumber: 'user/setUnReadMesNumber',
    }),
  },
  components: {
    IphoneTips,
    // RealNamePopup,
    signDialog,
    // RegressionPopup,
    stopUsingPopup,
    H5GamePopup,
    Ball,
    postErrorPopup,
  },
};
</script>
