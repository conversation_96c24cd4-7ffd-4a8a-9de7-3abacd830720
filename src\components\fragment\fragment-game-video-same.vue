<template>
  <!-- 视频同款 -->
  <div class="game-container" @click="goToGame(info.game, 0)">
    <div class="title-bar">
      <div class="title">{{ info.header_title }}</div>
      <div class="more-btn" @click="clickMore" v-if="info.action_code"
        >更多<span></span
      ></div>
    </div>
    <div
      class="game-bar"
      v-sensors-exposure="gameExposure(info.game_list[0], 0)"
    >
      <game-item-4
        class="game-item"
        :gameInfo="info.game_list[0]"
        :iconSize="72"
        :showHot="true"
      ></game-item-4>
    </div>
  </div>
</template>

<script>
import { BOX_goToGame } from '@/utils/box.uni.js';
import { handleActionCode } from '@/utils/actionCode.js';
export default {
  name: 'fragmentGameFirst',
  props: {
    info: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  methods: {
    gameExposure(item, index) {
      return {
        'event-name': 'game_exposure',
        'property-page_name': this.$sensorsPageGet(),
        'property-section_name': this.info.header_title || '暂无',
        'property-game_id': `${item.id}`,
        'property-game_name': item.title,
        'property-game_index': `${index}`,
      };
    },
    clickMore() {
      handleActionCode(this.info);
    },

    goToGame(item, index) {
      // 神策埋点
      this.$sensorsTrack('game_click', {
        page_name: this.$sensorsPageGet(),
        section_name: this.info.header_title || '暂无',
        game_id: `${item.id}`,
        game_name: item.title,
        game_index: `${index}`,
      });
      BOX_goToGame(
        {
          params: {
            id: item.id,
            gameInfo: item,
          },
        },
        { id: item.id },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.game-container {
  width: 351 * @rem;
  height: 127 * @rem;
  background-image: url('~@/assets/images/video-same-top-bg.png');
  background-size: 351 * @rem 127 * @rem;
  background-repeat: no-repeat;
  background-position: center top;
  margin: 0 auto;
  position: relative;
  .title-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 12 * @rem;
    padding-top: 7 * @rem;
    .title {
      font-size: 15 * @rem;
      color: #2bbe88;
      height: 28 * @rem;
      display: flex;
      align-items: center;
    }
    .more-btn {
      width: 98 * @rem;
      height: 28 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 13 * @rem;
      color: #2bbe88;
      span {
        width: 7 * @rem;
        height: 9 * @rem;
        background: url(~@/assets/images/arrow-right-green.png) center center
          no-repeat;
        background-size: 7 * @rem 9 * @rem;
        margin-left: 5 * @rem;
      }
    }
  }
  .game-bar {
    box-sizing: border-box;
    width: 100%;
    padding: 0 12 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
