<template>
  <van-dialog
    v-model="show"
    :showConfirmButton="false"
    :lockScroll="false"
    class="popup-dialog"
  >
    <div class="popup-container">
      <div class="content-box">
        <div class="title">{{ content.title }}</div>
        <div class="content" v-html="content.txt1"> </div>
      </div>
      <div class="btn-box">
        <div class="confirm btn" @click="handleConfirm()">确定</div>
      </div>
    </div>
  </van-dialog>
</template>

<script>
import { handleActionCode } from '@/utils/actionCode.js';
import { ApiV2024IndexPopcloseLog } from '@/api/views/system.js';

export default {
  data() {
    return {
      show: false,
      content: {},
    };
  },
  methods: {
    async handleConfirm() {
      this.show = false;
      try {
        await ApiV2024IndexPopcloseLog({
          type: this.content.type,
          pop_id: this.content.pop_id,
          pop_type: this.content.pop_type,
        });
      } catch (error) {
      } finally {
        handleActionCode(this.content.action);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.popup-dialog {
  width: 300 * @rem;
  height: 216 * @rem;
  background: #ffffff;
  border-radius: 16 * @rem;
  z-index: 3001 !important;
  .popup-container {
    height: 216 * @rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .content-box {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      .title {
        padding: 28 * @rem 0 0 0;
        font-weight: 600;
        font-size: 18 * @rem;
        color: #191b1f;
      }
      .content {
        padding: 28 * @rem 0 0 0;
        width: 238 * @rem;
        font-weight: 400;
        font-size: 15 * @rem;
        color: #60666c;
        text-align: center;
        line-height: 22 * @rem;
      }
    }

    .btn-box {
      margin-top: 21 * @rem;
      border-top: 1px solid #f0f1f5;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 48 * @rem;
      width: 100%;
      .confirm {
        flex: 1;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #191b1f;
      }
    }
  }
}
</style>
