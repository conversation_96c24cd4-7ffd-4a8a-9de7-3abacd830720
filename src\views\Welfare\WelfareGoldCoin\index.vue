<template>
  <div class="gold-coin-exchange-page">
    <div class="main">
      <!-- 兑换平台币 -->
      <div class="exchange-container" v-if="exchangeList.length">
        <div class="exchange-title">
          <img
            src="@/assets/images/welfare/welfare-center/task-title-8.png"
            alt=""
          />
        </div>
        <div
          class="exchange-list"
          :class="{ 'pd-15': exchangeList.length > 4 ? false : true }"
        >
          <div
            class="exchange-item"
            v-for="(item, index) in visibleExchangeList"
            :key="index"
            @click="goToExchangeDetail(item)"
          >
            <div class="icon">
              <img :src="item.img" :alt="item.title" />
            </div>
            <div class="content">
              <div class="title">{{ item.title }}</div>
              <div class="gold">
                {{ $t('消耗') }}<span>{{ item.gold }}</span
                >{{ $t('金币') }}
              </div>
            </div>
            <div class="exchange-box">
              <div
                class="exchange-btn"
                v-if="Number(item.exchange_status) == 1 && item.inventory > 0"
              >
                {{ $t('立即兑换') }}
              </div>
              <div class="exchange-btn no" v-else-if="item.inventory == 0">
                {{ $t('来晚啦') }}
              </div>
              <div class="exchange-btn no" v-else>
                {{ $t('立即兑换') }}
              </div>
              <div class="surplus-num">
                {{ $t('剩余') }}({{
                  userInfo.token ? item.inventory : max_Num
                }}/{{ max_Num }})
              </div>
            </div>
          </div>
        </div>
        <div
          class="exchange-more-btn"
          v-if="exchangeList.length > 4"
          @click="showPtbMoreItems()"
        >
          <span class="describe">{{
            showFourPtbFiveOnly ? '收起' : '展开查看更多'
          }}</span>
          <span class="icon" :class="{ 'is-down': showFourPtbFiveOnly }"></span>
        </div>
      </div>
      <!-- 兑换游戏道具 -->
      <div class="prop-container" ref="propContainer" v-if="gameList.length">
        <div class="prop-title">
          <img
            src="@/assets/images/welfare/welfare-center/task-title-9.png"
            alt=""
          />
        </div>
        <div class="prop-more-game">
          <div class="more-game-list">
            <swiper
              id="navListSwiper"
              ref="navListSwiper"
              :options="swiperOptions"
              :auto-update="true"
              style="width: 100%; margin: 0 auto"
              v-if="gameList.length > 0"
            >
              <swiper-slide
                class="more-game-item"
                v-for="game in gameList"
                :key="game.id"
                :class="{ active: isActiveIndex === game.id }"
              >
                <div class="describe-icon">
                  <img :src="game.titlepic" alt="" />
                </div>
                <div class="describe-list" v-show="isActiveIndex === game.id">
                  <div class="describe-title">
                    <div class="title-content text-scroll">
                      <div class="title-text">{{ game.title }}</div>
                      <div class="subtitle" v-if="game.subtitle">{{
                        game.subtitle
                      }}</div>
                      <div class="title-text">{{ game.title }}</div>
                      <div class="subtitle" v-if="game.subtitle">{{
                        game.subtitle
                      }}</div>
                    </div>
                  </div>

                  <div
                    class="discount-tag"
                    v-if="Number(game.pay_rebate / 10) < 10"
                  >
                    <img
                      class="discount-icon"
                      src="@/assets/images/games/discount-normal.png"
                    />
                    <div class="discount-text"
                      ><span>{{ game.pay_rebate / 10 }}</span
                      >折直充</div
                    >
                  </div>
                </div>
              </swiper-slide>
            </swiper>
          </div>

          <div
            class="more-game-btn"
            v-if="gameList.length"
            @click.stop="moreGameBtn()"
          ></div>
        </div>
        <div
          class="prop-list"
          :class="{ 'pd-15': cardGameList.length > 4 ? false : true }"
        >
          <div
            class="prop-item"
            v-for="item in visibleCardGameList"
            :key="item.id"
          >
            <div class="icon">
              <img :src="item.titlepic" :alt="item.title" />
            </div>
            <div class="content">
              <marquee-text class="title" :text="item.title"></marquee-text>
              <div class="gold">
                消耗<span>{{ item.need_gold }}</span
                >金币
              </div>
            </div>
            <div class="prop-btn" @click="redeemNow(item)">立即兑换</div>
            <div class="limit_sub" v-if="item.redeem_num_text">
              {{ item.redeem_num_text }}
            </div>
          </div>
        </div>
        <div
          class="prop-more-btn"
          v-if="cardGameList.length > 4"
          @click="showGamePropMoreItems()"
        >
          <span class="describe">{{
            showFourGamePopupFiveOnly ? '收起' : '展开查看更多'
          }}</span>
          <span
            class="icon"
            :class="{ 'is-down': showFourGamePopupFiveOnly }"
          ></span>
        </div>
      </div>
      <!-- 金币活动 -->
      <div class="activity-container">
        <div class="activity-title">
          <img
            src="@/assets/images/welfare/welfare-center/task-title-10.png"
            alt=""
          />
        </div>
        <div class="activity-list">
          <div class="activity-item btn" @click="goToGoldGamble">
            <img
              src="@/assets/images/recharge/activity-gold-gameble.png"
              alt=""
            />
          </div>
          <div class="activity-item btn" @click="goToTurnTable">
            <img
              src="@/assets/images/recharge/activity-turn-table.png"
              alt=""
            />
          </div>
        </div>
      </div>
    </div>
    <!-- 兑换游戏道具-更多游戏Popup -->
    <more-game-popup
      :show.sync="moreGamePopupShow"
      @send-data="receiveDataFromChild"
    ></more-game-popup>
    <!-- 创建小号提示弹窗 -->
    <xh-create-tip-dialog
      :show.sync="createDialogShow"
      :id="Number(this.exchangeGameInfo.game_id)"
    ></xh-create-tip-dialog>
    <!-- 复制礼包弹窗 -->
    <van-dialog
      v-model="copyDialogShow"
      :close-on-click-overlay="true"
      message-align="left"
      :lock-scroll="false"
      class="copy-dialog"
      :show-confirm-button="false"
    >
      <div class="title">
        <div class="title-icon"></div>
        <div class="title-text">{{ $t('礼包码') }}</div>
      </div>
      <div class="cardpass">{{ cardInfo.cardpass }}</div>
      <div class="desc">{{ introduction }}</div>
      <div class="copy-btn btn" @click="copy(cardInfo.cardpass)">
        {{ $t('复制礼包码') }}
      </div>
    </van-dialog>
    <!-- 小号选择弹窗 -->
    <van-dialog
      v-model="xhDialogShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="xh-dialog"
      :close-on-click-overlay="true"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">{{ $t('请选择当前游戏小号') }}</div>
        <div class="center">
          <div class="left">{{ $t('小号') }}</div>
          <div class="right">
            <div class="text" @click="xiaohaoListShow = !xiaohaoListShow">
              <span v-if="xiaohaoList.length">{{
                currentXiaohao.nickname
              }}</span>
              <span
                class="more-text-icon"
                :class="{ on: xiaohaoListShow }"
              ></span>
            </div>
            <div
              class="xiaohao-list"
              :class="{ on: xiaohaoListShow }"
              v-if="xiaohaoList.length"
            >
              <div
                class="xiaohao-item"
                v-for="(item, index) in xiaohaoList"
                :key="index"
                @click="xiaoHaoListClick(item)"
              >
                {{ item.nickname }}
              </div>
            </div>
          </div>
        </div>
        <div class="dialog-bottom-bar">
          <div class="cancel btn" @click="closeXiaohaoDialog">
            {{ $t('取消') }}
          </div>
          <div class="confirm btn" @click="chooseXiaohao">{{ $t('确定') }}</div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import {
  ApigoldToPtbExchangeList,
  ApiGameGetGameCardList,
  ApiGameGetCardGameList,
} from '@/api/views/gold.js';
import { ApiCardRead, ApiCardGet } from '@/api/views/gift.js';
import { mapActions, mapGetters } from 'vuex';
import {
  platform,
  BOX_openInNewNavWindow,
  BOX_showActivity,
  BOX_openInNewWindow,
  BOX_takeGift,
  BOX_goToGame,
  BOX_login,
} from '@/utils/box.uni.js';
import xhCreateTipDialog from '@/components/xh-create-tip-dialog/index.vue';
import MoreGamePopup from './components/more-game-popup/index.vue';
import { ApiXiaohaoMyListByGameId } from '@/api/views/xiaohao.js';
import { themeColorLess } from '@/common/styles/_variable.less';
import h5Page from '@/utils/h5Page';
export default {
  name: 'GoldCoinExchange',
  components: {
    MoreGamePopup,
    xhCreateTipDialog,
  },
  data() {
    let self = this;
    return {
      exchangeList: [], // 兑换平台币信息
      visibleExchangeList: [], // 展示兑换平台币信息
      showFourPtbFiveOnly: false, // 平台币展开更多
      max_Num: 2000,
      gameList: [],
      newAddGameList: [],
      gameId: 0, //兑换游戏道具的首款游戏
      cardGameList: [], // 兑换游戏道具
      visibleCardGameList: [], // 展示兑换游戏道具
      gold_card_list: [], // 三个游戏数据集合
      showFourGamePopupFiveOnly: false, // 游戏道具展开更多
      moreGamePopupShow: false, // 更多游戏Popup
      copyScrollTop: 0,
      isActiveIndex: 0,
      flag: true,
      swiperOptions: {
        slidesPerView: 'auto',
        freeMode: true,
        freeModeMomentumRatio: 0.3, // 运动惯量
        spaceBetween: 10,
        on: {
          tap(ev) {
            if (self.flag) {
              setTimeout(() => {
                let nav = self.gameList[this.clickedIndex];
                if (!nav) {
                  self.flag = true;
                  return;
                }
                if (self.isActiveIndex === nav.id) {
                  // 打开详情页
                  BOX_goToGame(
                    {
                      params: {
                        id: nav.id,
                      },
                    },
                    { id: nav.id },
                  );
                }
                self.isActiveIndex = nav.id;
                self.GetGameCardList();
                self.xiaohaoList = [];
                self.currentXiaohao = {};
                self.cardInfo = {};
                self.flag = true;
              }, 100);
            }
            self.flag = false;
          },
        },
      },
      exchangeGameInfo: [],
      copyDialogShow: false, //复制礼包弹窗
      xhDialogShow: false, //小号选择弹窗
      createDialogShow: false, // 创建小号提示弹窗
      xiaohaoList: [], //小号列表
      xiaohaoListShow: false, //显示小号列表
      currentXiaohao: {}, //当前选择小号
      cardInfo: {}, // 礼包码信息
    };
  },
  async activated() {
    this.init();
  },
  watch: {
    $route(to, from) {
      if (
        platform == 'android' &&
        from.name == to.name &&
        to.name == 'WelfareGoldCoinExchange'
      ) {
        this.init();
      }
    },
  },

  methods: {
    async init() {
      this.gameId = this.$route.params.extra_id;
      this.SET_USER_INFO(true);
      await this.getExchangeList();
      await this.GetGameCardList();
      if (this.$route.params.type == 1 && this.gameId) {
        this.toGameExchange();
      }
    },
    toGameExchange() {
      this.$nextTick(() => {
        let top =
          document.querySelectorAll('.gold-bar')[0].offsetHeight +
          document.querySelectorAll('.tab-bar')[0].offsetHeight;
        let distance = this.$refs.propContainer.offsetTop + top - 30;
        window.scrollTo({
          left: 0,
          top: distance,
          behavior: 'smooth',
        });
      });
    },
    toDetail(item) {
      this.CLICK_EVENT(item.classid);
      BOX_openInNewWindow(
        { name: 'GameDetail' },
        { url: `${window.location.origin}/#/game_detail/${item.id}` },
      );
    },
    // 兑换平台币
    goToExchangeDetail(item) {
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      BOX_openInNewWindow(
        { name: 'ExchangePtb', params: { id: item.id } },
        {
          url: `${window.location.origin}/#/exchange_ptb/${item.id}`,
        },
      );
      // this.toPage('ExchangePtb', { id: item.id });
    },
    async getExchangeList() {
      let params = {};
      if (this.gameId) {
        params.game_id = this.gameId;
      }
      const res = await ApigoldToPtbExchangeList(params);
      this.exchangeList = res.data.list;
      this.gold_card_list = res.data.card_list.gold_card_list;
      this.cardGameList = this.gold_card_list.map(item => item.card_info);
      this.gameList = this.gold_card_list.map(item => item.game_info);
      this.isActiveIndex = this.gameList[0].id;
      this.visibleExchangeList = this.filteredExchangeList;
    },
    goToGoldGamble() {
      BOX_openInNewWindow(
        { name: 'GoldGamble' },
        { url: `${window.location.origin}/#/gold_gamble` },
      );
    },
    goToTurnTable() {
      BOX_showActivity({ name: 'TurnTable' }, { page: 'jbzp' });
    },
    goToSavingsSecret() {
      BOX_openInNewWindow(
        { name: 'SavingsSecret' },
        { url: `${window.location.origin}/#/savings_secret` },
      );
    },
    showPtbMoreItems() {
      this.showFourPtbFiveOnly = !this.showFourPtbFiveOnly;
      if (!this.showFourPtbFiveOnly) {
        this.visibleExchangeList = this.filteredExchangeList;
      } else {
        this.visibleExchangeList = this.exchangeList;
      }
    },
    // 兑换游戏道具
    showGamePropMoreItems() {
      this.showFourGamePopupFiveOnly = !this.showFourGamePopupFiveOnly;
      if (!this.showFourGamePopupFiveOnly) {
        this.visibleCardGameList = this.filteredGamePropList;
      } else {
        this.visibleCardGameList = this.cardGameList;
      }
    },
    async GetGameCardList() {
      const res = await ApiGameGetGameCardList({
        id: this.isActiveIndex,
      });
      this.cardGameList = res.data.list;
      this.visibleCardGameList = this.filteredGamePropList;
    },
    receiveDataFromChild(data) {
      this.xiaohaoList = [];
      this.currentXiaohao = {};
      this.cardInfo = {};
      // 接收子组件发送的数据
      this.newAddGameList = data;
      if (this.newAddGameList) {
        // 判断是否有新游戏
        let isNewGameAdded = false;
        this.gameList.forEach(item => {
          if (item.id === this.newAddGameList.id) {
            isNewGameAdded = true;
          }
        });
        if (!isNewGameAdded) {
          this.gameList = [this.newAddGameList, ...this.gameList];
          this.isActiveIndex = this.newAddGameList.id;
        } else {
          this.isActiveIndex = this.newAddGameList.id;
        }
        this.GetGameCardList();
      }
    },
    // 立即兑换
    async redeemNow(item) {
      // 先判断是否是svip
      if (!this.userInfo.is_svip) {
        this.$dialog
          .confirm({
            message: '抱歉，您当前尚未开通SVIP会员，\n请先开通SVIP会员',
            confirmButtonText: '前往开通',
            cancelButtonText: '取消',
            confirmButtonColor: themeColorLess,
            lockScroll: false,
          })
          .then(() => {
            BOX_openInNewWindow({ name: 'Svip' }, { url: h5Page.svip_url });
          });
        return;
      }

      if (this.userInfo.gold < item.need_gold) {
        this.$toast.clear();
        this.$dialog
          .confirm({
            message: ' 抱歉，您当前金币不足，\n可以前往赚取更多金币再来哦～',
            confirmButtonText: this.$t('去赚金币'),
            cancelButtonText: this.$t('取消'),
            confirmButtonColor: themeColorLess,
            lockScroll: false,
          })
          .then(() => {
            BOX_openInNewWindow(
              { name: 'GoldCoin' },
              { url: h5Page.renwudating },
            );
          });
      } else {
        if (platform == 'android') {
          BOX_takeGift(item);
          return;
        }
        this.exchangeGameInfo = item;
        // 先判断是否登录，未登录则跳转登录页
        if (!this.userInfo.token) {
          this.$router.push({
            name: 'PhoneLogin',
          });
        } else {
          await this.getXhList();
          // 判断该游戏是否有小号，没有则弹出对话框
          if (!this.xiaohaoList.length) {
            this.createDialogShow = true;
            return false;
          }
          // 只有一个小号直接领取
          if (this.xiaohaoList.length == 1) {
            this.currentXiaohao = this.xiaohaoList[0];
            this.$toast.loading({
              message: this.$t('加载中...'),
            });
            try {
              this.$nextTick(async () => {
                const getRes = await ApiCardGet({
                  cardId: this.exchangeGameInfo.id,
                  xhId: this.currentXiaohao.id,
                });
                this.cardInfo = getRes.data;
                this.SET_USER_INFO();
                this.$toast.clear();
                this.copyDialogShow = true;

                // 神策埋点
                this.$sensorsTrack('game_rewards_claim', {
                  game_id: `${this.exchangeGameInfo.game_id}`,
                  adv_id: '暂无',
                  game_name: this.exchangeGameInfo.titlegame,
                  game_type: `${this.exchangeGameInfo.classid}`,
                  game_size: '暂无',
                  reward_type: this.exchangeGameInfo.title, // 传礼包名称
                  data_source: this.$sensorsChainGet(),
                });
              });
            } catch (e) {
              this.$toast(e.msg);
            }
            return false;
          }
          // 弹出小号选择框
          this.xiaohaoListShow = false;
          this.xhDialogShow = true;
        }
      }
    },
    // 点击选择小号
    xiaoHaoListClick(item) {
      this.currentXiaohao = item;
      this.xiaohaoListShow = false;
    },
    // 关闭选择小号弹窗
    closeXiaohaoDialog() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
    },
    // 选择完小号执行领取操作
    async chooseXiaohao() {
      this.xiaohaoListShow = false;
      this.xhDialogShow = false;
      // 开始领取操作
      let params = {
        gameId: this.exchangeGameInfo.game_id,
        xhId: this.currentXiaohao.id,
        cardId: this.exchangeGameInfo.id,
      };
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      try {
        this.$nextTick(async () => {
          const getRes = await ApiCardGet({
            cardId: this.exchangeGameInfo.id,
            xhId: this.currentXiaohao.id,
          });
          this.cardInfo = getRes.data;
          this.SET_USER_INFO();
          this.$toast.clear();
          this.copyDialogShow = true;

          // 神策埋点
          this.$sensorsTrack('game_rewards_claim', {
            game_id: `${this.exchangeGameInfo.game_id}`,
            adv_id: '暂无',
            game_name: this.exchangeGameInfo.titlegame,
            game_type: `${this.exchangeGameInfo.classid}`,
            game_size: '暂无',
            reward_type: this.exchangeGameInfo.title, // 传礼包名称
            data_source: this.$sensorsChainGet(),
          });
        });
      } catch (e) {
        this.$toast(e.msg);
      }
    },
    // 获取小号列表
    async getXhList() {
      const res = await ApiXiaohaoMyListByGameId({
        gameId: this.exchangeGameInfo.game_id,
      });
      const { list, text } = res.data;
      if (list && list.length) {
        this.xiaohaoList = list;
      }
      // 如果没有选择的小号，默认选择第一个
      if (!this.currentXiaohao?.id) {
        this.currentXiaohao = this.xiaohaoList[0];
      }
    },
    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast(this.$t('复制成功'));
          this.copyDialogShow = false;
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
    // 更多游戏
    moreGameBtn() {
      this.moreGamePopupShow = true;
    },
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
  },
  computed: {
    filteredExchangeList() {
      return this.exchangeList.slice(
        0,
        !this.showFourPtbFiveOnly ? 4 : this.exchangeList.length,
      );
    },
    filteredGamePropList() {
      return this.cardGameList.slice(
        0,
        !this.showFourGamePopupFiveOnly ? 4 : this.cardGameList.length,
      );
    },
    ...mapGetters({
      userInfo: 'user/userInfo',
    }),
    introduction() {
      return `${this.$t('使用说明')}：${this.cardInfo.cardtext}`;
    },
  },
  // watch: {
  //   moreGamePopupShow(val) {
  //     if (val) {
  //       this.$modalHelper.afterOpen(); // 打开禁用
  //     } else {
  //       this.$modalHelper.beforeClose(); // 关闭禁用
  //     }
  //   },
  // },
};
</script>

<style lang="less" scoped>
.gold-coin-exchange-page {
  .main {
    background-color: #f7f8fa;
    padding-bottom: 20 * @rem;

    .exchange-container {
      width: 351 * @rem;
      margin: 0 auto;
      background: #fff;
      border-radius: 12 * @rem;
      .exchange-title {
        padding: 12 * @rem 12 * @rem 0;
        img {
          width: auto;
          height: 26 * @rem;
          object-fit: fill;
        }
      }
      .exchange-list {
        .exchange-item {
          box-sizing: border-box;
          padding: 0 12 * @rem 0;
          display: flex;
          align-items: center;
          width: 331 * @rem;
          height: 70 * @rem;
          background-color: #f7f8fa;
          border-radius: 8 * @rem;
          margin: 10 * @rem auto 0;
          .icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45 * @rem;
            height: 45 * @rem;
            border-radius: 8 * @rem;
            background: linear-gradient(
              180deg,
              #ffeabf 0%,
              rgba(255, 234, 191, 0.4) 100%
            );

            img {
              width: 41 * @rem;
              height: auto;
              margin-top: -4 * @rem;
            }
          }
          .content {
            margin-left: 8 * @rem;
            flex: 1;
            min-width: 0;
            .title {
              font-size: 14 * @rem;
              color: #333333;
              line-height: 18 * @rem;
              font-weight: 600;
            }
            .gold {
              margin-top: 8 * @rem;
              font-size: 12 * @rem;
              color: #777777;
              line-height: 15 * @rem;
              span {
                color: #ff7247;
              }
            }
          }
          .exchange-box {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            .exchange-btn {
              width: 64 * @rem;
              height: 24 * @rem;
              background: #1cce94;
              border-radius: 21 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #fff;
              &.no {
                background: #cfd3d8;
              }
            }
            .surplus-num {
              margin-top: 8 * @rem;
              height: 14 * @rem;
              font-weight: 400;
              font-size: 11 * @rem;
              color: #777777;
              line-height: 14 * @rem;
              text-align: right;
              font-style: normal;
              text-transform: none;
            }
          }
        }
      }
      .exchange-more-btn {
        height: 50 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        .describe {
          color: #777777;
          font-size: 12 * @rem;
        }
        .icon {
          background: url(~@/assets/images/welfare/welfare-center/gold-coin-down-icon1.png);
          width: 10 * @rem;
          height: 8 * @rem;
          margin-left: 4 * @rem;
          background-size: 100% 8 * @rem;
          &.is-down {
            transition: transform 0.3s ease-in-out;
            transform: rotate(180deg);
          }
        }
      }
    }
    .prop-container {
      width: 351 * @rem;
      margin: 16 * @rem auto 0;
      background: #fff;
      border-radius: 12 * @rem;
      .prop-title {
        padding: 12 * @rem 12 * @rem 0;
        img {
          width: auto;
          height: 26 * @rem;
          object-fit: fill;
        }
      }
      .prop-more-game {
        padding: 0 12 * @rem 0;
        display: flex;
        align-items: flex-start;
        justify-content: space-around;
        position: relative;
        margin-top: 16 * @rem;
        .more-game-list {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          overflow-y: auto;
          /deep/.swiper-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            transition-property: transform;
            box-sizing: content-box;
          }
          .more-game-item {
            width: 54 * @rem;
            flex-shrink: 0;
            .describe-icon {
              width: 54 * @rem;
              height: 54 * @rem;
            }
            .describe-list {
              .describe-title {
                display: flex;
                align-items: center;
              }
            }
            &:not(:first-child) {
              margin-left: 5 * @rem;
            }
          }
          .active {
            display: flex;
            align-items: center;
            justify-content: space-around;
            width: 192 * @rem;
            height: 66 * @rem;
            background: linear-gradient(
              180deg,
              #ecf8ff 0%,
              rgba(244, 245, 247, 0) 100%
            );
            border: 1 * @rem solid #cbe3ff;
            border-radius: 8 * @rem;
            padding: 0 10 * @rem;
            margin-right: 12 * @rem;
            box-sizing: border-box;
            .describe-icon {
              width: 46 * @rem;
              height: 46 * @rem;
            }
            .describe-list {
              margin-left: 9 * @rem;
              min-width: 0;
              flex: 1;
              .describe-title {
                display: flex;
                align-items: center;
                overflow: hidden;
                .title-content {
                  display: flex;
                  align-items: center;
                  &.text-scroll {
                    flex-shrink: 0;
                    flex-grow: 1;
                    white-space: nowrap;
                    animation: scroll-left 5s linear forwards infinite;
                  }
                  @keyframes scroll-left {
                    0% {
                      transform: translateX(0%);
                    }
                    20% {
                      transform: translateX(0%);
                    }
                    100% {
                      transform: translateX(-50%);
                    }
                  }

                  .title-text {
                    flex: 1;
                    min-width: 0;
                    height: 18 * @rem;
                    font-weight: 600;
                    font-size: 14 * @rem;
                    color: #242840;
                    line-height: 18 * @rem;
                  }
                  .subtitle {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-sizing: border-box;
                    border-radius: 4 * @rem;
                    height: 15 * @rem;
                    margin-left: 4 * @rem;
                    font-size: 10 * @rem;
                    color: #93999f;
                    font-weight: 400;
                    line-height: 1;
                    white-space: nowrap;
                    padding: 1 * @rem 4 * @rem;
                    border: 1 * @rem solid #e3e5e8;
                    margin-right: 20 * @rem;
                  }
                }
              }
              .discount-tag {
                display: flex;
                align-items: center;
                width: fit-content;
                margin-top: 7 * @rem;
                flex-shrink: 0;

                .discount-icon {
                  width: 30 * @rem;
                  height: 18 * @rem;
                  position: relative;
                  z-index: 1;
                  &.discount-01 {
                    width: 49 * @rem;
                  }
                }
                .discount-text {
                  display: flex;
                  align-items: center;
                  height: 18 * @rem;
                  padding-right: 4 * @rem;
                  flex: 1;
                  min-width: 0;
                  font-size: 11 * @rem;
                  color: #ff6649;
                  white-space: nowrap;
                  background-color: #fff5ed;
                  border-radius: 0 2 * @rem 2 * @rem 0;
                  margin-left: -5 * @rem;
                  padding-left: 5 * @rem;
                }
              }
              .describe-discount {
                margin-top: 5 * @rem;
                width: 90 * @rem;
                height: 17 * @rem;
                border-radius: 2 * @rem;
                border: 0.5 * @rem solid #ff7171;
                display: flex;
                align-items: center;
                .left {
                  height: 100%;
                  width: 42 * @rem;
                  background: #ffecec;

                  font-weight: 400;
                  font-size: 10 * @rem;
                  color: #ff6b69;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 3 * @rem;
                  box-sizing: border-box;
                }
                .right {
                  height: 100%;
                  width: 100%;

                  font-weight: 400;
                  font-size: 10 * @rem;
                  color: #ff6b69;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                }
              }
            }
            &:not(:first-child) {
              margin-left: 5 * @rem;
            }
          }
        }

        .more-game-btn {
          position: absolute;
          top: 0;
          right: 0;
          background: url(~@/assets/images/welfare/welfare-center/more-game-btn1.png)
            no-repeat 0 0;
          background-size: 26 * @rem 64 * @rem;
          width: 26 * @rem;
          height: 64 * @rem;
          z-index: 9;
        }
      }
      .prop-list {
        padding: 5 * @rem 0 0;
        &.pd-15 {
          padding: 5 * @rem 0 15 * @rem;
        }
        .prop-item {
          box-sizing: border-box;
          padding: 0 12 * @rem;
          display: flex;
          align-items: center;
          width: 327 * @rem;
          height: 80 * @rem;
          background-color: #f7f8fa;
          border-radius: 8 * @rem;
          margin: 10 * @rem auto 0;
          position: relative;
          .icon {
            width: 45 * @rem;
            height: 45 * @rem;
          }
          .content {
            margin-left: 8 * @rem;
            flex: 1;
            min-width: 0;
            .title {
              width: 170 * @rem;
              font-size: 14 * @rem;
              color: #333333;
              line-height: 18 * @rem;
              font-weight: 600;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .gold {
              margin-top: 8 * @rem;
              font-size: 12 * @rem;
              color: #777777;
              line-height: 15 * @rem;
              span {
                color: #ff7247;
              }
            }
          }
          .prop-btn {
            width: 64 * @rem;
            height: 24 * @rem;
            background: #1cce94;
            border-radius: 21 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 11 * @rem;
            position: absolute;
            right: 12 * @rem;
            bottom: 18 * @rem;
            &.no {
              background: #ccc;
            }
          }
          .limit_sub {
            position: absolute;
            top: 0;
            right: 0;
            background: url(~@/assets/images/welfare/welfare-center/gold-coin-limit_sub1.png)
              no-repeat 0 0;
            background-size: 60 * @rem 22 * @rem;
            width: 60 * @rem;
            height: 22 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #60666c;
            font-size: 10 * @rem;
          }
        }
      }
      .prop-more-btn {
        height: 50 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        .describe {
          color: #777777;
          font-size: 12 * @rem;
        }
        .icon {
          background: url(~@/assets/images/welfare/welfare-center/gold-coin-down-icon1.png);
          width: 10 * @rem;
          height: 8 * @rem;
          margin-left: 4 * @rem;
          background-size: 100% 8 * @rem;
          &.is-down {
            transition: transform 0.3s ease-in-out;
            transform: rotate(180deg);
          }
        }
      }
    }
    .activity-container {
      width: 351 * @rem;
      margin: 16 * @rem auto 0;
      background: #fff;
      border-radius: 12 * @rem;
      .activity-title {
        padding: 12 * @rem 12 * @rem 0;
        img {
          width: auto;
          height: 26 * @rem;
          object-fit: fill;
        }
      }
      .activity-list {
        display: flex;
        padding: 12 * @rem 12 * @rem 16 * @rem;
        align-items: center;
        justify-content: space-between;
        .activity-item {
          width: 160 * @rem;
          height: 99 * @rem;
        }
      }
    }
  }
  .xh-dialog {
    width: 244 * @rem;
    background: transparent;
    overflow: visible;
    .logo-icon {
      width: 244 * @rem;
      height: 37 * @rem;
      .image-bg('~@/assets/images/games/dialog-logo.png');
      margin: 0 auto;
      position: relative;
      z-index: 3;
    }
    .dialog-content {
      box-sizing: border-box;
      position: relative;
      width: 244 * @rem;
      background-color: #fff;
      border-radius: 20 * @rem;
      margin-top: -4 * @rem;
      z-index: 2;
      padding: 16 * @rem 10 * @rem 19 * @rem;
      .title {
        font-size: 16 * @rem;
        color: #000000;
        font-weight: 600;
        text-align: center;
        line-height: 25 * @rem;
      }
      .center {
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 15 * @rem 0 0;
        padding: 0 18 * @rem;
        .left,
        .right {
          position: relative;
          line-height: 40 * @rem;
        }
        .left {
          font-size: 14 * @rem;
          color: #000000;
          font-weight: 400;
        }
        .right {
          width: 133 * @rem;
          text-align: right;
          border-bottom: 0.5 * @rem solid #a6a6a6;
          .text {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            color: #000000;
            font-size: 13 * @rem;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            span {
              display: block;
              flex-shrink: 0;
            }
          }
          .more-text-icon {
            width: 10 * @rem;
            height: 6 * @rem;
            background: url(~@/assets/images/games/bottom-arrow.png) center
              center no-repeat;
            background-size: 10 * @rem 6 * @rem;
            margin-left: 6 * @rem;
            transition: 0.3s;
            &.on {
              transform: rotateZ(180deg);
            }
          }
        }
        .xiaohao-list {
          display: none;
          position: absolute;
          top: 40 * @rem;
          left: 0;
          z-index: 2000;
          width: 100%;
          max-height: 200 * @rem;
          overflow: auto;
          border-radius: 0 0 4 * @rem 4 * @rem;
          background: #fff;

          border: 1 * @rem solid #f2f2f2;
          &.on {
            display: block;
          }
          .xiaohao-item {
            box-sizing: border-box;
            text-align: center;
            line-height: 40 * @rem;
            text-align: right;
            padding: 0 15 * @rem;
            font-size: 13 * @rem;
            color: #000000;
            font-weight: 400;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            &:not(:last-of-type) {
              border-bottom: 0.5 * @rem solid #f2f2f2;
            }
          }
        }
      }

      .dialog-bottom-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 18 * @rem;
        padding: 0 5 * @rem;
        .cancel {
          width: 102 * @rem;
          height: 35 * @rem;
          color: #7d7d7d;
          font-size: 13 * @rem;
          font-weight: 400;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f2f2f2;
          border-radius: 18 * @rem;
        }
        .confirm {
          width: 102 * @rem;
          height: 35 * @rem;
          color: #ffffff;
          font-size: 13 * @rem;
          font-weight: 400;
          display: flex;
          align-items: center;
          justify-content: center;
          background: @themeBg;
          border-radius: 18 * @rem;
        }
      }
    }
  }
  .copy-dialog {
    box-sizing: border-box;
    width: 244 * @rem;
    border-radius: 12 * @rem;
    background-color: #fff;
    padding: 20 * @rem 16 * @rem 22 * @rem;
    .title {
      display: flex;
      align-items: center;
      justify-content: center;
      .title-icon {
        width: 18 * @rem;
        height: 18 * @rem;
        .image-bg('~@/assets/images/games/gift-title-icon.png');
      }
      .title-text {
        font-size: 18 * @rem;
        color: #000000;
        font-weight: 500;
        margin-left: 4 * @rem;
      }
    }
    .cardpass {
      box-sizing: border-box;
      width: 209 * @rem;
      height: 39 * @rem;
      background-color: #f4f4f4;
      border-radius: 6 * @rem;
      display: flex;
      align-items: center;
      padding: 0 10 * @rem;
      margin-top: 13 * @rem;
      font-size: 13 * @rem;
      color: #000000;
      font-weight: 400;
      word-break: break-all;
    }
    .desc {
      font-size: 12 * @rem;
      line-height: 17 * @rem;
      color: #757575;
      font-weight: 400;
      margin-top: 13 * @rem;
      padding: 0 5 * @rem;
    }
    .copy-btn {
      width: 186 * @rem;
      height: 36 * @rem;
      margin: 20 * @rem auto 0;
      background: @themeBg;
      border-radius: 18 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 13 * @rem;
      font-weight: 500;
      color: #ffffff;
    }
  }
}

/* 浏览器滚动条隐藏 */
* {
  scrollbar-width: none;
}
*::-webkit-scrollbar {
  display: none;
}
* {
  -ms-overflow-style: none;
}
* {
  -ms-overflow-style: none;
}
* {
  overflow: -moz-scrollbars-none;
  scrollbar-width: none;
}
* {
  overflow: -webkit-scrollbar;
}
</style>
