<template>
  <!-- 首推游戏 -->
  <div
    class="game-container"
    :style="{ backgroundImage: `url(${info.game.video_thumb})` }"
    @click="goToGame(info.game)"
  >
    <div
      class="game-bar"
      v-sensors-exposure="gameExposure(info.game, 0)"
    >
      <game-item-4
        class="game-item"
        :gameInfo="info.game"
        :iconSize="56"
        :showHot="true"
      ></game-item-4>
      <yy-download-btn
        v-if="info.is_show_btn"
        :gameInfo="info.game"
        @click="goToGame(info.game, 0)"
      ></yy-download-btn>
    </div>
  </div>
</template>

<script>
import { BOX_goToGame } from '@/utils/box.uni.js';
export default {
  name: 'fragmentGameFirst',
  props: {
    info: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  methods: {
    gameExposure(item, index) {
      return {
        'event-name': 'game_exposure',
        'property-page_name': this.$sensorsPageGet(),
        'property-section_name': this.info.header_title || '暂无',
        'property-game_id': `${item.id}`,
        'property-game_name': item.title,
        'property-game_index': `${index}`,
      };
    },
    goToGame(item, index) {
      // 神策埋点
      this.$sensorsTrack('game_click', {
        page_name: this.$sensorsPageGet(),
        section_name: this.info.header_title || '暂无',
        game_id: `${item.id}`,
        game_name: item.title,
        game_index: `${index}`,
      });
      BOX_goToGame(
        {
          params: {
            id: item.id,
            gameInfo: item,
          },
        },
        { id: item.id },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.game-container {
  width: 351 * @rem;
  height: 211 * @rem;
  border-radius: 21 * @rem;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center top;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  .game-bar {
    box-sizing: border-box;
    position: absolute;
    bottom: 0;
    height: 76 * @rem;
    width: 100%;
    padding: 0 12 * @rem;
    display: flex;
    align-items: center;
    justify-content: center;
    // 毛玻璃效果
    backdrop-filter: blur(4px); // 兼容性
    -webkit-backdrop-filter: blur(4px); // 兼容性
    background-color: rgba(255, 255, 255, 0.05); // 背景色
    z-index: 1;
    /deep/ .game-item-components {
      .game-name {
        color: #fff;
        .game-subtitle {
          display: none;
        }
      }
      .hot-num {
        color: #fff;
      }
      .types {
        .type {
          color: #fff;
          &:not(:first-child) {
            &:before {
              content: '';
              background-color: #fff;
            }
          }
        }
      }
      .tags {
        .tag {
          background: rgba(255, 255, 255, 0.2);
          border-radius: 5 * @rem;
          .tag-name {
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
