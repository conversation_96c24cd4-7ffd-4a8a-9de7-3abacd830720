import { isIos, isIosBox, isAndroidBox } from '@/utils/userAgent';
import { ApibindWx } from '@/api/views/users';
import router from '@/router';
import { getQueryVariable } from '@/utils/function.js';
import { Toast } from 'vant';
import { handleActionCode } from '@/utils/actionCode.js';

function platformInit () {
  let platform = ''; // 平台
  try {
    if (isIos && getPostData() != undefined) {
      if (!isIosBox) {
        platform = 'ios'; // ios官包(暂时没有)
      } else {
        platform = 'iosBox'; // ios马甲包
      }
    } else {
      BOX.getFrom();
      if (!isAndroidBox) {
        platform = 'android'; // 安卓官包
      } else {
        platform = 'androidBox'; // 安卓马甲包
      }
    }
  } catch (e) {
    platform = 'h5'; // webapp
  }
  return platform;
}

function fromInit () {
  let from = '';
  switch (platform) {
    case 'ios':
      from = getEval().from;
      break;
    case 'android':
      from = BOX.getFrom();
      break;
    case 'iosBox':
      from = getEval().from;
      break;
    case 'androidBox':
    case 'h5':
      break;
  }
  return from;
}
export function getToken () {
  let token = '';
  switch (platform) {
    case 'ios':
      token = getEval().token;
      break;
    case 'android':
      token = BOX.getToken();
      break;
    case 'iosBox':
      token = getEval().token;
    case 'androidBox':
    case 'h5':
      break;
  }
  return token;
}

export function BOX_getAuthInfo () {
  var result;
  try {
    switch (platform) {
      case 'ios':
      case 'iosBox':
        result = getEval();
        break;
      case 'android':
      case 'androidBox':
        result = JSON.parse(BOX.getAuthInfo());
        break;
      case 'h5':
        result = false;
        break;
    }
  } catch (e) {
    result = false;
  }
  return result;
}

/**
 * @param web webapp相关的参数 如 name(webapp页面的name)、 params(webapp页面的params)、 h5_url
 * @param web.openType 1为window.open 2为window.location.href
 * @param web.msg 为打开微信或者支付宝这类软件时，如果打不开的话原生壳会调用这个字段并弹窗
 * @param app app相关的参数 如 url、 page(原生页)
 */
// 在浏览器中打开
export const BOX_openInBrowser = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'ios':
        if (isIosSdk) {
          window.location.href = app.url;
        }
        result = true;
        break;
      case 'iosBox':
        window.webkit.messageHandlers.openInBrowser.postMessage(web.h5_url);
        result = true;
        break;
      case 'android':
        if (
          app.msg &&
          ((!isSdk && authInfo.versionCode >= 4190) ||
            (isSdk && authInfo.versionCode >= 400))
        ) {
          result = BOX.openInBrowser(app.url, app.msg);
        } else {
          result = BOX.openInBrowser(app.url);
        }
        break;
      case 'androidBox':
        if (
          app.msg &&
          ((!isSdk && authInfo.versionCode >= 4190) ||
            (isSdk && authInfo.versionCode >= 400))
        ) {
          result = BOX.openInBrowser(web.h5_url, web.msg);
        } else {
          result = BOX.openInBrowser(web.h5_url);
        }
        break;
      case 'h5':
        if (web.open_type) {
          window.open(web.h5_url);
        } else {
          window.location.href = web.h5_url;
        }
        result = true;
        break;
    }
  } catch (e) {
    result = false;
  }
  return result;
};

// APP内新窗口打开(不带导航栏)
// ios中的openInNewWindow 会刷新本来的页面(一进去就刷) 隐藏状态栏
// ios中的openInNewWindowHiddenToolBar 不会刷新本来的页面 不隐藏状态栏 --> 隐藏状态栏

export const BOX_openInNewWindow = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'iosBox':
        if (web.h5_url) {
          window.webkit.messageHandlers.openInNewWindowHiddenToolBar.postMessage(
            web.h5_url,
          );
        } else {
          router.push({
            name: web.name,
            params: web.params,
          });
        }
        break;
      case 'android':
        try {
          BOX.openInNewWindowHiddenToolBar(app.url);
        } catch {
          router.push({
            name: web.name,
            params: web.params,
          });
        }

        break;
      case 'androidBox':
        if (web.h5_url) {
          //安卓马甲包开启无头新窗口
          BOX.openInNewFullScreenWindow(web.h5_url, web.title);
        } else {
          router.push({
            name: web.name,
            params: web.params,
          });
        }
        break;
      case 'h5':
        if (web.h5_url) {
          if (web.h5_url.indexOf('activity.3733.com') > -1) {
            router.push({
              name: 'Activity',
              params: {
                url: web.h5_url,
              },
            });
            return;
          }
          window.location.href = web.h5_url;
        } else {
          router.push({
            name: web.name,
            params: web.params,
          });
        }
        break;
      default:
        if (web.h5_url) {
          window.location.href = web.h5_url;
        } else {
          router.push({
            name: web.name,
            params: web.params,
          });
        }
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 打开带头部的新窗口
export const BOX_openInNewNavWindow = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'iosBox':
        if (web.h5_url) {
          window.webkit.messageHandlers.openInNewNavWindow.postMessage(
            web.h5_url,
          );
        } else {
          router.push({
            name: web.name,
            params: web.params,
          });
        }
        break;
      case 'android':
        BOX.openInNewWindow(app.url);
        break;
      case 'androidBox':
        if (web.h5_url) {
          BOX.openInNewWindow(web.h5_url);
        } else {
          router.push({
            name: web.name,
            params: web.params,
          });
        }
      case 'h5':
        if (web.h5_url) {
          if (web.h5_url.indexOf('activity.3733.com') > -1) {
            router.push({
              name: 'Activity',
              params: {
                url: web.h5_url,
              },
            });
            return;
          }
          window.location.href = web.h5_url;
        } else {
          router.push({
            name: web.name,
            params: web.params,
          });
        }
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// APP内新窗口打开(返回时会刷新页面)
export const BOX_openInNewNavWindowRefresh = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'iosBox':
        if (web.h5_url) {
          window.webkit.messageHandlers.openInNewNavWindow.postMessage(
            web.h5_url,
          );
        } else {
          router.push({
            name: web.name,
            params: web.params,
          });
        }
        break;
      case 'android':
        BOX.openInNewWindowRefresh(app.url);
        break;
      case 'androidBox':
        if (web.h5_url) {
          BOX.openInNewWindowRefresh(web.h5_url);
        } else {
          router.push({
            name: web.name,
            params: web.params,
          });
        }
        break;
      case 'h5':
        if (web.h5_url) {
          window.location.href = web.h5_url;
        } else {
          router.push({
            name: web.name,
            params: web.params,
          });
        }
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 关闭窗口
export const BOX_close = refresh => {
  let result;
  try {
    switch (platform) {
      case 'ios':
        window.webkit.messageHandlers.close.postMessage(refresh);
        break;
      case 'iosBox':
        window.webkit.messageHandlers.close.postMessage(refresh);
        break;
      case 'android':
        BOX.close(refresh);
        break;
      case 'androidBox':
        BOX.close(refresh);
      case 'h5':
        router.go(-1);
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

/**
 * 页面跳转
 * 每日签到：qd
 * 个人中心：grzx
 * 赚金币：zjb
 * 首页：index
 * 小号回收：xhhs
 * 交易界面：jy
 * 金币转盘：jbzp
 * 下载管理：yygl
 * 金币商城：jbsc
 * 捡漏：jl
 * 游戏试玩 yxsw
 * 游戏内测员 yxncy
 * 我的代金券 wddjq
 * 绑定邮箱 bdyx
 **/
export const BOX_showActivity = (web, app) => {
  // webapp传组件的name
  let result;
  try {
    switch (platform) {
      case 'iosBox':
        if (web.isIosBoxToNative) {
          window.webkit.messageHandlers.showActivity.postMessage(app.page);
        } else {
          router.push({
            name: web.name,
            params: web.params,
          });
        }
        break;
      case 'android':
        BOX.showActivity(app.page);
        break;
      case 'androidBox':
        if (web.isAndroidBoxToNative) {
          BOX.showActivity(app.page);
        } else {
          router.push({
            name: web.name,
            params: web.params,
          });
        }
        break;
      case 'h5':
        router.push({
          name: web.name,
          params: web.params,
        });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

export const BOX_showActivityByAction = web => {
  if (platform == 'android') {
    BOX.showActivityByAction(JSON.stringify(web));
  } else {
    handleActionCode(web);
  }
};

// 打开游戏详情页
export const BOX_goToGame = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'android':
        BOX.goToGame(app.id);
        break;
      case 'iosBox':
      case 'androidBox':
      case 'h5':
        router.push({
          name: 'GameDetail',
          params: web.params,
        });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 打开外部游戏详情页
export const BOX_goToExternalGame = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'android':
        BOX.goToGame(app.id);
        break;
      case 'iosBox':
      case 'androidBox':
      case 'h5':
        router.push({
          name: 'ExternalGameDetail',
          params: web.params,
        });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};
/**
 * 打开返利详情页
 * @param {object} web web的参数
 * @param {object} app app的参数
 */
export const BOX_goToFanliDetails = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'android':
        BOX.goToFanliDetails(app.url, app.newsId, app.title, app.newsType);
        break;
      case 'iosBox':
      case 'androidBox':
      case 'h5':
        // web.params: url、title
        router.push({
          name: 'Iframe',
          params: web.params,
        });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

/**
 * 打开专属代金券
 * @param {object} web web的参数
 * @param {object} app app的参数
 * @param number app.position tab位置
 */
export const BOX_goToCouponCenter = (web, app) => {
  let result;
  try {
    switch (platform) {
      case 'android':
        BOX.goToUpCouponCenter(app.position);
        break;
      case 'iosBox':
      case 'androidBox':
      case 'h5':
        router.push({
          name: 'CouponCenter',
          params: web.params,
        });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 打开实名认证页面
export const BOX_memAuth = () => {
  let result;
  try {
    switch (platform) {
      case 'android':
        BOX.memAuth();
        break;
      case 'iosBox':
      case 'androidBox':
      case 'h5':
        router.push({
          name: 'IdCard',
        });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 打开绑定手机页面、唤起绑定弹窗
export const Box_changePhone = () => {
  let result;
  try {
    switch (platform) {
      case 'android':
        BOX.bindPhone();
        break;
      case 'iosBox':
      case 'androidBox':
      case 'h5':
        router.push({
          name: 'ChangePhone',
        });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

/**
 * 执行登录
 */
export const BOX_login = () => {
  let result;
  try {
    switch (platform) {
      case 'ios':
        window.webkit.messageHandlers.login.postMessage();
        break;
      case 'iosBox':
        router.push({
          name: 'PhoneLogin',
        });
        break;
      case 'android':
        BOX.login();
        break;
      case 'androidBox':
        router.push({
          name: 'PhoneLogin',
        });
      case 'h5':
        const share = getQueryVariable('share');
        if (share) {
          window.location.href = 'https://app.3733.com';
          return;
        }
        router.push({
          name: 'PhoneLogin',
        });
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 打开微信
export const BOX_openWx = () => {
  try {
    BOX.openApp('com.tencent.mm');
  } catch {
    window.location.href = 'weixin://';
  }
};

// 打开QQ
export const BOX_openQQ = qq => {
  try {
    BOX.openQQ(qq);
  } catch {
    window.location.href = `mqq://im/chat?chat_type=wpa&uin=${qq}&version=1&src_type=web`;
  }
};

// 游戏下载
export const BOX_openApp = packageName => {
  try {
    BOX.openApp(packageName);
  } catch (e) {
    console.log(e);
  }
};

// 游戏下载
export const BOX_downloadGame = game => {
  try {
    BOX.downloadGame(JSON.stringify(game));
  } catch (e) {
    console.log(e);
  }
};

// 判断是否已安装游戏
export const BOX_checkInstall = packageName => {
  let result = true;
  try {
    result = BOX.checkInstall(packageName);
  } catch (e) {
    console.log(e);
  } finally {
    return result;
  }
};

// 微信验证
export const Box_wxOAuth2 = () => {
  let result;
  try {
    switch (platform) {
      case 'ios':
      case 'iosBox':
        window.webkit.messageHandlers.wxOAuth2.postMessage({});
        break;
      case 'android':
      case 'androidBox':
        BOX.wxOAuth2();
        break;
      case 'h5':
        Toast('请手动打开微信');
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

// 马甲包内扭转横竖屏
/**
 * @param {number} code 1横屏0竖屏
 */
export const BOX_setScreenOrientation = code => {
  try {
    switch (platform) {
      case 'iosBox':
        window.webkit.messageHandlers.setScreenOrientation.postMessage(code);
        break;
      case 'androidBox':
        BOX.setScreenOrientation(code);
        break;
      default:
        break;
    }
  } catch {}
};

// 获取当前app包名
export const BOX_getPackageName = () => {
  let result;
  try {
    if (isIos && getPostData() != undefined) {
      result = getEval().packageName;
    } else {
      result = BOX.getPackageName();
    }
  } catch (e) {
    result = false;
  }
  return result;
};

// 获取ios套壳数据
function getPostData () {
  if (!(typeof window.getData == undefined)) {
    return window.getData;
  }
  return undefined;
}

// 遗留下来的方法,ios通讯用
function getEval () {
  let data = getPostData();
  return eval('(' + data + ')');
}

// 获取隐私政策url最后的参数，比如/4sf8u
export function BOX_getProtocolKey () {
  let result;
  try {
    if (isIos && getPostData() != undefined) {
      result = window.webkit.messageHandlers.getProtocolKey.postMessage({});
    } else {
      result = BOX.getProtocolKey();
    }
  } catch (e) {
    result = '4sf8u';
  }
  return result;
}

/**
 * 获取sdk支付回调
 * @returns boolean | string
 */
export function SDK_getSchemes () {
  var result;
  try {
    if (isIos && getPostData() != undefined) {
      result = getEval().schemes;
    } else {
      result = BOX.getSchemes();
    }
  } catch (e) {
    result = false;
  }
  return result;
}

// 给安卓端发送信息
export function Box_postMessage (params) {
  var result;
  try {
    BOX.postMessage(JSON.stringify(params));
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
}

// 微信认证回调
window.getWxUserInfo = async params => {
  const res = await ApibindWx({
    wxCode: params.wxCode,
  });
  if (res.code >= 1) {
    Toast(`绑定成功${res.data.wx_nickname}`);
  }
};

// 获取礼包
/*
 ** card为礼包信息，里面要带游戏信息game
 ** card为礼包信息，里面要带游戏信息game
 */

/**
 * 获取礼包
 * @param {object} cardcard 礼包信息，里面要带游戏信息game
 * @param {number} autoXh  是否648礼包 0普通礼包，1为648礼包
 *
 */

export const BOX_takeGift = (card, autoXh = 0) => {
  try {
    BOX.takeGift(JSON.stringify(card), autoXh);
  } catch {}
};

/**
 * 获取代金券
 * @param {object} coupon 代金券信息，里面要带游戏信息game
 * @param {number} autoXh  是否648代金券 0普通代金券，1为648代金券
 *
 */

export const BOX_takeCoupon = (coupon, autoXh = 0) => {
  try {
    BOX.takeCoupon(JSON.stringify(coupon), autoXh);
  } catch {}
};

/**
 * 判断当前webview是否是在碎片里（碎片不是单独一个页面，没有返回键等）
 */
export function BOX_isInFragment() {
  let result;
  try {
    result = BOX.isInFragment();
  } catch {
    result = false;
  }
  return result;
}

/**
 * 充值上报给安卓端
 * @param {Object} {"order_id"："订单号"，"productname"："商品名称"}
 */
export function BOX_setPayParams(params) {
  if (isAndroidSdk) {
    try {
      BOX.setPayParams(JSON.stringify(params));
    } catch {}
  }
}

/**
 * 盒子下载apk
 *
 * @param  {string} apkUrl apk下载地址
 */
export const Box_downloadApk = (apkUrl = '') => {
  let result;
  try {
    switch (platform) {
      case 'android':
      case 'androidBox':
        BOX.downloadApk(apkUrl);
        break;
    }
    result = true;
  } catch (e) {
    result = false;
  }
  return result;
};

/**
 * 打开原生客服
 * @param  {string} url 来源地址
 * @param  {string} title 标题
 */
export const BOX_consultService = (
  url = null,
  title = '客服',
  is_zx = false,
) => {
  let result;
  try {
    BOX.consultService(url, title, is_zx);
    result = true;
  } catch {
    result = false;
  }
  return result;
};

/**
 * 获取原生客服未读消息数量
 *
 */
export const BOX_getKefuUnreadCount = () => {
  let result;
  try {
    result = BOX.getKefuUnreadCount();
  } catch {
    result = 0;
  }
  return result;
};

// iosSDK打开盒子方法
export const BOX_openIosAppByAction = (web) => {
  let result;
  try {
    BOX.openIosAppByAction(JSON.stringify(web));
    result = true;
  } catch {
    result = false;
  }
  return result;
};

export let platform = platformInit();
export let from = fromInit();
export let isIosSdk = platform == 'ios' && from == 103 ? true : false;
export let isAndroidSdk = platform == 'android' && from == 3 ? true : false;
export let isSdk = isAndroidSdk || isIosSdk ? true : false;
export let authInfo = BOX_getAuthInfo();
export let packageName = BOX_getPackageName();
