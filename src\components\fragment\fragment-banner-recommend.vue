<template>
  <!-- 首推游戏 -->
  <div
    class="game-container"
    :style="{ backgroundImage: `url(${detail.bg_img_url})` }"
    @click="goToGame(detail.game)"
  >
    <div
      class="game-bar"
      :style="{
        backgroundImage: `linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 10%, ${detail.bg_color}cc, ${detail.bg_color}`,
      }"
    >
      <div class="left-info">
        <div class="subtitle"> {{ detail.text1 }}</div>
        <div class="title">
          {{
            detail.title ||
            `${detail.game?.main_title || '标题'}${detail.game?.subtitle ? '-' + detail.game?.subtitle : ''}`
          }}
        </div>
      </div>
      <div class="right-icon">
        <img :src="detail.icon_url" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
import { BOX_goToGame } from '@/utils/box.uni.js';
export default {
  name: 'fragmentGameFirst',
  props: {
    info: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  computed: {
    detail() {
      return this.info.tab_action[0];
    },
  },
  methods: {
    goToGame(item) {
      BOX_goToGame(
        {
          params: {
            id: item.id,
            gameInfo: item,
          },
        },
        { id: item.id },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.game-container {
  width: 351 * @rem;
  height: 211 * @rem;
  border-radius: 12 * @rem;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center top;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  .game-bar {
    box-sizing: border-box;
    position: absolute;
    bottom: 0;
    height: 82 * @rem;
    width: 100%;
    padding: 20*@rem 12 * @rem 0;
    display: flex;
    align-items: center;
    z-index: 1;
    .left-info{
      flex: 1;
      min-width: 0;
      .subtitle{
        font-size: 12*@rem;
        color: #FFFFFF;
        line-height: 12*@rem;
      }
      .title{
        font-size: 18*@rem;
        color: #fff;
        line-height: 16*@rem;
        margin-top: 8*@rem;
        font-weight: bold;
        overflow: hidden;
      }
    }
    .right-icon{
      width: 61*@rem;
      height: 28*@rem;
      margin-left: 10*@rem;
    }
  }
}
</style>
