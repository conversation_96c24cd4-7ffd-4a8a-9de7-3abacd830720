<template>
  <div class="deal-trends-page page">
    <nav-bar-2 :title="$t('成交动态')" :border="true"></nav-bar-2>
    <div class="main" :class="{ centered: empty }">
      <content-empty
        v-if="empty"
        :tips="$t('暂时没有成交动态~')"
      ></content-empty>
      <load-more
        v-else
        class="list-container"
        v-model="loading"
        :finished="finished"
        @loadMore="loadMore"
        :check="false"
      >
        <div class="deal-list">
          <div
            class="deal-item"
            v-for="(deal, dealIndex) in dealList"
            :key="dealIndex"
          >
            <deal-item :info="deal" :type="2"></deal-item>
          </div>
        </div>
      </load-more>
    </div>
  </div>
</template>

<script>
import { ApiXiaohaoTradeList } from '@/api/views/xiaohao.js';
export default {
  name: 'DealTrends',
  data() {
    return {
      dealList: [],
      finished: false,
      loading: false,
      empty: false,
      page: 1,
      listRows: 10,
    };
  },
  async created() {
    await this.getDealList();
  },
  methods: {
    async getDealList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      this.loading = true;
      const res = await ApiXiaohaoTradeList({
        isDone: 1,
        page: this.page,
        listRows: this.listRows,
      });
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.dealList = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.dealList.push(...list);
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
      this.loading = false;
    },
    async loadMore() {
      await this.getDealList(2);
    },
  },
};
</script>

<style lang="less" scoped>
.deal-trends-page {
  background: #f7f8fa;

  .main {
    flex: 1;
    &.centered {
      display: flex;
      flex-direction: column;
      align-content: center;
      justify-content: center;
      background: #f7f8fa;
    }
    .list-container {
      .deal-list {
        padding-top: 12 * @rem;
        background: #f7f8fa;
        .deal-item {
          margin: 0 12 * @rem;
          background: #ffffff;
          border-radius: 8 * @rem;
          &:not(:first-child) {
            margin-top: 12 * @rem;
          }
        }
      }
    }
  }
}
</style>
