<template>
  <div class="deal-list">
    <yy-list
      class="yy-list"
      v-model="loadingObj"
      :finished="finished"
      @refresh="onRefresh"
      @loadMore="loadMore"
      :empty="empty"
    >
      <template v-for="(item, index) in list">
        <buy-item :key="index" :info="item" v-if="type == 'buy'"></buy-item>
        <sell-item :key="index" :info="item" v-else></sell-item>
      </template>
    </yy-list>
  </div>
</template>

<script>
import {
  ApiXiaohaoMyOrderList,
  ApiXiaohaoMyTradeList,
} from '@/api/views/xiaohao.js';
import sellItem from '../sell-item';
import buyItem from '../buy-item';
export default {
  name: 'DealList',
  components: {
    sellItem,
    buyItem,
  },
  props: {
    status: {
      type: Number,
      required: true,
      default: -1,
    },
    type: {
      type: String,
      validator: function (value) {
        return ['buy', 'sell'].includes(value);
      },
      default: 'buy',
    },
  },
  data() {
    return {
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      list: [],
      page: 1,
      listRows: 10,
      empty: false,
    };
  },
  methods: {
    async getList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      let res;
      if (this.type == 'buy') {
        res = await ApiXiaohaoMyOrderList({
          status: this.status,
          page: this.page,
          listRows: this.listRows,
        });
      } else if (this.type == 'sell') {
        res = await ApiXiaohaoMyTradeList({
          status: this.status,
          page: this.page,
          listRows: this.listRows,
        });
      }

      if (action === 1 || this.page === 1) {
        this.list = [];
      }
      this.list.push(...res.data.list);
      if (!this.list.length) {
        this.empty = true;
      }
      if (res.data.list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      if (!this.list.length) {
        await this.getList();
      } else {
        await this.getList(2);
      }

      this.loadingObj.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.deal-list {
  box-sizing: border-box;
  height: calc(100vh - 144 * @rem - @safeAreaTop);
  height: calc(100vh - 144 * @rem - @safeAreaTopEnv);
  flex: 1;
  display: flex;
  flex-direction: column;
  .yy-list {
    flex: 1;
    /deep/ .van-pull-refresh {
      flex: 1;
      overflow-y: auto;
    }
  }
}
</style>
