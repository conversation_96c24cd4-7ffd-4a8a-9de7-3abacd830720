<template>
  <!-- 轮播 -->
  <van-swipe
    class="yy-swiper"
    :style="{
      width: `${swiperWidth * remNumberLess}rem !important`,
      height: `${swiperHeight * remNumberLess}rem !important`,
    }"
    :autoplay="5000"
    @change="onChange"
    v-if="bannerList.length"
  >
    <template v-for="(banner, bannerIndex) in bannerList">
      <van-swipe-item
        class="swiper-slide"
        :key="banner.id"
        @click="tapBanner(banner, bannerIndex)"
      >
        <div class="banner-item" v-sensors-exposure="bannerExposure(banner, bannerIndex)">
          <img class="banner-bg" :src="banner.titleimg" alt="" />
          <!-- 合集 -->
          <div v-if="banner.type == 34 && banner.heji.title" class="card">
            <div class="card-icon" v-if="banner.icon">
              <img :src="banner.icon" alt="" />
            </div>
            <div class="card-info">
              <div class="collection-title">{{ banner.heji.title }}</div>
              <div class="collection-desc">{{ banner.heji.desc }}</div>
            </div>
          </div>
          <!-- 游戏 -->
          <div class="card" v-if="banner.type == 1">
            <div class="card-icon">
              <img :src="banner.game.titlepic" alt="" />
            </div>
            <div class="card-info">
              <div class="game-name"
                >{{ banner.game.main_title
                }}<span v-if="banner.game.subtitle">{{
                  banner.game.subtitle
                }}</span></div
              >
              <div class="game-tags">
                <div
                  class="tag"
                  v-for="(tag, tagIndex) in banner.game.extra_tag"
                  :key="tagIndex"
                >
                  {{ tag.name }}
                </div>
              </div>
            </div>
          </div>
          <!-- 活动--对应后台‘盒子内url’ -->
          <div class="activity-card" v-if="banner.type == 4">
            <div class="activity-info">
              <div class="activity-title">{{ banner.banner_title }}</div>
              <div class="activity-desc">{{ banner.banner_subtitle }}</div>
            </div>
          </div>
        </div>
      </van-swipe-item>
    </template>

    <template #indicator>
      <div class="custom-indicator" v-if="bannerList.length > 1">
        <div
          class="dot"
          :class="{ active: current === index }"
          v-for="(item, index) in bannerList"
          :key="index"
        >
        </div>
      </div>
    </template>
  </van-swipe>
</template>

<script>
import { remNumberLess } from '@/common/styles/_variable.less';
import { clickBanner } from '@/utils/function.js';
import { ApiStatisticsBanner } from '@/api/views/home.js';
import { platform } from '@/utils/box.uni.js';
import { handleActionCode } from '@/utils/actionCode.js';
export default {
  name: 'YyBanner',
  data() {
    return {
      remNumberLess,
      current: 0,
    };
  },
  props: {
    info: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  computed: {
    bannerList() {
      return this.info.banner || [];
    },
    swiperWidth() {
      return 350;
    },
    swiperHeight() {
      return this.swiperWidth * this.bannerList[0]?.scale;
    },
  },
  methods: {
    bannerExposure(banner, index) {
      return {
        'event-name': 'banner_exposure',
        'property-page_name': this.$sensorsPageGet(),
        'property-banner_index': `${index}`,
        'property-banner_name': banner.title || banner.game?.title || banner.heji?.title,
      }
    },
    onChange(index) {
      this.current = index;
    },
    tapBanner(banner, index) {

      // 神策埋点
      this.$sensorsTrack('banner_click', {
        page_name: this.$sensorsPageGet(),
        banner_index: index,
        banner_name:
          banner.title || banner.game?.title || banner.heji?.title,
      });

      this.CLICK_EVENT(banner.click_id);
      try {
        if (banner.id) {
          ApiStatisticsBanner({ id: banner.id });
        }
      } catch (e) {
        console.log(e);
      }

      // 6.5.3 版本新增 当轮播图里有action，优先用action进行跳转
      if (banner.action) {
        handleActionCode(banner.action);
        return;
      }

      if (banner.type === 34) {
        // 推荐合集
        this.toPage('ExternalGameCollect', { id: banner.heji_id });
        return false;
      }
      if (platform == 'iosBox' && banner.game && banner.game.classid == 140) {
        // 分类模拟器游戏
        this.toPage('EmulatorGameDetail', { id: banner.game.id });
        return false;
      }
      clickBanner(banner);
    },
  },
};
</script>
<style lang="less" scoped>
.yy-swiper {
  width: 350 * @rem;
  height: 221 * @rem;
  margin: 0 auto;
  .swiper-slide {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 16 * @rem;
    overflow: hidden;
    img {
      object-fit: cover;
    }
    .banner-title {
      box-sizing: border-box;
      height: 60 * @rem;
      width: 100%;
      background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.7) 100%
      );
      position: absolute;
      bottom: 0;
      left: 0;
      color: #fff;
      line-height: 22 * @rem;
      font-size: 16 * @rem;
      font-weight: bold;
      padding: 13 * @rem;
      padding-top: 28 * @rem;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  /deep/ .van-swipe__indicator {
    width: 5 * @rem;
    height: 5 * @rem;
    background-color: #fff;
    opacity: 1;
    border-radius: 3 * @rem;
  }
  /deep/ .van-swipe__indicator--active {
    width: 13 * @rem;
    height: 5 * @rem;
    background-color: #faae86;
  }
  /deep/ .van-swipe__indicator:not(:last-child) {
    margin-right: 3 * @rem;
  }
}

.banner-item {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 16 * @rem;
  overflow: hidden;

  .card {
    display: flex;
    align-items: center;
    width: 100%;
    height: 60 * @rem;
    padding: 0 12 * @rem;
    border-radius: 0 0 12 * @rem 12 * @rem;
    box-sizing: border-box;
    background: linear-gradient(180deg, rgba(105, 75, 59, 0) 0%, #694b3b 100%);
    backdrop-filter: blur(4 * @rem);
    position: absolute;
    bottom: 0;
    left: 0;

    .card-icon {
      border: 1 * @rem solid rgba(255, 255, 255, 0.5);
      flex-shrink: 0;
      width: 40 * @rem;
      height: 40 * @rem;
      margin-right: 13 * @rem;
      border-radius: 8 * @rem;
      overflow: hidden;

      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }

    .card-info {
      flex: 1;
      min-width: 0;
      .collection-title {
        width: 100%;
        height: 20 * @rem;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #ffffff;
        line-height: 20 * @rem;
        text-align: left;
        overflow: hidden;
      }
      .collection-desc {
        width: 100%;
        height: 20 * @rem;
        font-size: 12 * @rem;
        color: #f2f2f2;
        line-height: 20 * @rem;
        text-align: left;
        overflow: hidden;
      }
      .game-name {
        display: flex;
        align-items: center;
        width: 100%;
        height: 20 * @rem;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #ffffff;
        line-height: 20 * @rem;
        text-align: left;
        overflow: hidden;
        white-space: nowrap;
        span {
          display: flex;
          align-items: center;
          box-sizing: border-box;
          padding: 2 * @rem 4 * @rem;
          border: 0.5px solid #e0e0e0;
          line-height: 11 * @rem;
          font-size: 11 * @rem;
          font-weight: bold;
          border-radius: 4 * @rem;
          margin-left: 6 * @rem;
        }
      }
      .game-tags {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        width: 100%;
        height: 20 * @rem;
        margin-top: 3 * @rem;

        .tag {
          flex-shrink: 0;
          padding: 0 6 * @rem;
          height: 17 * @rem;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 5 * @rem;
          margin-right: 10 * @rem;
          font-weight: 400;
          font-size: 11 * @rem;
          color: #ffffff;
          line-height: 17 * @rem;
        }
      }
    }
  }

  .activity-card {
    display: flex;
    align-items: center;
    width: 100%;
    height: 60 * @rem;
    padding: 0 20 * @rem;
    box-sizing: border-box;
    background: linear-gradient(
      180deg,
      rgba(3, 0, 20, 0) 0%,
      rgba(3, 0, 20, 0.5) 34%,
      #030014 82%
    );
    backdrop-filter: blur(4 * @rem);
    position: absolute;
    bottom: 0;
    left: 0;

    .activity-info {
      flex: 1;
      min-width: 0;

      .activity-title {
        width: 100%;
        height: 20 * @rem;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #ffffff;
        line-height: 20 * @rem;
        overflow: hidden;
      }

      .activity-desc {
        width: 100%;
        height: 14 * @rem;
        font-weight: 400;
        font-size: 14 * @rem;
        color: #ffffff;
        line-height: 14 * @rem;
        margin-top: 6 * @rem;
        overflow: hidden;
      }
    }
  }
}

.custom-indicator {
  position: absolute;
  right: 20 * @rem;
  bottom: 12 * @rem;
  display: flex;
  align-items: center;
  .dot {
    width: 3 * @rem;
    height: 3 * @rem;
    border-radius: 3 * @rem;
    background: rgba(255, 255, 255, 0.6);
    margin-right: 3 * @rem;
    &.active {
      background: rgba(255, 255, 255, 0.9);
      width: 8 * @rem;
    }
  }
}
</style>
