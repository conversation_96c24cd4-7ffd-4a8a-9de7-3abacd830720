// vue.config.js
const path = require('path');
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin

module.exports = {
  // configureWebpack: {         
  //   plugins: [             
  //      new BundleAnalyzerPlugin() // 分析打包大小使用默认配置         
  //   ]    
  //  }, 
  devServer: {
    port: 8090,
    host: '0.0.0.0',
    https: false, // https:{type:Boolean}
    open: true, // 配置自动启动浏览器
    disableHostCheck: true,
  },
  parallel: false,
  // 打包文件名
  outputDir: process.env.outputDir,
  // 打包html名字
  indexPath:
    process.env.VUE_APP_CURRENTMODE === 'production'
      ? 'index.html'
      : 'index_test.html',
  // 静态文件路径
  publicPath:
    process.env.VUE_APP_CURRENTMODE === 'production'
      ? `https://static-hw.3733.com/wa/${process.env.VUE_APP_versionCode}`
      : process.env.VUE_APP_CURRENTMODE === 'test'
        ? './web/'
        : './',
  // 文件名称带hash值（清除缓存）
  // filenameHashing: process.env.VUE_APP_CURRENTMODE === 'production'?false:true,
  filenameHashing: false,
  productionSourceMap: false,
  chainWebpack: config => {
    // 全局引入less公共变量
    const types = ['vue-modules', 'vue', 'normal-modules', 'normal'];
    types.forEach(type =>
      addStyleResource(config.module.rule('less').oneOf(type)),
    );
  },
  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          modifyVars: {
            // 通过 less 文件覆盖vant的less变量
            hack: `true; @import "./src/common/styles/_variable_vant.less";`,
          },
        },
      },
    },
  },
};

function addStyleResource(rule) {
  rule
    .use('style-resource')
    .loader('style-resources-loader')
    .options({
      patterns: [
        path.resolve(__dirname, './src/common/styles/_variable.less'),
        path.resolve(__dirname, './src/common/styles/_variable_vant.less'),
      ],
    });
}
