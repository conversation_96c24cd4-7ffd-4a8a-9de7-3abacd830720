<template>
  <rubber-band :topColor="'#f5f5f6'" :bottomColor="'#f5f5f6'">
    <div class="my-quan-page page">
      <nav-bar-2 :border="true" :title="$t('我的代金券')">
        <template #right>
          <div class="introduction" @click="toIntroduction">
            {{ $t('使用说明') }}
          </div>
        </template>
      </nav-bar-2>
      <div class="main">
        <div class="nav-list">
          <div
            class="nav-item btn"
            :class="{ on: current == navIndex }"
            v-for="(nav, navIndex) in navList"
            :key="navIndex"
            @click="tapNav(navIndex)"
          >
            {{ nav.title }}
          </div>
        </div>
        <content-empty v-if="empty"></content-empty>
        <load-more
          class="my-quan-container"
          v-model="loading"
          :finished="finished"
          :finishedText="'已经到底了哦～'"
          @loadMore="loadMore"
          :check="false"
          v-else
        >
          <div class="quan-list">
            <div
              class="quan-item"
              v-for="(item, index) in couponList"
              :key="index"
            >
              <div class="coupon-model" :class="{ 'svip-bg': item.type == 1 }">
                <div class="top">
                  <div class="left">
                    <div class="amount-section">
                      <div class="amount"
                        >¥<span>{{ item.money }}</span></div
                      >
                      <div class="tip">
                        {{ $t('满') }}{{ item.reach_money }}{{ $t('元可用') }}
                      </div>
                    </div>
                    <div class="info">
                      <div class="game-name">{{ item.title }}</div>
                    </div>
                  </div>
                  <div class="right">
                    <div
                      class="get btn"
                      @click="toGame(item)"
                      v-if="state == 0"
                    >
                      {{ $t('使用') }}
                    </div>
                    <div class="get btn had" v-if="state == 2">
                      {{ $t('已使用') }}
                    </div>
                    <div class="get btn had" v-if="state == 1">
                      {{ $t('已过期') }}
                    </div>
                    <div
                      class="to-use-detail"
                      @click="toUseDetail(item)"
                      v-if="item.type == 7"
                      >使用明细</div
                    >
                  </div>
                </div>
                <div class="bottom">
                  <div class="date">
                    {{ item.expire_time_text }}
                  </div>
                  <div class="xh-name" v-if="item.xh_id">
                    {{ item.xh_name }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </load-more>

        <div class="more-quan-container">
          <div class="more-quan" @click="toPage('CouponCenter')">
            <div class="more-text">{{ $t('领券中心') }}</div>
            <div class="right-icon-black"></div>
          </div>
          <bottom-safe-area></bottom-safe-area>
        </div>
      </div>
    </div>
  </rubber-band>
</template>

<script>
import { ApiCouponMine } from '@/api/views/coupon.js';
import h5Page from '@/utils/h5Page';
export default {
  name: 'MyCoupon',
  data() {
    return {
      finished: false,
      loading: false,
      page: 1,
      listRows: 10,
      couponList: [],
      empty: false,
      state: 0, // 0：可使用,2:已使用，1：已过期,
      current: 0,
      navList: [
        {
          title: this.$t('可使用'),
          state: 0,
        },
        {
          title: this.$t('已使用'),
          state: 2,
        },
        {
          title: this.$t('已过期'),
          state: 1,
        },
      ],
    };
  },
  async created() {
    await this.getMyCoupons();
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    next(true);
  },
  methods: {
    async tapNav(index) {
      if (this.current != index) {
        this.finished = false;
        this.current = index;
        this.state = this.navList[index].state;
        this.couponList = [];
        this.loading = true;
        await this.getMyCoupons();
      }
    },
    toGame(item) {
      if (!item.package_name) {
        // 通用券
        this.$dialog.alert({
          title: this.$t('提示'),
          message: this.$t('该券为通用券，适用于大部分bt游戏'),
          confirmButtonText: this.$t('知道了'),
          confirmButtonColor: '@themeColor',
          lockScroll: false,
        });
        return;
      }
      this.$router.push({
        name: 'GameDetail',
        params: {
          id: item.game_id,
        },
      });
    },
    async getMyCoupons(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiCouponMine({
        page: this.page,
        listRows: this.listRows,
        state: this.state,
      });
      let { list } = res.data;
      if (action === 1 || this.page === 1) {
        this.couponList = [];
        if (!list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.couponList.push(...list);
      this.loading = false;
      if (list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async loadMore() {
      await this.getMyCoupons(2);
    },
    subscript(item) {
      switch (parseInt(item.type)) {
        case 1:
          return this.$t('SVIP会员专享');
        case 2:
          return this.$t('无门槛');
        default:
          return false;
      }
    },
    toIntroduction() {
      this.$router.push({
        name: 'Iframe',
        params: {
          url: h5Page.daijinquanshuoming,
          title: this.$t('代金券使用说明'),
        },
      });
    },
    toUseDetail(item) {
      this.$router.push({
        name: 'CouponUseDetail',
        params: {
          id: item.id,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .van-list {
  padding-bottom: calc(50 * @rem + @safeAreaBottom);
  padding-bottom: calc(50 * @rem + @safeAreaBottomEnv);
}
.my-quan-page {
  display: flex;
  flex-direction: column;
  padding-bottom: calc(50 * @rem + @safeAreaBottom);
  padding-bottom: calc(50 * @rem + @safeAreaBottomEnv);
  background-color: #f3f5f9;
  .introduction {
    font-size: 14 * @rem;
    color: #111111;
  }
  .main {
    padding-top: 40 * @rem;
    background-color: #f3f5f9;
    .nav-list {
      position: fixed;
      width: 100%;
      .fixed-center;
      top: calc(50 * @rem + @safeAreaTop);
      top: calc(50 * @rem + @safeAreaTopEnv);
      display: flex;
      justify-content: space-around;
      align-items: center;
      height: 40 * @rem;
      background-color: #fff;
      z-index: 2000;
      .nav-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        font-size: 15 * @rem;
        color: #797979;
        &.on {
          position: relative;
          color: #000000;
          &::after {
            content: '';
            width: 14 * @rem;
            height: 4 * @rem;
            border-radius: 2 * @rem;
            background-color: @themeColor;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: 0;
          }
        }
      }
    }
  }
  .my-quan-container {
    box-sizing: border-box;

    min-height: calc(100vh - 100 * @rem - @safeAreaTop - @safeAreaBottom);
    display: flex;
    flex-direction: column;

    /deep/ .pull-refresh {
      background-color: #f3f5f9;
      flex-grow: 1;
      flex-shrink: 0;
      min-height: 0;
    }
    .quan-list {
      padding-top: 6 * @rem;
      .quan-item {
        position: relative;
        width: 339 * @rem;
        height: 121 * @rem;
        margin: 12 * @rem auto 0;
        .coupon-model {
          box-sizing: border-box;
          width: 339 * @rem;
          height: 121 * @rem;
          position: relative;
          background-color: #fff;
          margin: 0 auto;
          border-radius: 10 * @rem;
          padding: 0 16 * @rem;

          .top {
            display: flex;
            align-items: center;
            height: 78 * @rem;

            .left {
              box-sizing: border-box;
              flex: 1;
              min-width: 0;
              height: 100%;
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;

              .amount-section {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 81 * @rem;
                .amount {
                  text-align: center;
                  font-size: 11 * @rem;
                  color: #fe4a26;
                  white-space: nowrap;
                  span {
                    font-size: 24 * @rem;
                    font-weight: bold;
                  }
                }
                .tip {
                  font-size: 10 * @rem;
                  color: #fe4a26;
                  white-space: nowrap;
                }
              }

              .info {
                margin-left: 5 * @rem;
                flex: 1;
                min-width: 0;
                .game-name {
                  font-size: 14 * @rem;
                  font-weight: bold;
                  color: #000000;
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                  line-height: 20 * @rem;
                  max-height: 40 * @rem;
                  overflow: hidden;
                  text-align: left;
                }
              }
            }
            .right {
              flex-shrink: 0;
              width: 49 * @rem;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              .get {
                font-size: 11 * @rem;
                color: #ffffff;
                width: 49 * @rem;
                height: 23 * @rem;
                line-height: 1;
                background: @themeBg;
                border-radius: 4 * @rem;
                font-weight: bold;
                display: flex;
                justify-content: center;
                align-items: center;
                &.had {
                  background: #bababa;
                }
              }
              .to-use-detail {
                font-weight: 400;
                font-size: 11 * @rem;
                color: @themeColor;
                line-height: 15 * @rem;
                text-align: center;
                margin-top: 9 * @rem;
              }
            }
          }

          .bottom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 43 * @rem;
            border-top: 1 * @rem dashed #d8d8d8;

            .date {
              color: #909090;
              font-size: 11 * @rem;
              line-height: 13 * @rem;
            }
            .xh-name {
              color: #909090;
              font-size: 11 * @rem;
              line-height: 13 * @rem;
            }
          }
        }
      }
    }

    .quan-history {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 30 * @rem 0;
      .btn-text {
        font-size: 14 * @rem;
        color: #666666;
      }
      .right-icon {
        width: 6 * @rem;
        height: 10 * @rem;
        background: url(~@/assets/images/right-icon.png) no-repeat;
        background-size: 6 * @rem 10 * @rem;
        margin-left: 4 * @rem;
      }
    }
  }
  .more-quan-container {
    background-color: #fff;
    border-top: 0.5 * @rem solid #f5f5f6;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 10;
    width: 100%;
    .more-quan {
      width: 100%;
      height: 40 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      .more-text {
        font-size: 14 * @rem;
        color: @themeColor;
      }
      .right-icon-black {
        width: 13 * @rem;
        height: 12 * @rem;
        background: url(~@/assets/images/games/right-arrow.png) center center
          no-repeat;
        background-size: 10 * @rem 10 * @rem;
        margin-left: 4 * @rem;
      }
    }
  }
}
</style>
