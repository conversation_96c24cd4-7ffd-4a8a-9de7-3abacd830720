<template>
  <rubber-band topColor="#FFFFFF" bottomColor="#FFFFFF">
    <div class="gamble-detail-page">
      <nav-bar-2
        :title="$t('商品详情')"
        :border="true"
        :azShow="true"
      ></nav-bar-2>
      <pull-refresh @refresh="onRefresh" v-model="isLoading">
        <div class="main">
          <div class="banner">
            <img :src="goodsBanner" alt="" />
            <div class="lucky-number" v-if="detail.top_content">
              {{ detail.top_content }}
            </div>
          </div>

          <div class="goods-info">
            <div class="line-1">
              <div class="title">{{ detail.title }}</div>
              <div class="current-status">{{ goodsState }}</div>
            </div>
            <div class="line-2">
              <div class="line-title">{{ $t('商品期号') }}：</div>
              <div class="period">{{ detail.period }}</div>
            </div>
            <div class="line-3">
              <div class="line-title">{{ $t('购买详情') }}：</div>
              <div class="left-total">
                <div
                  class="left-current"
                  :style="{
                    width: `${Math.floor(
                      (detail.use_total / detail.total) * 100,
                    )}%`,
                  }"
                ></div>
              </div>

              <div class="left-no-text" v-if="detail.state == 2">
                {{ $t('已售空') }}
              </div>
              <div class="left-text" v-else>
                <span>{{ detail.use_total }}</span
                >/{{ detail.total }}
              </div>
            </div>
            <div class="line-4">
              {{ $t('参与金币夺宝后请在【金币夺宝】-【我的夺宝】中查看') }}
            </div>
          </div>
          <div class="bottom-container">
            <!-- 已开奖 -->
            <template v-if="detail.state == 3">
              <div class="result-container">
                <div class="my-result" v-if="luckUser.result == 1">
                  <div class="result-icon win">
                    <img
                      src="@/assets/images/welfare/gold-gamble/result-win.png"
                      alt=""
                    />
                  </div>
                  <div class="result-desc">
                    {{ $t('幸运女神关注到了您，为您带来了大奖～') }}
                  </div>
                </div>
                <div class="my-result" v-if="luckUser.result == 0">
                  <div class="result-icon lose">
                    <img
                      src="@/assets/images/welfare/gold-gamble/result-lose.png"
                      alt=""
                    />
                  </div>
                  <div class="result-desc">
                    {{ $t('大奖与你擦肩而过，不要灰心，再接再厉') }}
                  </div>
                </div>
                <div class="lucky-user">
                  <div class="lucky-title">
                    {{ detail.period }}{{ $t('期获奖用户') }}
                  </div>
                  <div class="user-info">
                    <user-avatar
                      class="avatar"
                      :src="luckUser.avatar"
                      :self="false"
                    ></user-avatar>
                    <div class="lucky-user-right">
                      <div class="nickname">
                        {{ $t('幸运用户') }}：{{ luckUser.nickname }}
                      </div>
                      <div class="number">
                        {{ $t('幸运号码') }}：{{ luckUser.code }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <!-- 进行中 -->
            <template v-if="[1, 2].includes(Number(detail.state))">
              <div class="bottom-online">
                <van-sticky :offset-top="stickyOffsetTop">
                  <div class="bottom-navs">
                    <div
                      class="bottom-nav"
                      :class="{ current: current == navIndex }"
                      v-for="(nav, navIndex) in bottomNavList"
                      :key="navIndex"
                      @click="tapBottomNav(navIndex)"
                    >
                      {{ nav.title }}
                    </div>
                    <div
                      class="line"
                      :style="{ left: `${current * 116 * remNumberLess}rem` }"
                    ></div>
                  </div>
                </van-sticky>
                <div class="bottom-content" v-if="current == 0">
                  <!-- 本期参与 -->
                  <content-empty
                    v-if="empty"
                    class="content-empty"
                    :emptyImg="emptyImg"
                    :tips="$t('快来参与金币夺宝吧')"
                  ></content-empty>
                  <load-more
                    v-model="loading"
                    :finished="finished"
                    @loadMore="loadMore"
                    :check="false"
                    v-else
                  >
                    <div class="player-list">
                      <div
                        class="player-item"
                        v-for="item in playUserList"
                        :key="item.id"
                      >
                        <user-avatar
                          class="player-avatar"
                          :src="item.avatar"
                          :self="false"
                        ></user-avatar>
                        <div class="player-right">
                          <div class="line-1">
                            <div class="nickname">{{ item.nickname }}</div>
                            <div class="date">
                              {{ formatDateOnly(item.create_time) }}
                            </div>
                          </div>
                          <div class="line-2">
                            <div class="num">
                              {{ $t('购买') }}<span>{{ item.num }}</span
                              >{{ $t('个夺宝码') }}
                            </div>
                            <div class="time">
                              {{ formatTime(item.create_time) }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </load-more>
                </div>
                <div class="bottom-content" v-if="current == 1">
                  <!-- 往期揭秘 -->
                  <content-empty
                    v-if="pastEmpty"
                    class="content-empty"
                    :emptyImg="emptyImg"
                  ></content-empty>
                  <div class="past-list" v-else>
                    <div
                      class="past-item"
                      v-for="item in pastList"
                      :key="item.id"
                    >
                      <div class="past-top">
                        <div class="period"
                          >{{ item.period }}{{ $t('期') }}</div
                        >
                        <div class="open-time">
                          {{ $t('开奖时间') }}：{{
                            formatDate(item.update_time)
                          }}
                        </div>
                      </div>
                      <div class="palyer-info">
                        <user-avatar
                          class="player-avatar"
                          :src="item.avatar"
                          :self="false"
                        ></user-avatar>
                        <div class="player-right">
                          <div class="lucky-info">
                            <div class="nickname">
                              {{ $t('获奖用户') }}：{{ item.nickname }}
                            </div>
                            <div class="number">
                              <div class="lucky-code">
                                {{ $t('幸运号码') }}：{{ item.code }}
                              </div>
                            </div>
                          </div>
                          <div class="lucky-num">
                            {{ $t('购买数量') }}：{{ item.num }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="bottom-content" v-if="current == 2">
                  <!-- 开奖规则 -->
                  <div class="open-rule">
                    <img
                      src="@/assets/images/welfare/gold-gamble/open-rule.png"
                      alt=""
                    />
                    <div class="rule-item">
                      <div class="rule-num">A</div>
                      <div class="rule-text">
                        {{
                          $t(
                            '截止该商品开奖时间点前全站最后50个参与记录的时间值之和（如时间为20:12:18:232，则取201218232）',
                          )
                        }}
                      </div>
                    </div>
                    <div class="rule-item">
                      <div class="rule-num">B</div>
                      <div class="rule-text">
                        {{ $t('如28/6=4余数4（4x6=24 28-24=4）') }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </pull-refresh>

      <div class="bottom-operation">
        <div class="my-code-btn btn" v-if="isPart" @click="clickMyCode">
          {{ $t('我的夺宝码') }}
        </div>
        <div class="gamble-btn no" v-if="detail.state == 2">
          {{ $t('已售空') }}
        </div>
        <div class="gamble-btn no" v-if="detail.state == 3">
          {{ $t('已参与夺宝') }}
        </div>
        <div
          class="gamble-btn btn"
          @click="clickStartGamble"
          v-if="detail.state == 1"
        >
          {{ $t('立即夺宝') }}
        </div>
      </div>
      <!-- 上弹购买夺宝码 -->
      <van-popup
        class="gamble-buy-popup"
        position="bottom"
        v-model="gambleBuyPopupShow"
        :lock-scroll="false"
      >
        <div class="title">{{ $t('购买夺宝码') }}</div>
        <div class="code-info">
          <div class="code-icon"></div>
          <div class="code-right">
            <div class="code-rule">
              {{ detail.gold }}{{ $t('金币=1个夺宝码') }}
            </div>
            <div class="code-left">
              {{ $t('剩余') }}<span>{{ leftTotal }}</span
              >{{ $t('个夺宝码') }}
            </div>
          </div>
        </div>
        <div class="buy-number-container">
          <div class="buy-number-operation">
            <div class="buy-number-title">{{ $t('购买数量') }}</div>
            <van-stepper
              class="number-stepper"
              v-model="buyNumber"
              min="0"
              :max="leftTotal"
              :button-size="`${24 * remNumberLess}rem`"
              :input-width="`${40 * remNumberLess}rem`"
            />
          </div>
          <div class="buy-number-choices">
            <div
              class="choice"
              :class="{ on: item == buyNumber }"
              v-for="item in codeChoices"
              :key="item"
              @click="chooseNumber(item)"
            >
              {{ item }}
            </div>
          </div>
        </div>
        <div class="total-info">
          <div class="total-gold">
            {{ $t('合计') }}：<span>{{ totalNeedGold }}{{ $t('金币') }}</span>
          </div>
          <div class="left-gold">{{ $t('剩余金币') }}：{{ gold }}</div>
        </div>
        <div class="confirm-container">
          <div
            class="confirm-btn btn"
            @click="handleBuy"
            v-if="gold >= totalNeedGold"
          >
            {{ $t('立即购买') }}
          </div>
          <div class="confirm-btn no" @click="$toast($t('金币不足'))" v-else>
            {{ $t('金币不足') }}
          </div>
        </div>
      </van-popup>
      <!-- 我的夺宝码弹窗 -->
      <van-dialog
        v-model="myGambleCodeShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :close-on-click-overlay="true"
        class="my-gamble-code-popup"
      >
        <div class="popup-icon">
          <div class="popup-title">{{ $t('我的夺宝码') }}</div>
        </div>
        <div class="popup-content">
          <div class="code-list">
            <div
              class="code-item"
              v-for="item in myGambleCode"
              :key="item.code"
            >
              {{ item.code }}
            </div>
          </div>
          <div class="confirm-btn" @click="myGambleCodeShow = false">
            {{ $t('确定') }}
          </div>
        </div>
      </van-dialog>
    </div>
  </rubber-band>
</template>

<script>
import {
  ApiGoldDuobaoRead,
  ApiGoldDuobaoPlayUsers,
  ApiGoldDuobaoUserCode,
  ApiGoldDuobaoCreateOrder,
  ApiGoldDuobaoPastPeriod,
} from '@/api/views/goldGamble.js';
import couponBanner from '@/assets/images/welfare/gold-gamble/detail-coupon-banner.png';
import goldBanner from '@/assets/images/welfare/gold-gamble/detail-gold-banner.png';
import { remNumberLess } from '@/common/styles/_variable.less';
import emptyImg from '@/assets/images/welfare/gold-gamble/my-empty.png';
export default {
  name: 'GambleDetail',
  data() {
    return {
      remNumberLess,
      emptyImg,
      id: 0,
      gold: 0,
      detail: {},
      luckUser: null,
      gambleBuyPopupShow: false,
      codeChoices: [5, 10, 25, 50],
      buyNumber: 0,
      isPart: 0, // 当前用户是否参与
      myGambleCode: [],
      myGambleCodeShow: false,
      bottomNavList: [
        { title: this.$t('本期参与') },
        { title: this.$t('往期揭秘') },
        { title: this.$t('开奖规则') },
      ],
      current: 0,
      playUserList: [],
      finished: false, // 本期参与
      loading: false, // 本期参与
      page: 1, // 本期参与
      listRows: 10, // 本期参与
      empty: false, // 本期参与
      pastList: [],
      pastEmpty: false, // 往期揭秘
      stickyOffsetTop: '0px', //顶部导航栏的高度
      isLoading: false,
    };
  },
  computed: {
    goodsBanner() {
      let src;
      switch (Number(this.detail.type)) {
        case 1:
          src = couponBanner;
          break;
        case 2:
          src = goldBanner;
          break;
      }
      return src;
    },
    goodsState() {
      let state;
      switch (Number(this.detail.state)) {
        case 1:
          state = this.$t('进行中');
          break;
        case 2:
          state = this.$t('已售空');
          break;
        case 3:
          state = this.$t('已开奖');
          break;
      }
      return state;
    },
    leftTotal() {
      return this.detail.total - this.detail.use_total;
    },
    totalNeedGold() {
      if (this.buyNumber == 0) {
        return 0;
      }
      return this.buyNumber * this.detail.gold;
    },
  },
  async created() {
    this.id = this.$route.params.id;
    await this.getDetail();
    if (this.detail.state != 3) {
      await this.getPlayUsers();
    }
  },
  async mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop =
      document.querySelector('.van-nav-bar').offsetHeight + 'px';
  },
  methods: {
    chooseNumber(num) {
      if (num > this.leftTotal) {
        this.$toast(`${this.$t('剩余夺宝码数量不足')}${num}${this.$t('个')}`);
        return;
      }
      this.buyNumber = num;
    },
    async clickMyCode() {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      const res = await ApiGoldDuobaoUserCode({ id: this.id });
      let { user_code } = res.data;
      this.myGambleCode = user_code;
      this.$toast.clear();
      this.myGambleCodeShow = true;
    },
    clickStartGamble() {
      this.gambleBuyPopupShow = true;
    },
    async handleBuy() {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      const res = await ApiGoldDuobaoCreateOrder({
        num: this.buyNumber,
        id: this.id,
      });
      if (res.code > 0) {
        await this.getDetail();
        await this.getPlayUsers();
        this.gambleBuyPopupShow = false;
        this.$toast(this.$t('购买夺宝码成功'));
      }
    },
    async getDetail() {
      const res = await ApiGoldDuobaoRead({ id: this.id });
      let { gold, detail, info, luck_user, is_part } = res.data;
      this.isPart = is_part || 0;
      this.gold = gold || 0;
      this.detail = detail || info;
      if (luck_user) {
        this.luckUser = luck_user;
      }
    },
    async tapBottomNav(index) {
      if (this.current == index) return;
      this.current = index;
      if (this.current == 0) {
        await this.getPlayUsers();
      } else if (this.current == 1) {
        await this.getPastPeriod();
      }
    },
    async getPlayUsers(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiGoldDuobaoPlayUsers({
        id: this.id,
        page: this.page,
        listRows: this.listRows,
      });
      let { play_users } = res.data;
      if (action === 1 || this.page === 1) {
        this.playUserList = [];
        if (!play_users.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.playUserList.push(...play_users);
      this.loading = false;
      if (play_users.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async loadMore() {
      await this.getPlayUsers(2);
    },
    async getPastPeriod() {
      const res = await ApiGoldDuobaoPastPeriod({ id: this.id });
      let { list } = res.data;
      this.pastList = list;
      if (!this.pastList.length) {
        this.pastEmpty = true;
      } else {
        this.pastEmpty = false;
      }
    },
    formatDate(timestamp) {
      let { year, date, time, second } = this.$handleTimestamp(timestamp);
      return `${year}-${date} ${time}:${second}`;
    },
    formatDateOnly(timestamp) {
      let { year, month, day } = this.$handleTimestamp(timestamp);
      return `${year}/${month}/${day}`;
    },
    formatTime(timestamp) {
      let { time, second } = this.$handleTimestamp(timestamp);
      return `${time}:${second}`;
    },
    async onRefresh() {
      await this.getDetail();
      if (this.detail.state != 3) {
        await this.getPlayUsers();
        await this.getPastPeriod();
      }
      this.isLoading = false;
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.nav-title {
  font-weight: 600 !important;
  font-size: 18 * @rem !important;
  color: #191b1f !important;
}
.gamble-detail-page {
  background-color: #fff;
  min-height: 100vh;
  .main {
    padding-bottom: calc(60 * @rem + @safeAreaBottom);
    padding-bottom: calc(60 * @rem + @safeAreaBottomEnv);
    .banner {
      position: relative;
      height: 180 * @rem;
      .lucky-number {
        position: absolute;
        left: 0;
        bottom: 0 * @rem;
        box-sizing: border-box;
        width: 100%;
        height: 48 * @rem;
        background: rgba(255, 246, 229, 0.8);
        border-radius: 16 * @rem 16 * @rem 0 0;
        padding: 9 * @rem 0 0 12 * @rem;
        font-size: 12 * @rem;
        color: #fd6a53;
        font-weight: 600;
        overflow: hidden;
      }
    }

    .goods-info {
      position: relative;
      top: -16 * @rem;
      background: #fff;
      box-sizing: border-box;
      padding: 20 * @rem 12 * @rem 16 * @rem 12 * @rem;
      height: 156 * @rem;
      border-radius: 16 * @rem 16 * @rem 0 0;
      border-bottom: 8 * @rem solid #f7f8fa;
      display: flex;
      flex-direction: column;
      .line-1 {
        display: flex;
        align-items: center;
        .title {
          font-size: 18 * @rem;
          color: #191b1f;
          font-weight: bold;
          flex: 1;
          min-width: 0;
        }
        .current-status {
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 16 * @rem;
          color: #fd6a53;
          font-weight: 600;
        }
      }
      .line-2 {
        font-size: 14 * @rem;
        color: #30343b;
        display: flex;
        align-items: center;
        margin-top: 14 * @rem;
      }
      .line-3 {
        display: flex;
        align-items: center;
        margin-top: 8 * @rem;
        .line-title {
          font-size: 14 * @rem;
          color: #30343b;
        }
        .left-total {
          flex: 1;
          min-width: 0;
          width: 220 * @rem;
          height: 6 * @rem;
          background-color: #efeceb;
          border-radius: 3 * @rem;
          margin-right: 6 * @rem;
          overflow: hidden;
          .left-current {
            width: 0%;
            height: 6 * @rem;
            background-color: #fd6a53;
            border-radius: 3 * @rem;
          }
        }
        .left-text {
          font-size: 13 * @rem;
          color: #000000;
          span {
            color: #fd6a53;
          }
        }
        .left-no-text {
          color: #9a9a9a;
          font-size: 13 * @rem;
        }
      }
      .line-4 {
        margin-top: 16 * @rem;
        font-size: 12 * @rem;
        color: #93999f;
      }
    }
  }
  .bottom-container {
    position: relative;
    top: -16 * @rem;
    .result-container {
      .my-result {
        margin-top: 32 * @rem;
        .result-icon {
          margin: 0 auto;
          &.win {
            display: flex;
            align-items: center;
            justify-content: center;
            img {
              width: 198 * @rem;
              height: 24 * @rem;
            }
          }
          &.lose {
            display: flex;
            align-items: center;
            justify-content: center;
            img {
              width: 160 * @rem;
              height: 24 * @rem;
            }
          }
        }
        .result-desc {
          font-size: 13 * @rem;
          color: #93999f;
          text-align: center;
          line-height: 18 * @rem;
          margin-top: 12 * @rem;
        }
      }
      .lucky-user {
        width: 315 * @rem;
        margin: 30 * @rem auto 0;
        background: #fff8f2;
        border-radius: 12 * @rem;
        padding-bottom: 23 * @rem;
        overflow: hidden;
        .lucky-title {
          width: 143 * @rem;
          height: 29 * @rem;
          line-height: 29 * @rem;
          background: #ffe3d7;
          border-radius: 0 0 16 * @rem 16 * @rem;
          font-size: 13 * @rem;
          color: #974c41;
          text-align: center;
          margin: 0 auto;
        }
        .user-info {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 20 * @rem;
          .avatar {
            width: 40 * @rem;
            height: 40 * @rem;
            background-color: #9a9a9a;
            border-radius: 50%;
          }
          .lucky-user-right {
            margin-left: 10 * @rem;
            max-width: 220 * @rem;
            .nickname {
              font-size: 13 * @rem;
              color: #30343b;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .number {
              font-size: 13 * @rem;
              color: #fd6a53;
              margin-top: 4 * @rem;
            }
          }
        }
      }
    }
    .bottom-online {
      .bottom-navs {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 348 * @rem;
        margin: 0 auto;
        position: relative;
        background-color: #fff;
        .bottom-nav {
          width: 116 * @rem;
          height: 40 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 15 * @rem;
          color: #60666c;
          &.current {
            font-weight: bold;
            color: #191b1f;
          }
        }
        .line {
          width: 16 * @rem;
          height: 3 * @rem;
          border-radius: 2 * @rem;
          background-color: #fd6a53;
          position: absolute;
          bottom: 0 * @rem;
          transform: translateX(52 * @rem);
          transition: 0.3s;
        }
      }
      .bottom-content {
        .content-empty {
          margin-top: 68 * @rem;
        }
        /deep/.van-empty__image {
          width: 128 * @rem;
          height: 128 * @rem;
        }
        /deep/.van-empty__description {
          margin-top: 6 * @rem;
          font-size: 12 * @rem;
          color: #93999f;
        }
        .player-list {
          padding: 0 14 * @rem 0 12 * @rem;
          .player-item {
            height: 79 * @rem;
            box-sizing: border-box;
            padding: 20 * @rem 0;
            border-bottom: 0.5 * @rem solid #eaebf1;
            display: flex;
            align-items: center;
            .player-avatar {
              width: 36 * @rem;
              height: 36 * @rem;
              overflow: hidden;
              border-radius: 50%;
            }
            .player-right {
              flex: 1;
              min-width: 0;
              margin-left: 10 * @rem;
              .line-1 {
                display: flex;
                justify-content: space-between;
                align-items: center;
                .nickname {
                  font-size: 14 * @rem;
                  color: #30343b;
                  font-weight: bold;
                  line-height: 18 * @rem;
                }
                .date {
                  font-size: 12 * @rem;
                  color: #93999f;
                  line-height: 15 * @rem;
                }
              }
              .line-2 {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 6 * @rem;
                .num {
                  font-size: 12 * @rem;
                  color: #60666c;
                  span {
                    color: #fd6a53;
                  }
                }
                .time {
                  font-size: 12 * @rem;
                  color: #fd6a53;
                }
              }
            }
          }
        }
        .past-list {
          padding: 0 14 * @rem 0 12 * @rem;
          .past-item {
            padding: 20 * @rem 0;
            &:not(:last-of-type) {
              border-bottom: 0.5 * @rem solid #ebebeb;
            }
            border-bottom: 0.5 * @rem solid #ebebeb;
            .past-top {
              display: flex;
              align-items: center;
              justify-content: space-between;
              .period {
                font-size: 14 * @rem;
                color: #191b1f;
                font-weight: bold;
                line-height: 18 * @rem;
              }
              .open-time {
                font-size: 12 * @rem;
                color: #fd6a53;
                line-height: 18 * @rem;
              }
            }
            .palyer-info {
              display: flex;
              align-items: center;
              margin-top: 9 * @rem;
              .player-avatar {
                width: 36 * @rem;
                height: 36 * @rem;
                border-radius: 50%;
                overflow: hidden;
              }
              .player-right {
                flex: 1;
                min-width: 0;
                margin-left: 10 * @rem;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .lucky-info {
                  .nickname {
                    font-size: 12 * @rem;
                    color: #93999f;
                    line-height: 17 * @rem;
                    flex-shrink: 0;
                    width: 190 * @rem;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }
                  .number {
                    font-size: 12 * @rem;
                    color: #93999f;
                    line-height: 17 * @rem;
                    margin-top: 4 * @rem;
                    flex-shrink: 0;
                    width: 190 * @rem;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }
                }
                .lucky-num {
                  flex-shrink: 0;
                  font-size: 12 * @rem;
                  color: #93999f;
                }
              }
            }
          }
        }
        .open-rule {
          margin-top: 20 * @rem;
          img {
            width: 306 * @rem;
            height: 73 * @rem;
            margin: 0 auto 30 * @rem;
          }
          .rule-item {
            padding: 0 18 * @rem;
            display: flex;
            align-items: flex-start;
            margin-top: 15 * @rem;
            .rule-num {
              margin-top: 3 * @rem;
              width: 16 * @rem;
              height: 16 * @rem;
              border-radius: 50%;
              background-color: #fd6a53;
              color: #fff;
              font-size: 12 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .rule-text {
              flex: 1;
              min-width: 0;
              margin-left: 6 * @rem;
              font-size: 13 * @rem;
              line-height: 20 * @rem;
              color: #60666c;
            }
          }
        }
      }
    }
  }
  .bottom-operation {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    width: 100%;
    height: calc(60 * @rem + @safeAreaBottom);
    height: calc(60 * @rem + @safeAreaBottomEnv);
    background: #ffffff;
    box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.04);
    position: fixed;
    .fixed-center;
    bottom: 0;
    padding: 0 18 * @rem;
    padding-bottom: @safeAreaBottom;
    padding-bottom: @safeAreaBottomEnv;
    .gamble-btn {
      flex: 1;
      height: 40 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(90deg, #ff573d 0%, #ffad5c 100%);
      border-radius: 40 * @rem;
      color: #fff;
      font-size: 15 * @rem;
      font-weight: 600;
      box-sizing: border-box;
      &.no {
        background: #cccccc;
      }
    }
    .my-code-btn {
      width: 138 * @rem;
      height: 40 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16 * @rem;
      box-sizing: border-box;
      border: 1 * @rem solid #fd6a53;
      color: #fd6a53;
      font-weight: bold;
      border-radius: 40 * @rem;
      margin-right: 15 * @rem;
    }
  }
  .gamble-buy-popup {
    box-sizing: border-box;
    border-radius: 20 * @rem 20 * @rem 0 0;
    padding-bottom: @safeAreaBottom;
    padding-bottom: @safeAreaBottomEnv;
    .title {
      height: 49 * @rem;
      padding: 24 * @rem 0 0 12 * @rem;
      box-sizing: border-box;
      font-size: 18 * @rem;
      color: #191b1f;
      font-weight: bold;
    }
    .code-info {
      display: flex;
      align-items: center;
      padding: 20 * @rem 12 * @rem;
      .code-icon {
        width: 72 * @rem;
        height: 72 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 12 * @rem;
        background: url('~@/assets/images/welfare/gold-gamble/gamble-code-icon.png')
          #fff8f2 no-repeat center center;
        background-size: 72 * @rem 60 * @rem;
      }
      .code-right {
        margin-left: 10 * @rem;
        flex: 1;
        min-width: 0;
        .code-rule {
          font-size: 16 * @rem;
          font-weight: bold;
          color: #191b1f;
          line-height: 22 * @rem;
        }
        .code-left {
          font-size: 13 * @rem;
          color: #60666c;
          margin-top: 8 * @rem;
          line-height: 18 * @rem;
          span {
            color: #fd6a53;
          }
        }
      }
    }
    .buy-number-container {
      padding: 7 * @rem 12 * @rem 24 * @rem 12 * @rem;
      .buy-number-operation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .buy-number-title {
          font-size: 15 * @rem;
          color: #191b1f;
        }
        .number-stepper {
          /deep/ .van-stepper__input {
            background-color: #fff;
            font-size: 16 * @rem;
            color: #333333;
            font-weight: bold;
            margin: 0 2 * @rem;
          }
          /deep/ .van-stepper__minus,
          /deep/ .van-stepper__plus {
            background-color: #fff;
            border: 0.5 * @rem solid #cfd2d8;
            border-radius: 6 * @rem;
          }
        }
      }
      .buy-number-choices {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 18 * @rem;
        .choice {
          width: 72 * @rem;
          height: 36 * @rem;
          background: #f0f1f5;
          border-radius: 8 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14 * @rem;
          color: #30343b;
          &.on {
            color: #fff;
            background: #fd6a53;
          }
        }
      }
    }
    .total-info {
      width: 339 * @rem;
      margin: 0 auto;
      border-top: 0.5 * @rem solid #eaebf1;
      padding: 24 * @rem 12 * @rem;
      .total-gold {
        font-size: 15 * @rem;
        color: #191b1f;
        font-weight: bold;
        span {
          color: #fd6a53;
          font-size: 18 * @rem;
          font-weight: bold;
        }
      }
      .left-gold {
        font-size: 13 * @rem;
        color: #60666c;
        margin-top: 6 * @rem;
      }
    }
    .confirm-container {
      width: 100%;
      height: 60 * @rem;
      background-color: #fff;
      box-shadow: 0px -3px 4px 0px rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: center;
      .confirm-btn {
        width: 339 * @rem;
        height: 40 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 15 * @rem;
        color: #ffffff;
        font-weight: bold;
        border-radius: 40 * @rem;
        background: linear-gradient(90deg, #ff573d 0%, #ffad5c 100%);
        &.no {
          background: #c1c1c1;
        }
      }
    }
  }
  .my-gamble-code-popup {
    width: 300 * @rem;
    background-color: transparent;
    display: flex;
    flex-direction: column;
    align-items: center;
    .popup-icon {
      width: 300 * @rem;
      height: 90 * @rem;
      background: url('~@/assets/images/welfare/gold-gamble/my-code-title-icon.png')
        no-repeat center center;
      background-size: 100% 100%;
      position: relative;
      top: 1 * @rem;
      z-index: 2;
      .popup-title {
        position: absolute;
        left: 20 * @rem;
        bottom: 20 * @rem;
        font-size: 18 * @rem;
        color: #191b1f;
        font-weight: bold;
        text-align: center;
        z-index: 3;
      }
    }
    .popup-content {
      position: relative;
      width: 300 * @rem;
      border-radius: 0 0 12 * @rem;
      background-color: #ffffff;
      z-index: 3;
      overflow: hidden;
      .code-list {
        overflow-y: auto;
        height: 99 * @rem;
        padding: 0 15 * @rem;
        display: flex;
        align-content: flex-start;
        flex-wrap: wrap;
        margin-top: 18 * @rem;
        .code-item {
          width: 33.3%;
          height: 33 * @rem;
          line-height: 33 * @rem;
          text-align: center;
          font-weight: 400;
          font-size: 16 * @rem;
          color: #30343b;
        }
      }
    }
    .confirm-btn {
      width: 238 * @rem;
      height: 40 * @rem;
      border-radius: 40 * @rem;
      color: #fff;
      font-size: 15 * @rem;
      background: linear-gradient(90deg, #ff573d 0%, #ffad5c 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 26 * @rem auto 28 * @rem;
    }
  }
}
</style>
