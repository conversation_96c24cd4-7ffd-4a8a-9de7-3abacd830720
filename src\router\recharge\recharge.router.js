export default [
  {
    path: '/platform_coin',
    name: 'PlatformCoin',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/PlatformCoin'
      ),
    meta: {
      keepAlive: false,
      requiresAuth: true,
      pageTitle: '平台币充值',
    },
  },
  {
    path: '/platform_coin_detail',
    name: 'PlatformCoinDetail',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/PlatformCoinDetail'
      ),
  },
  // 重定向到金币中心 2024年9月30日13:37:18
  {
    path: '/gold_coin',
    name: 'GoldCoinBak',
    component: () =>
      import(/* webpackChunkName: "recharge" */ '@/views/Recharge/GoldCoin'),
    meta: {
      keepAlive: false,
    },
  },
  // 任务大厅path更改 2024年9月30日13:37:29
  {
    path: '/gold_coin_task',
    name: 'GoldCoin',
    component: () =>
      import(/* webpackChunkName: "recharge" */ '@/views/Recharge/GoldCoin'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
    },
  },
  {
    path: '/gold_coin_exchange',
    name: 'GoldCoinExchange',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/GoldCoin/GoldCoinExchange'
      ),
    meta: {
      keepAlive: true,
      requiresAuth: true,
    },
  },
  {
    path: '/gold_coin_detail',
    name: 'GoldCoinDetail',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/GoldCoin/GoldCoinDetail'
      ),
  },
  {
    path: '/gold_coin_tips',
    name: 'GoldCoinTips',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/GoldCoin/GoldCoinTips'
      ),
  },
  {
    path: '/exchange_ptb/:id',
    name: 'ExchangePtb',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/GoldCoin/ExchangePtb'
      ),
  },
  {
    path: '/exchange_ptb_result',
    name: 'ExchangePtbResult',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/GoldCoin/ExchangePtbResult'
      ),
  },
  // {
  //   path: '/requite',
  //   name: 'Requite',
  //   component: () =>
  //     import(/* webpackChunkName: "recharge" */ '@/views/Recharge/Requite'),
  //   meta: {
  //     requiresAuth: true,
  //     keepAlive: true,
  //   },
  // },
  // {
  //   path: '/requite_recharge',
  //   name: 'RequiteRecharge',
  //   component: () =>
  //     import(
  //       /* webpackChunkName: "recharge" */ '@/views/Recharge/RequiteRecharge'
  //     ),
  //   meta: {
  //     requiresAuth: true,
  //   },
  // },
  {
    path: '/game_recharge',
    name: 'GameRecharge',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/GameRecharge'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/game_recharge_order',
    name: 'GameRechargeOrder',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/GameRechargeOrder'
      ),
    meta: {
      requiresAuth: true,
      keepAlive: false,
    },
  },
  {
    path: '/svip',
    name: 'Svip',
    component: () =>
      import(/* webpackChunkName: "recharge" */ '@/views/Recharge/Svip'),
    meta: {
      requiresAuth: true,
      keepAlive: true,
      pageTitle: 'Svip充值',
    },
  },
  {
    path: '/svip_discount',
    name: 'SvipDiscount',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/Svip/SvipDiscount'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/changwan_card',
    name: 'ChangwanCard',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/ChangwanCard'
      ),
    meta: {
      requiresAuth: true,
      keepAlive: true,
    },
  },
  {
    path: '/savings_card',
    name: 'SavingsCard',
    component: () =>
      import(/* webpackChunkName: "recharge" */ '@/views/Recharge/SavingsCard'),
    meta: {
      requiresAuth: true,
      keepAlive: true,
      pageTitle: '省钱卡充值',
    },
  },
  {
    path: '/order_record',
    name: 'OrderRecord',
    component: () =>
      import(/* webpackChunkName: "recharge" */ '@/views/Recharge/OrderRecord'),
    meta: {
      requiresAuth: true,
      keepAlive: true,
    },
  },
  // {
  //   path: '/svip_changwan',
  //   name: 'SvipChangwan',
  //   component: () =>
  //     import(
  //       /* webpackChunkName: "recharge" */ '@/views/Recharge/SvipChangwan'
  //     ),
  //   meta: {
  //     requiresAuth: true,
  //     keepAlive: false,
  //   },
  //   children: [
  // {
  //   path: 'svip',
  //   name: 'Svip',
  //   component: () => import(/* webpackChunkName: "recharge" */ '@/views/Recharge/SvipChangwan/Svip'),
  //   meta: {
  //     requiresAuth: true,
  //     keepAlive: true
  //   }
  // },
  // {
  //   path: 'changwan_card',
  //   name: 'ChangwanCard',
  //   component: () => import(/* webpackChunkName: "recharge" */ '@/views/Recharge/SvipChangwan/ChangwanCard'),
  //   meta: {
  //     requiresAuth: true,
  //     keepAlive: true
  //   }
  // },
  //   ],
  // },
  {
    path: '/savings_secret',
    name: 'SavingsSecret',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/SavingsSecret'
      ),
    meta: {
      requiresAuth: false,
      keepAlive: false,
    },
  },
  {
    path: '/gold_coin_center',
    name: 'GoldCoinCenter',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/GoldCoinCenter'
      ),
    meta: {
      requiresAuth: true,
      keepAlive: true,
    },
  },
  {
    path: '/gold_coin_discount_game',
    name: 'GoldCoinDiscountGame',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/GoldCoinDiscountGame'
      ),
    meta: {
      requiresAuth: true,
      keepAlive: true,
    },
  },
  {
    path: '/cloud_game_buy',
    name: 'CloudGameBuy',
    component: () =>
      import(/* webpackChunkName: "recharge" */ '@/views/Recharge/CloudGame'),
    meta: {
      requiresAuth: true,
      keepAlive: true,
    },
  },
  {
    path: '/cloud_game_buy_list',
    name: 'CloudGameBuyList',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/CloudGame/CloudGameBuyList'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/cloud_game_buy_detail/:id',
    name: 'CloudGameBuyDetail',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/CloudGame/CloudGameBuyDetail'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/cloud_game_use',
    name: 'CloudGameUse',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/CloudGame/CloudGameUse'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/exchange_detail/:id',
    name: 'ExchangeDetail',
    component: () =>
      import(
        /* webpackChunkName: "recharge" */ '@/views/Recharge/GoldCoin/ExchangeDetail'
      ),
    meta: {
      requiresAuth: true,
    },
  },
];
