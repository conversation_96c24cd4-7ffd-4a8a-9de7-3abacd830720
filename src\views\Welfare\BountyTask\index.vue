<template>
  <div class="page">
    <nav-bar-2
      title=""
      bgStyle="transparent"
      :placeholder="false"
      :azShow="true"
    >
      <template #right>
        <div class="task-ruler" @click="goToRule">赏金<br />规则</div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="top-container">
        <div class="max-get-coin">
          ·最高可得<span>{{ maxGold }}</span
          >金币·
          <div class="coin-ratio">
            <img src="@/assets/images/welfare/coin-ratio.png" alt="" />
          </div>
        </div>
        <div class="top-subtitle">
          <div class="item">
            <span>01</span>
            <em>领取任务</em>
          </div>
          <div class="item">
            <span>02</span>
            <em>下载游戏</em>
          </div>
          <div class="item">
            <span>03</span>
            <em>完成任务领奖励</em>
          </div>
        </div>
        <div class="nav-list">
          <div
            class="nav-item"
            :class="{
              'active-bounty-nav1': current === navIndex && nav.orderType === 0,
              'default-bounty-nav1':
                current !== navIndex && nav.orderType === 0,
              'active-bounty-nav2': current === navIndex && nav.orderType === 1,
              'default-bounty-nav2':
                current !== navIndex && nav.orderType === 1,
              'active-bounty-nav3': current === navIndex && nav.orderType === 2,
              'default-bounty-nav3':
                current !== navIndex && nav.orderType === 2,
            }"
            v-for="(nav, navIndex) in navList"
            :key="navIndex"
            @click="changeNav(navIndex)"
          >
            {{ nav.title }}
          </div>
        </div>
      </div>
      <div class="content-container">
        <task-list
          v-if="current == 0"
          :list_subtitle="list_subtitle"
        ></task-list>
        <my-task
          v-if="current == 1"
          :my_subtitle="my_subtitle"
          @toTaskList="current = 0"
        ></my-task>
        <rank-list
          v-if="current == 2"
          :hunter_subtitle="hunter_subtitle"
        ></rank-list>
      </div>
    </div>
  </div>
</template>

<script>
import { BOX_openInNewWindow, BOX_login } from '@/utils/box.uni.js';
import {
  ApiBountyTaskIndex,
  ApiBountyTaskGetBountySubtitle,
} from '@/api/views/bounty.js';
export default {
  components: {
    taskList: () => import('./TaskList'),
    myTask: () => import('./MyTask'),
    rankList: () => import('./RankList'),
  },
  data() {
    return {
      maxGold: 0,
      ruleDocument: '',
      orderType: 0,
      current: 0, // 当前选中
      navList: [
        {
          orderType: 0,
          title: '任务中心',
        },
        {
          orderType: 1,
          title: '我的任务',
        },
        {
          orderType: 2,
          title: '最强赏金猎人',
        },
      ],
      list_subtitle: '',
      my_subtitle: '',
      hunter_subtitle: '',
    };
  },
  async created() {
    const res = await ApiBountyTaskIndex();
    this.maxGold = res.data.maxGold;
    this.ruleDocument = res.data.ruleDocument;
    const res1 = await ApiBountyTaskGetBountySubtitle();
    this.list_subtitle = res1.data.list_subtitle || '每日更新';
    this.my_subtitle = res1.data.my_subtitle || '任务详情';
    this.hunter_subtitle = res1.data.hunter_subtitle || '赏金猎人榜';
  },
  methods: {
    toGoldMall() {
      BOX_openInNewWindow(
        { name: 'GoldCoinExchange' },
        { url: `${window.location.origin}/#/gold_coin_exchange` },
      );
    },
    goToRule() {
      BOX_openInNewWindow(
        { name: 'BountyTaskRule' },
        { url: `${window.location.origin}/#/bounty_task_rule` },
      );
    },
    changeNav(navIndex) {
      if (this.current == navIndex) {
        return false;
      }
      if (this.current == 1) {
        if (!this.userInfo.token) {
          BOX_login();
          return false;
        }
      }
      this.orderType = this.navList[navIndex].orderType;
      this.current = navIndex;
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  .gold-mall-btn {
    color: #999999;
    font-size: 14 * @rem;
  }
  .task-ruler {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40 * @rem;
    height: 40 * @rem;
    background: rgba(2, 44, 90, 0.3);
    border-radius: 8 * @rem;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: bold;
    font-size: 13 * @rem;
    color: #ffffff;
    line-height: 16 * @rem;
  }
  .main {
    background-color: #b6e4f6;
    flex: 1;
    .top-container {
      box-sizing: border-box;
      position: fixed;
      z-index: 2;
      width: 100%;
      background: url(~@/assets/images/welfare/bounty-top-bg.png) center top
        no-repeat;
      background-color: #b6e4f6;
      background-size: 100% auto;
      left: 0;
      right: 0;
      margin: 0 auto;
      max-width: 450px;
      padding-top: @safeAreaTop;
      .max-get-coin {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 7 * @rem;
        background: linear-gradient(90deg, #6ce3fa 0%, #5f95f2 100%);
        border-radius: 8 * @rem;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: bold;
        font-size: 15 * @rem;
        color: #ffffff;
        line-height: 19 * @rem;
        text-shadow: 1px 1px 0px rgba(41, 98, 173, 0.4);
        position: absolute;
        top: 206 * @rem;
        left: 16 * @rem;

        span {
          color: #ffff41;
          font-size: 23 * @rem;
          line-height: 29 * @rem;
          margin: 0 2 * @rem;
        }

        .coin-ratio {
          width: 110 * @rem;
          height: 32 * @rem;
          position: absolute;
          bottom: -27 * @rem;
          right: 10 * @rem;
        }
      }
      .top-subtitle {
        margin: 276 * @rem auto 0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13 * @rem;
        color: #3b6fba;
        font-weight: 600;
        width: 342 * @rem;
        background: rgba(255, 255, 255, 0.56);
        backdrop-filter: blur(10 * @rem);
        border-radius: 20 * @rem;
        height: 36 * @rem;
        line-height: 36 * @rem;

        .item {
          display: flex;
          align-items: center;
          margin-right: 6 * @rem;

          span {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 16 * @rem;
            height: 16 * @rem;
            font-weight: bold;
            font-family:
              PingFang SC,
              PingFang SC;
            font-size: 10 * @rem;
            color: #ffffff;
            text-align: center;
            margin-right: 4 * @rem;
            background: url(~@/assets/images/welfare/task-step-bg.png) no-repeat;
            background-size: 16 * @rem 16 * @rem;
          }
          em {
            flex: 1;
            min-width: 0;
            height: 16 * @rem;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: bold;
            font-size: 13 * @rem;
            color: #3b6fba;
            line-height: 16 * @rem;
            text-align: left;
            overflow: hidden;
          }

          &::after {
            content: '';
            display: block;
            width: 15 * @rem;
            height: 12 * @rem;
            background: url(~@/assets/images/welfare/next-step-icon.png)
              no-repeat;
            background-size: 15 * @rem 12 * @rem;
            margin-left: 6 * @rem;
          }

          &:last-of-type {
            margin-right: 0;
            &::after {
              display: none;
            }
          }
        }
      }
      .nav-list {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin: 26 * @rem 10 * @rem 0;
        box-sizing: border-box;
        .nav-item {
          // background: url(~@/assets/images/welfare/bounty-nav.png) no-repeat -2 *
          //   @rem 0;
          width: 121 * @rem;
          height: 49 * @rem;

          text-align: center;
          margin: 0 auto;
          display: flex;
          justify-content: center;
          position: relative;
          font-size: 15 * @rem;
          line-height: 44 * @rem;
          color: #456fab;
          font-weight: 600;
          flex: 1;
          &.current {
            background: url(~@/assets/images/welfare/bounty-nav-current.png)
              no-repeat 0 0;
            color: #214983;

            // &::after {
            //   content: '';
            //   width: 14 * @rem;
            //   height: 9 * @rem;
            //   background: url(~@/assets/images/welfare/bounty-nav-down-icon.png)
            //     center center no-repeat;
            //   background-size: 14 * @rem 9 * @rem;
            //   position: absolute;
            //   left: 50%;
            //   bottom: -14 * @rem;
            //   transform: translateX(-50%);
            // }
          }
          &.active-bounty-nav1 {
            background: url(~@/assets/images/welfare/bounty-nav-current1.png)
              no-repeat 0 0;
            width: 121 * @rem;
            height: 49 * @rem;
            color: #214983;
            background-size: 99% 100%;
          }
          &.default-bounty-nav1 {
            background: url(~@/assets/images/welfare/bounty-nav1.png) no-repeat
              0 0;
            width: 121 * @rem;
            height: 49 * @rem;
            background-size: 99% 100%;
          }
          &.active-bounty-nav2 {
            background: url(~@/assets/images/welfare/bounty-nav-current2.png)
              no-repeat -2 * @rem 0;
            width: 121 * @rem;
            height: 49 * @rem;
            background-size: 99% 100%;
            color: #214983;
          }
          &.default-bounty-nav2 {
            background: url(~@/assets/images/welfare/bounty-nav2.png) no-repeat -2 *
              @rem 0;
            width: 119 * @rem;
            height: 49 * @rem;
            background-size: 99% 100%;
          }
          &.active-bounty-nav3 {
            background: url(~@/assets/images/welfare/bounty-nav-current2.png)
              no-repeat -2 * @rem 0;
            width: 121 * @rem;
            height: 49 * @rem;
            background-size: 99% 100%;
            color: #214983;
          }
          &.default-bounty-nav3 {
            background: url(~@/assets/images/welfare/bounty-nav2.png) no-repeat -2 *
              @rem 0;
            width: 119 * @rem;
            height: 49 * @rem;
            background-size: 99% 100%;
          }
        }
      }
    }
    .content-container {
      padding-top: 353 * @rem + 44 * @rem;
    }
  }
}
</style>
