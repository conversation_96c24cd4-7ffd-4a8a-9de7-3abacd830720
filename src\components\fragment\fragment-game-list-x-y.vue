<template>
  <div class="fragment-game-list">
    <div class="fragment-title">
      <div class="title-text">{{ info.header_title }}</div>
      <div class="right-icon" v-if="info.action_code" @click="clickMore"></div>
    </div>
    <div class="game-list">
      <div
        class="game-container"
        v-for="(games, index) in chunkArray(info.game_list, 3)"
        :key="index"
        :class="{ 'width-full': chunkArray(info.game_list, 3).length <= 1 }"
      >
        <div
          class="game-item"
          v-for="(game, index) in games"
          :key="game.id"
          @click="goToGame(game, index)"
          v-sensors-exposure="gameExposure(game, index)"
        >
          <game-item-4
            :gameInfo="game"
            :iconSize="68"
            :showHot="true"
          ></game-item-4>
          <yy-download-btn
            v-if="info.is_show_btn"
            :gameInfo="game"
            @click="goToGame(game, index)"
          ></yy-download-btn>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { BOX_goToGame } from '@/utils/box.uni.js';
import { handleActionCode } from '@/utils/actionCode.js';
export default {
  name: 'FragmentGameListX',
  props: {
    info: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  methods: {
    gameExposure(item, index) {
      return {
        'event-name': 'game_exposure',
        'property-page_name': this.$sensorsPageGet(),
        'property-section_name': this.info.header_title || '暂无',
        'property-game_id': `${item.id}`,
        'property-game_name': item.title,
        'property-game_index': `${index}`,
      };
    },
    goToGame(item, index) {
      // 神策埋点
      this.$sensorsTrack('game_click', {
        page_name: this.$sensorsPageGet(),
        section_name: this.info.header_title || '暂无',
        game_id: `${item.id}`,
        game_name: item.title,
        game_index: `${index}`,
      });
      BOX_goToGame(
        {
          params: {
            id: item.id,
            gameInfo: item,
          },
        },
        { id: item.id },
      );
    },
    clickMore() {
      handleActionCode(this.info);
    },
    chunkArray(arr, size) {
      return arr.reduce((result, item, index) => {
        if (index % size === 0) {
          result.push([]);
        }
        result[result.length - 1].push(item);
        return result;
      }, []);
    },
  },
};
</script>

<style lang="less" scoped>
.fragment-game-list {
  padding: 12 * @rem 0 9 * @rem;
  margin: 0 12 * @rem;
  background: #fff;
  border-radius: 12 * @rem;
  .fragment-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 7 * @rem 0;
    margin: 0 12 * @rem;
    .title-text {
      font-size: 16 * @rem;
      font-weight: bold;
      color: #191b1f;
      line-height: 16 * @rem;
    }
    .right-icon {
      width: 6 * @rem;
      height: 10 * @rem;
      background: url(~@/assets/images/right-icon.png) right center no-repeat;
      background-size: 6 * @rem 10 * @rem;
      padding-left: 20 * @rem;
    }
  }
  .game-list {
    display: flex;
    overflow-x: auto;
    padding: 4 * @rem 12 * @rem 0;
    &::-webkit-scrollbar {
      display: none;
    }

    .game-container {
      flex-shrink: 0;
      width: 295 * @rem;
      border-radius: 12 * @rem;
      margin-right: 20 * @rem;
      &.width-full {
        width: 100%;
      }

      &:last-of-type {
        margin-right: 0;
      }
      .game-item {
        display: flex;
        align-items: center;
        /deep/ .game-item-components {
          flex: 1;
          min-width: 0;
          padding: 7 * @rem 0;
        }
      }
    }
  }
}
</style>
