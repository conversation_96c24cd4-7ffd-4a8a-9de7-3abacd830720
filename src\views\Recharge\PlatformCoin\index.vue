<template>
  <div class="page platform-coin-page">
    <nav-bar-2
      :title="$t('平台币充值')"
      bgStyle="transparent"
      :placeholder="false"
      :bgColor="`rgba(255,255,255, ${navbarOpacity})`"
      :border="navbarOpacity ? true : false"
      :azShow="true"
    >
    </nav-bar-2>
    <div class="main">
      <div class="top-bar"></div>
      <div class="gold-bar section">
        <div class="gold-bar-item">
          <div class="item-left">{{ $t('当前余额') }}</div>
          <div class="item-right platform-gold"
            >{{ userInfo.ptb ? Number(userInfo.ptb).toFixed(1) : 0 }}
            <span>({{ userInfo.ptb_fake }}{{ $t('绑定') }})</span></div
          >
        </div>
        <div class="detail" @click="goToPtbDetail">{{ $t('明细') }}</div>
      </div>
      <div class="pay-box section">
        <div class="recharge-tips"
          >{{ $t('请选择充值金额') }}
          <span v-if="text_list.gold_welfare"
            >({{ text_list.gold_welfare }})</span
          ></div
        >
        <ul class="select-list" v-if="selectList.length">
          <li
            class="select-item"
            v-for="(item, index) in selectList"
            :key="index"
            :class="{
              on:
                selectMoney == item.price &&
                !(!item.first && item.is_only) &&
                !isDiyPay,
              cant: item.first == false,
            }"
            @click="changeMoney(item, true)"
          >
            <div class="money"
              ><span>{{ item.money_unit }}</span> {{ item.price }}</div
            >
            <div class="top">
              <div class="date">{{ item.gear_position }}</div>
              <div class="date-title">{{ item.date_unit }}</div>
            </div>

            <div
              class="tag"
              :class="{
                surprise: item.subscript_type == 2,
                fanli: item.subscript_type == 3,
              }"
              v-if="item.copywriting"
            >
              <div
                class="tag-content"
                :style="{
                  marginRight:
                    item.copywriting && item.copywriting.length > 8
                      ? '4*@rem'
                      : '0',
                }"
              >
                <div
                  :class="{
                    'text-scroll':
                      item.copywriting && item.copywriting.length > 8,
                    'text-scroll-fast':
                      item.copywriting && item.copywriting.length > 15,
                  }"
                  :style="{
                    animationDuration: `${
                      item.copywriting.length > 15
                        ? item.copywriting.length * 400
                        : item.copywriting.length * 800
                    }ms`,
                    animationDelay: 0,
                  }"
                  >{{ item.copywriting }}
                  <template v-if="item.copywriting.length > 8"
                    >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{
                      item.copywriting
                    }}
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</template
                  >
                </div></div
              >
            </div>
          </li>
          <li
            class="select-item diy"
            :class="{ on: isDiyPay }"
            @click="chooseDiy"
          >
            <input
              class="diy-pay-input"
              :placeholder="`请输入自定义充值金额(最低${money_info.min}${$t(
                '元',
              )})`"
              type="number"
              ref="diyPayMoney"
              v-model="diyPay"
            />
          </li>
        </ul>
        <div class="tips" v-if="money_info.fixedcopy">{{
          money_info.fixedcopy
        }}</div>
        <div class="welfare-list" v-if="preferentialList.length">
          <div
            class="welfare-item"
            v-for="(preferential, index) in preferentialList"
            :key="index"
          >
            <i
              class="icon"
              :class="{
                'icon-fan': preferential.type != 1,
                'icon-jian': preferential.type == 1,
              }"
            ></i>
            <div class="welfare-title"
              ><div class="title">{{ preferential.title }}</div>
              <span v-if="preferential.expire_time"
                >{{ fomateDay(preferential.expire_time)
                }}{{ $t('后失效') }}</span
              >
              <i
                class="caifuzhi"
                @click="wealthPopupShow = true"
                v-if="preferential.type == 3"
              ></i>
            </div>
            <div class="welfare-content">{{ preferential.money_desc }}</div>
          </div>
        </div>
      </div>
      <div
        class="recharge-rebate-box section"
        v-if="rebateList.list && rebateList.list.length"
      >
        <div class="box-title">
          <div class="text">{{ rebateList.title }}</div>
          <div class="small-text"
            >已充值<span>{{ rebateList.recharged_txt }}</span></div
          >
        </div>
        <div class="rebate-list">
          <div
            class="rebate-item"
            :class="{ active: rebate.status }"
            v-for="(rebate, index) in rebateList.list"
            :key="index"
          >
            <div class="rebate-item-title">{{ rebate.money_txt }}</div>
            <img :src="rebate.titlepic" alt="" />
            <div class="rebate-item-money">{{ rebate.god_txt }}</div>
            <div
              class="get-btn"
              v-if="rebate.is_receive"
              @click="getRebate(rebate)"
              >{{ rebate.receive_txt }}</div
            >
            <div class="not-reach" v-else>{{ rebate.receive_txt }}</div>
          </div>
        </div>
      </div>
      <div class="tips-box section" v-if="text_list.illustrate">
        <div class="box-title" v-if="!isSdk">
          <div class="text">{{ $t('赚平台币') }}</div>
        </div>
        <div class="tab-list" v-if="!isSdk">
          <div class="tab-item" @click="goToGoldCoinCenter">
            <img src="@/assets/images/recharge/platform-coin-tab1.png" alt="" />
          </div>
          <div class="tab-item" @click="goToGoldCoinExchange">
            <img src="@/assets/images/recharge/platform-coin-tab2.png" alt="" />
          </div>
        </div>
        <div class="small-title">{{ $t('温馨提示') }}</div>
        <div class="explain" v-if="text_list.illustrate">
          <p v-for="(item, index) in text_list.illustrate" :key="index">
            {{ item }}
          </p>
        </div>
      </div>
      <div class="buy-fixed" v-if="selectList.length">
        <div class="left">
          <div class="pay-bill">
            <div class="pay-bill-title"
              >{{ $t('充值金额') }}：{{ totalMoney.toFixed(2)
              }}{{ $t('元') }}</div
            ><span
              >({{ isDiyPay ? totalMoney * ptb_rate : selectItem.gear_position
              }}{{ $t('平台币') }})</span
            >
          </div>
          <div class="welfare-tips" v-if="newUserWelfare.expire_time">
            {{ newUserWelfare.title }}
            <span
              >{{ fomateDay(newUserWelfare.expire_time)
              }}{{ $t('后失效') }}</span
            >
          </div>
        </div>
        <div class="recharge btn" @click="selectPayWayShow = true">
          {{ $t('立即支付') }}
        </div>
      </div>
    </div>
    <pay-type-popup
      :show.sync="selectPayWayShow"
      :list="payWayList"
      @choosePayType="choosePayType"
      :money="totalMoney"
      :unit="unit"
    ></pay-type-popup>
    <van-popup
      v-model="wealthPopupShow"
      position="bottom"
      :lock-scroll="false"
      round
      class="wealth-popup"
    >
      <div class="wealth-container">
        <div class="wealth-title">{{ wealth.title }}</div>
        <div class="wealth-content">
          <p v-for="(item, index) in wealth.illustrate" :key="index">
            {{ item }}
          </p>
        </div>
        <div class="wealth-close btn" @click="wealthPopupShow = false"></div>
      </div>
    </van-popup>
    <van-popup
      v-model="preferentialPopupShow"
      round
      :close-on-click-overlay="false"
      :lock-scroll="false"
      @close="preferentialPopupClose"
      class="preferential-popup"
    >
      <img
        class="preferential-img"
        :src="preferentialPopupInfo.image"
        alt=""
        @click="goToActivity"
      />
      <div
        class="preferential-close btn"
        @click="preferentialPopupShow = false"
      ></div>
    </van-popup>
  </div>
</template>
<script>
import {
  ApiCreateOrderPtbNew,
  ApiGetPayUrl,
  ApiPlatformGetInfo,
  ApiPlatformGetNewInfo,
  ApiPlatformGetRechargeInfo,
  ApiPlatformGoldCoinCollection,
  ApiGetOrderStatus,
  ApiPlatformGetPreferential,
  ApiGetPaymentMethod,
} from '@/api/views/recharge.js';
import { formatExchangeRate } from '@/utils/tools.js';

import { mapGetters } from 'vuex';

import {
  platform,
  BOX_openInNewWindow,
  BOX_openInNewNavWindow,
  BOX_showActivityByAction,
  isSdk,
  BOX_setPayParams,
} from '@/utils/box.uni.js';
import qrcode from 'qrcodejs2';

export default {
  name: 'PlatformCoin',
  data() {
    return {
      isSdk,
      navbarOpacity: 0,
      payWay: {}, // 支付方式的对象
      selectMoney: 0,
      selectItem: {},
      ptb_rate: 10, //充值比例
      totalMoney: 0, // 准备充值的金额
      selectList: [], // 充值金额列表
      payWayList: [], // 支付方式
      text_list: {}, // 一些带翻译的文案字段
      rebateList: {}, // 每日充值返利
      selectPayWayShow: false,
      newUserWelfare: {}, // 新用户福利
      preferentialList: [], // 优惠列表
      money_info: '',
      wealth: {}, //财富值说明
      wealthPopupShow: false,
      unit: '', //单位
      diyPay: '',
      isDiyPay: false,
      preferentialPopupInfo: {}, //优惠弹窗信息
      preferentialPopupShow: false,
      preferentialPopupOnce: true,
      timer: null,
    };
  },
  watch: {
    diyPay(val, oldVal) {
      if (val < 0) {
        this.diyPay = '';
      }
      let max = Number(this.money_info.max);
      let value = Number(val);
      if (oldVal == 0 && val) {
        this.diyPay = Number(val);
      }
      if (value > max) {
        this.$toast('最高可充值' + this.unit + max);
        this.diyPay = max;
      }

      if (value != value.toFixed(2)) {
        this.diyPay = oldVal;
      }
      this.diyPaySubmit();
    },
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
  },
  async created() {
    await this.$onAppFinished;
    await this.getPlatformInfo();
    await this.getRebateList();
    this.preferentialPopupOnce = false;
    await this.getPayMethod();
  },
  mounted() {
    window.addEventListener('scroll', this.handleScroll);
  },
  beforeRouteLeave(to, from, next) {
    window.removeEventListener('scroll', this.handleScroll);
    next();
  },
  methods: {
    formatExchangeRate,
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (scrollTop > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
      } else {
        this.navbarOpacity = 0;
      }
    },
    goToPtbDetail() {
      if (isSdk) {
        this.$router.push({
          name: 'PlatformCoinDetail',
        });
      } else {
        BOX_openInNewWindow(
          { name: 'PlatformCoinDetail' },
          { url: `${window.location.origin}/#/platform_coin_detail` },
        );
      }
    },
    choosePayType(selectedPayType) {
      this.payWay = selectedPayType;
      this.handlePay();
    },
    handlePay() {
      let recodeId =
        (this.preferentialList.length && this.preferentialList[0].id) || 0;
      ApiCreateOrderPtbNew({
        isNew: 1,
        money: this.totalMoney,
        payWay: this.payWay.symbol,
        recodeId: recodeId,
        positionId: this.selectItem.id || 0,
      }).then(orderRes => {
        // 安卓sdk下单上报
        BOX_setPayParams({
          order_id: orderRes.data.orderId,
          productname: '充值平台币',
        });

        // 神策埋点
        this.$sensorsTrack('coin_payment_submit', {
          order_id: orderRes.data.orderId,
          coin_amount: Number(this.isDiyPay ? this.totalMoney * this.ptb_rate : this.selectItem.gear_position),
          recharge_amount: Number(this.isDiyPay ? this.totalMoney : this.selectItem.price),
          actual_recharge_amount: Number(this.totalMoney),
          recharge_source: this.$sensorsChainGet(),
        }, 'recharge_source');

        ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: 102,
          payWay: this.payWay.symbol,
          packageName: '',
        }).finally(() => {
          ApiGetOrderStatus({
            order_id: orderRes.data.orderId,
            order_type: 102,
          })
            .then(res2 => {
            })
            .catch(() => {
            })
            .finally(() => {
              this.getPlatformInfo();
              this.getRebateList();
            });
        });
      });
    },
    async getPayMethod() {
      let res = await ApiGetPaymentMethod({
        orderType: 102,
      });
      this.payWayList = res.data;
      this.payWay = this.payWayList[0];
    },
    async changeMoney(item, click = false) {
      if (click && (!item.price || item.price == this.selectMoney)) {
        return false;
      }
      if (item.first == false) {
        this.$toast('仅限首次充值');
        return false;
      }
      clearInterval(this.timer);
      this.timer = null;
      this.selectMoney = item.price;
      this.selectItem = item;
      this.isDiyPay = false;
      this.getPreferential();
    },
    async getPreferential() {
      const toastLoading = this.$toast.loading('加载中');
      try {
        let res = await ApiPlatformGetPreferential({
          id: this.selectItem.id || '',
        });
        let { list, error, pay_money } = res.data;
        if (error) {
          this.$toast(error);
        } else {
          toastLoading.clear();
        }
        // 2025-02-24隐藏财富值
        // this.preferentialList = [
        //   ...list,
        //   {
        //     title: this.$t('下单返财富值'),
        //     money_desc: `返 ${this.userInfo.is_hw ? (Number(pay_money) * 10 * 7).toFixed(1) : (Number(pay_money) * 10).toFixed(1)}${this.$t('财富值')}`,
        //     type: 3,
        //   },
        // ];
        this.preferentialList = list;
        this.totalMoney = Number(pay_money);
        this.newUserWelfare =
          list.find(item => {
            return item.type;
          }) || {};
        if (this.newUserWelfare.type) {
          this.timer = setInterval(() => {
            if (
              this.newUserWelfare.expire_time <=
              Math.floor(new Date().getTime() / 1000)
            ) {
              this.getPlatformInfo(true);
              clearInterval(this.timer);
              this.timer = null;
              return false;
            }
            this.newUserWelfare.expire_time = this.newUserWelfare.expire_time--;
          }, 1000);
        }
      } catch {
        toastLoading.clear();
        console.log('NetWork Error');
      }
    },
    async getPlatformInfo(flag = false) {
      const res = await ApiPlatformGetNewInfo();
      let {
        payWayList,
        platcoinList,
        text_list,
        money_info,
        wealth_list,
        popUp,
        ptb_rate,
      } = res.data;
      this.selectList = platcoinList;
      this.text_list = text_list;
      this.ptb_rate = ptb_rate;
      this.text_list.illustrate = text_list.illustrate.split('<br>');
      this.money_info = money_info;
      this.wealth = wealth_list;
      this.wealth.illustrate = wealth_list.illustrate.split('<br>');
      this.unit = this.selectList.length ? this.selectList[0].money_unit : '¥';
      this.preferentialPopupInfo = popUp || {};
      if (this.preferentialPopupInfo.image && this.preferentialPopupOnce) {
        this.preferentialPopupShow = true;
        return false;
      }
      if (!flag) {
        let info =
          platcoinList.find(item => {
            return item.is_recommend == 1;
          }) || platcoinList[0];
        this.changeMoney(info);
      } else {
        this.getPreferential();
      }
    },
    async getRebateList() {
      let res = await ApiPlatformGetRechargeInfo();
      this.rebateList = res.data;
    },
    async getRebate(item) {
      await ApiPlatformGoldCoinCollection({
        id: item.id,
      });
      await this.getRebateList();
    },
    chooseDiy() {
      this.isDiyPay = true;
      this.selectMoney = 0;
      this.selectItem = {};
      this.newUserWelfare = {};
      this.totalMoney = Number(this.diyPay);
      this.$nextTick(() => {
        this.$refs.diyPayMoney.focus();
      });
      // 2025-02-24隐藏财富值
      // this.preferentialList = [
      //   {
      //     title: this.$t('下单返财富值'),
      //     money_desc: `返 ${this.userInfo.is_hw ? (Number(this.totalMoney) * 10 * 7).toFixed(1) : (Number(this.totalMoney) * 10).toFixed(1)}${this.$t('财富值')}`,
      //     type: 3,
      //   },
      // ];
      this.preferentialList = [];
    },
    diyPaySubmit() {
      this.totalMoney = Number(this.diyPay);
      // 2025-02-24隐藏财富值
      // this.preferentialList = [
      //   {
      //     title: this.$t('下单返财富值'),
      //     money_desc: `返 ${this.userInfo.is_hw ? (Number(this.totalMoney) * 10 * 7).toFixed(1) : (Number(this.totalMoney) * 10).toFixed(1)}${this.$t('财富值')}`,
      //     type: 3,
      //   },
      // ];
      this.preferentialList = [];
    },
    preferentialPopupClose() {
      let info =
        this.selectList.find(item => {
          return item.is_recommend == 1;
        }) || this.selectList[0];
      this.changeMoney(info);
    },
    fomateDay(timestamp) {
      let second = timestamp * 1000 - new Date().getTime();
      let day = Math.floor(second / (24 * 60 * 60 * 1000));
      let hour = Math.floor(
        (second - day * 24 * 60 * 60 * 1000) / (60 * 60 * 1000),
      );
      let minute = Math.floor(
        (second - day * 24 * 60 * 60 * 1000 - hour * 60 * 60 * 1000) /
          (60 * 1000),
      );
      let seconds = Math.floor(
        (second -
          day * 24 * 60 * 60 * 1000 -
          hour * 60 * 60 * 1000 -
          minute * 60 * 1000) /
          1000,
      );
      if (!day) {
        return `${hour > 9 ? hour : '0' + hour}:${
          minute > 9 ? minute : '0' + minute
        }:${seconds > 9 ? seconds : '0' + seconds}`;
      }
      return `${day}天${hour > 9 ? hour : '0' + hour}:${
        minute > 9 ? minute : '0' + minute
      }:${seconds > 9 ? seconds : '0' + seconds}`;
    },
    goToGoldCoinCenter() {
      try {
        BOX_showActivityByAction({
          action_code: 30,
          web_url: 'Welfare',
          type: 1,
        });
      } catch (e) {
        BOX_openInNewWindow(
          { name: 'GoldCoinCenter' },
          { url: `${window.location.origin}/#/gold_coin_center` },
        );
      }
      // this.$router.push({ name: 'GoldCoinCenter' });
    },
    goToGoldCoinExchange() {
      try {
        BOX_showActivityByAction({
          action_code: 30,
          web_url: 'WelfareGoldCoinExchange',
        });
      } catch (e) {
        BOX_openInNewWindow(
          { name: 'GoldCoinExchange' },
          { url: `${window.location.origin}/#/gold_coin_exchange` },
        );
      }
    },
    goToActivity() {
      if (this.preferentialPopupInfo.jump_url) {
        if (
          this.preferentialPopupInfo.jump_url.indexOf('activity.3733.com') > -1
        ) {
          BOX_openInNewWindow(
            {
              name: 'Activity',
              params: { url: this.preferentialPopupInfo.jump_url },
            },
            { url: this.preferentialPopupInfo.jump_url },
          );
        } else if (this.preferentialPopupInfo.page_name) {
          BOX_openInNewWindow(
            { name: this.preferentialPopupInfo.page_name },
            { url: this.preferentialPopupInfo.jump_url },
          );
        } else {
          BOX_openInNewNavWindow(
            { h5_url: this.preferentialPopupInfo.jump_url },
            { url: this.preferentialPopupInfo.jump_url },
          );
        }
      }
      this.preferentialPopupShow = false;
    },
  },
};
</script>

<style lang="less" scoped>
.platform-coin-page {
  .top-bar {
    display: block;
    width: 100%;
    height: 232 * @rem;
    background: url(~@/assets/images/recharge/platform-coin-bg.png) no-repeat;
    background-size: 100%;
  }

  /deep/ .nav-bar {
    .nav-title {
      font-weight: bold;
    }
  }

  .section {
    background-color: #fff;
    padding: 0 12 * @rem;
    width: 351 * @rem;
    border-radius: 12 * @rem;
    margin: 16 * @rem auto 0;
    box-sizing: border-box;
    overflow: hidden;
    position: relative;
    z-index: 1;
  }

  .gold-bar {
    margin-top: -170 * @rem;
    margin-top: calc(-170 * @rem + @safeAreaTop);
    margin-top: calc(-170 * @rem + @safeAreaTopEnv);
    background: url(~@/assets/images/recharge/my-platform-coin-bg.png) no-repeat;
    background-size: 351 * @rem 95 * @rem;

    .gold-bar-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      height: 95 * @rem;

      .item-left {
        height: 20 * @rem;
        font-weight: 500;
        font-size: 14 * @rem;
        color: #ffffff;
        line-height: 20 * @rem;
        text-align: left;
      }

      .platform-gold {
        display: flex;
        align-items: flex-end;
        font-size: 28 * @rem;
        line-height: 28 * @rem;
        color: #fff;
        font-weight: bold;
        margin-top: 9 * @rem;

        span {
          font-weight: 400;
          font-size: 12 * @rem;
          color: #ffffff;
          line-height: 17 * @rem;
          text-align: left;
          margin-left: 3 * @rem;
        }
      }
    }

    .detail {
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: 0;
      right: 0;
      width: 73 * @rem;
      height: 30 * @rem;
      font-weight: 500;
      font-size: 12 * @rem;
      color: #ffffff;
      line-height: 12 * @rem;
      text-align: center;

      &::after {
        content: '';
        display: block;
        width: 4 * @rem;
        height: 8 * @rem;
        background: url(~@/assets/images/recharge/white-right-arrow.png)
          no-repeat;
        background-size: 4 * @rem 8 * @rem;
        margin-left: 3 * @rem;
        margin-top: -2 * @rem;
      }
    }
  }

  .main {
    background-color: #f8f8f9;
    padding-bottom: 80 * @rem;
    padding-bottom: calc(80 * @rem + @safeAreaBottom / 2);
    padding-bottom: calc(80 * @rem + @safeAreaBottomEnv / 2);

    .box-title {
      display: flex;
      align-items: center;
      padding: 16 * @rem 0 12 * @rem;
      .text {
        height: 23 * @rem;
        font-weight: bold;
        font-size: 16 * @rem;
        color: #121212;
        line-height: 23 * @rem;
        text-align: left;
        padding-right: 9 * @rem;
        background: url(~@/assets/images/recharge/title-bg.png) no-repeat bottom
          right;
        background-size: 48 * @rem 23 * @rem;
        background-origin: padding-box;
      }
      .small-text {
        height: 11 * @rem;
        font-weight: 400;
        font-size: 11 * @rem;
        color: #979797;
        line-height: 11 * @rem;
        text-align: left;
        margin-top: 5 * @rem;

        span {
          color: #1cce94;
        }
      }
    }

    .small-title {
      color: #121212;
      line-height: 22 * @rem;
      font-size: 14 * @rem;
      font-weight: bold;
      padding-top: 10 * @rem;
    }

    .pay-box {
      padding-bottom: 16 * @rem;
      .recharge-tips {
        display: flex;
        align-items: flex-end;
        height: 22 * @rem;
        font-weight: bold;
        font-size: 16 * @rem;
        color: #121212;
        line-height: 22 * @rem;
        padding-top: 16 * @rem;
        span {
          height: 11 * @rem;
          font-weight: 400;
          font-size: 11 * @rem;
          color: #979797;
          line-height: 11 * @rem;
          text-align: left;
          margin-left: 5 * @rem;
          margin-bottom: 3 * @rem;
        }
      }
      .select-list {
        display: flex;
        flex-wrap: wrap;
        background-color: #fff;
        padding: 12 * @rem 0 6 * @rem;

        .select-item {
          position: relative;
          box-sizing: border-box;
          width: 103 * @rem;
          height: 72 * @rem;
          margin-bottom: 10 * @rem;
          margin-right: 8 * @rem;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background-color: #f8f8f9;
          border-radius: 6 * @rem;
          cursor: pointer;
          border: 0.5 * @rem solid #f8f8f9;

          &:nth-of-type(3n) {
            margin-right: 0;
          }

          .tag {
            max-width: 90%;
            box-sizing: border-box;
            position: absolute;
            left: -1 * @rem;
            top: -6 * @rem;
            height: 20 * @rem;
            background: #1cce94;
            border-radius: 6 * @rem 6 * @rem 6 * @rem 0 * @rem;
            display: flex;
            align-items: center;
            font-size: 11 * @rem;
            color: #ffffff;
            line-height: 1.1;
            white-space: nowrap;

            &.surprise {
              background-color: #ff5506;

              &::before {
                content: '';
                display: block;
                height: 17 * @rem;
                width: 17 * @rem;
                background: url(~@/assets/images/recharge/tag-surprise.png)
                  no-repeat;
                background-size: 17 * @rem 17 * @rem;
                position: absolute;
                top: -2 * @rem;
                left: -5 * @rem;
                z-index: 2;
              }

              .tag-content {
                padding: 0 * @rem 8 * @rem 0 0;
                margin-left: 13 * @rem;
              }
            }
            &.fanli {
              background: linear-gradient(90deg, #4a0f0a 0%, #9c3a18 100%);

              &::before {
                content: '';
                display: block;
                height: 17 * @rem;
                width: 17 * @rem;
                background: url(~@/assets/images/recharge/tag-fanli.png)
                  no-repeat;
                background-size: 17 * @rem 17 * @rem;
                position: absolute;
                top: -2 * @rem;
                left: -5 * @rem;
                z-index: 2;
              }

              .tag-content {
                padding: 0 * @rem 8 * @rem 0 0;
                margin-left: 14 * @rem;
              }
            }
            .tag-content {
              display: flex;
              padding: 0 * @rem 8 * @rem;
              overflow: hidden;
              margin-right: 4 * @rem;
            }
            .text-scroll {
              white-space: nowrap;
              animation: scroll-left 3s linear forwards infinite;
            }
            .text-scroll-fast {
              white-space: nowrap;
              animation: scroll-left-fast 3s linear forwards infinite;
            }
            @keyframes scroll-left {
              0% {
                transform: translateX(0);
              }
              50% {
                transform: translateX(-50%);
              }
              100% {
                transform: translateX(-50%);
              }
            }
            @keyframes scroll-left-fast {
              0% {
                transform: translateX(0);
              }
              70% {
                transform: translateX(-50%);
              }
              100% {
                transform: translateX(-50%);
              }
            }
          }

          .top {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 5 * @rem;
          }

          .date {
            height: 12 * @rem;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #666666;
            line-height: 12 * @rem;
            text-align: center;
          }

          .date-title {
            height: 12 * @rem;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #666666;
            line-height: 12 * @rem;
            text-align: center;
          }

          .money {
            display: flex;
            align-items: flex-end;
            height: 28 * @rem;
            font-weight: bold;
            font-size: 20 * @rem;
            color: #121212;
            line-height: 28 * @rem;
            text-align: center;

            span {
              height: 12 * @rem;
              font-weight: 400;
              font-size: 12 * @rem;
              color: #121212;
              line-height: 12 * @rem;
              text-align: left;
              margin-bottom: 6 * @rem;
              margin-right: 1 * @rem;
            }
          }

          &.on {
            border-color: #21b98a;
            border-width: 0.5 * @rem;
            background: linear-gradient(180deg, #ecfbf4 0%, #f9fffc 95%);

            .date {
              color: #21b98a;
            }

            .date-title {
              color: #21b98a;
            }

            .money {
              color: #21b98a;
              color: @themeColor;
              span {
                color: #21b98a;
              }
            }
          }

          &.cant {
            opacity: 0.5;
          }

          &.diy {
            width: 100%;
            height: 42 * @rem;
            margin-right: 0;
            margin-bottom: 0;

            &.on .diy-pay-input {
              color: #21b98a;
            }

            .diy-pay-input {
              display: block;
              width: 100%;
              height: 100%;
              font-weight: bold;
              background-color: transparent;
              text-align: left;
              font-size: 16 * @rem;
              line-height: 20 * @rem;
              padding: 0 12 * @rem;
              box-sizing: border-box;
              color: #000;
              caret-color: @themeColor;

              &::placeholder {
                font-size: 14 * @rem;
                line-height: 20 * @rem;
                font-weight: normal;
                color: #979797;
                text-align: left;
              }
            }
          }
        }
      }
      .tips {
        height: 14 * @rem;
        font-weight: 400;
        font-size: 10 * @rem;
        color: #979797;
        line-height: 14 * @rem;
        text-align: justify;
      }
      .welfare-list {
        background-color: #fcfcfc;
        border: 1 * @rem solid #eeeeee;
        border-radius: 8 * @rem;
        margin-top: 20 * @rem;

        .welfare-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 44 * @rem;
          padding: 0 8 * @rem;
          box-sizing: border-box;
          border-bottom: 0.5 * @rem solid #eeeeee;

          &:last-of-type {
            border-bottom: none;
          }

          .icon {
            flex-shrink: 0;
            width: 18 * @rem;
            height: 18 * @rem;
            margin-right: 4 * @rem;

            &.icon-fan {
              background: url(~@/assets/images/recharge/icon-fan.png) no-repeat;
              background-size: 18 * @rem;
            }

            &.icon-jian {
              background: url(~@/assets/images/recharge/icon-jian.png) no-repeat;
              background-size: 18 * @rem;
            }
          }

          .welfare-title {
            flex: 1;
            min-width: 0;
            display: flex;
            align-items: center;
            height: 20 * @rem;
            font-weight: 500;
            font-size: 13 * @rem;
            color: #222222;
            line-height: 20 * @rem;
            text-align: left;

            span {
              height: 15 * @rem;
              font-weight: 500;
              font-size: 10 * @rem;
              color: #fe6600;
              line-height: 15 * @rem;
              margin-left: 4 * @rem;
              margin-top: 1 * @rem;
            }

            .caifuzhi {
              flex-shrink: 0;
              display: block;
              width: 14 * @rem;
              height: 14 * @rem;
              background: url(~@/assets/images/recharge/coin-rule.png) no-repeat;
              background-size: 14 * @rem 14 * @rem;
              margin-left: 4 * @rem;
            }
          }

          .welfare-content {
            display: flex;
            align-items: center;
            height: 20 * @rem;
            font-weight: 500;
            font-size: 13 * @rem;
            color: #fc3a3b;
            line-height: 20 * @rem;

            span {
              height: 15 * @rem;
              font-weight: 500;
              font-size: 11 * @rem;
              color: #fc3a3b;
              line-height: 15 * @rem;
              margin-right: 4 * @rem;
              margin-top: 2 * @rem;
            }
          }
        }
      }
    }

    .recharge-rebate-box {
      .rebate-list {
        display: flex;
        overflow-x: auto;
        padding-bottom: 10 * @rem;
        &::-webkit-scrollbar {
          display: none;
        }
        .rebate-item {
          flex-shrink: 0;
          width: 119 * @rem;
          height: 128 * @rem;
          background: #f8f8f9;
          border-radius: 6 * @rem;
          padding: 14 * @rem;
          margin-right: 10 * @rem;
          box-sizing: border-box;
          position: relative;

          &:last-of-type {
            margin-right: 0;
          }

          .rebate-item-title {
            height: 14 * @rem;
            font-size: 14 * @rem;
            color: #121212;
            line-height: 14 * @rem;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          img {
            height: 30 * @rem;
            width: auto;
            margin: 13 * @rem auto 0;
          }

          .rebate-item-money {
            height: 12 * @rem;
            font-weight: bold;
            font-size: 12 * @rem;
            line-height: 12 * @rem;
            // text-stroke: 1 * @rem #ffffff;
            // -webkit-text-stroke: 1 * @rem #ffffff;
            // -moz-text-stroke: 1 * @rem #ffffff;
            text-shadow:
              -1 * @rem -1 * @rem 0 #fff,
              1 * @rem -1 * @rem 0 #fff,
              -1 * @rem 1 * @rem 0 #fff,
              1 * @rem 1 * @rem 0 #fff;
            color: #f1780c;
            text-align: center;
            margin-top: -3 * @rem;
            position: relative;
            z-index: 1;
          }
          .get-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 51 * @rem;
            height: 24 * @rem;
            background: #1cce94;
            border-radius: 12 * @rem;
            margin: 10 * @rem auto 0;
            font-weight: 500;
            font-size: 12 * @rem;
            color: #ffffff;
            line-height: 12 * @rem;
            text-align: center;
          }

          .not-reach {
            height: 12 * @rem;
            font-weight: 500;
            font-size: 12 * @rem;
            color: #666666;
            line-height: 12 * @rem;
            text-align: center;
            margin-top: 16 * @rem;
          }

          &.active {
            background: linear-gradient(180deg, #ecfbf4 0%, #f9fffc 95%);
          }
        }
      }
    }

    .tips-box {
      .tab-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        .tab-item {
          width: 155 * @rem;
          height: 78 * @rem;
          border-radius: 6 * @rem;
          margin-bottom: 10 * @rem;
        }
      }

      .explain {
        margin-top: 4 * @rem;
        font-size: 12 * @rem;
        color: #979797;
        line-height: 19 * @rem;

        p {
          display: flex;
          margin-bottom: 10 * @rem;

          &::before {
            content: '';
            flex-shrink: 0;
            display: block;
            width: 4 * @rem;
            height: 4 * @rem;
            border-radius: 50%;
            background-color: #979797;
            margin-right: 4 * @rem;
            margin-top: 7 * @rem;
          }
        }
      }
    }

    .buy-fixed {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: #fff;
      padding: 14 * @rem 12 * @rem;
      padding-bottom: calc(14 * @rem + @safeAreaBottom / 2);
      padding-bottom: calc(14 * @rem + @safeAreaBottomEnv / 2);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      box-shadow: 0 * @rem -1 * @rem 8 * @rem 0 * @rem rgba(0, 0, 0, 0.08);

      .left {
        flex: 1;
        min-width: 0;

        .pay-bill {
          display: flex;
          align-items: flex-end;

          .pay-bill-title {
            height: 16 * @rem;
            font-weight: bold;
            font-size: 16 * @rem;
            color: #000000;
            line-height: 16 * @rem;
            text-align: left;
          }
          span {
            height: 11 * @rem;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 400;
            font-size: 11 * @rem;
            color: #979797;
            line-height: 11 * @rem;
            text-align: left;
            margin-left: 4 * @rem;
          }
        }
        .welfare-tips {
          height: 12 * @rem;
          font-weight: 400;
          font-size: 11 * @rem;
          color: #979797;
          line-height: 12 * @rem;
          text-align: left;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          margin-top: 10 * @rem;

          span {
            color: #ff6649;
          }
        }
      }

      .recharge {
        width: 98 * @rem;
        height: 38 * @rem;
        line-height: 38 * @rem;
        text-align: center;
        letter-spacing: 0.625 * @rem;
        font-size: 14 * @rem;
        font-weight: bold;
        color: #ffffff;
        background: @themeBg;
        border-radius: 25 * @rem;
        position: relative;

        &.no {
          background: #c5c5c5;
        }

        .welfare-tips {
          display: flex;
          align-items: center;
          height: 28.5 * @rem;
          font-weight: 500;
          font-size: 10 * @rem;
          color: #ffffff;
          line-height: 14 * @rem;
          background: url(~@/assets/images/recharge/tips-bg.png) no-repeat;
          background-size: 100% 28.5 * @rem;
          position: absolute;
          top: -17 * @rem;
          left: -6 * @rem;
          padding: 0 8 * @rem 5 * @rem;
          box-sizing: border-box;
        }
      }
    }
  }

  .wealth-container {
    position: relative;

    .wealth-close {
      width: 20 * @rem;
      height: 20 * @rem;
      background: url(~@/assets/images/close-gray.png) no-repeat;
      background-size: 20 * @rem 20 * @rem;
      position: absolute;
      top: 20 * @rem;
      right: 14 * @rem;
    }

    .wealth-title {
      display: block;
      width: 100%;
      height: 28 * @rem;
      font-weight: bold;
      font-size: 20 * @rem;
      color: #121212;
      line-height: 28 * @rem;
      text-align: center;
      padding-top: 25 * @rem;
    }

    .wealth-content {
      padding: 20 * @rem;
      font-weight: 400;
      font-size: 12 * @rem;
      color: #8a848a;
      line-height: 14 * @rem;
      text-align: justify;

      p {
        display: flex;
        margin-bottom: 12 * @rem;

        &::before {
          content: '';
          flex-shrink: 0;
          display: block;
          width: 4 * @rem;
          height: 4 * @rem;
          border-radius: 50%;
          background-color: #8a848a;
          margin-right: 6 * @rem;
          margin-top: 6 * @rem;
        }
      }
    }
  }

  .diy-pay-container {
    background-color: #f3f5f9;
    padding-bottom: 35 * @rem;
    position: relative;

    .diy-pay-title {
      height: 25 * @rem;
      font-weight: 500;
      font-size: 18 * @rem;
      color: #000000;
      line-height: 25 * @rem;
      text-align: center;
      padding: 16 * @rem;
    }

    .diy-pay-content {
      margin-top: 13 * @rem;

      .money {
        display: flex;
        align-items: center;
        justify-content: center;

        span {
          height: 22 * @rem;
          font-weight: 500;
          font-size: 16 * @rem;
          color: #333333;
          line-height: 22 * @rem;
          margin-top: 10 * @rem;
        }

        input {
          width: 100 * @rem;
          height: 50 * @rem;
          font-weight: 500;
          font-size: 36 * @rem;
          color: #333333;
          line-height: 50 * @rem;
          background-color: #f3f5f9;
        }
      }

      .tips {
        height: 17 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #979797;
        line-height: 17 * @rem;
        text-align: center;
        margin-top: 12 * @rem;
      }
    }

    .diy-pay-close {
      display: block;
      width: 30 * @rem;
      height: 30 * @rem;
      background: url(~@/assets/images/recharge/popup-back.png) no-repeat center
        center;
      background-size: 20 * @rem 20 * @rem;
      position: absolute;
      top: 12 * @rem;
      left: 7 * @rem;
      z-index: 3;
    }

    .submit {
      display: block;
      height: 21 * @rem;
      line-height: 21 * @rem;
      font-size: 15 * @rem;
      color: #21b98a;
      font-weight: bold;
      padding: 0 3 * @rem;
      position: absolute;
      top: 18 * @rem;
      right: 12 * @rem;
      z-index: 3;
    }
  }

  .preferential-popup {
    overflow-y: unset;
    background-color: transparent;
    width: 288 * @rem;

    .preferential-container {
      width: 300 * @rem;
      height: 234 * @rem;
      background: url(~@/assets/images/recharge/welfare-popup-bg.png) no-repeat;
      background-size: 300 * @rem 234 * @rem;
      position: relative;
      box-sizing: border-box;

      &.coupon-container {
        padding-top: 60 * @rem;
        height: 248 * @rem;
        background-size: 300 * @rem 248 * @rem;

        .get {
          margin-top: 40 * @rem;
        }
      }

      .preferential-title {
        width: 168 * @rem;
        height: 118 * @rem;
        position: absolute;
        top: -46 * @rem;
        left: 50%;
        transform: translateX(-50%);
        background: url(~@/assets/images/recharge/surprise-title2.png) no-repeat;
        background-size: 168 * @rem 118 * @rem;

        &.type1 {
          width: 199 * @rem;
          height: 46 * @rem;
          top: -26 * @rem;
          background: url(~@/assets/images/recharge/surprise-title1.png)
            no-repeat;
          background-size: 199 * @rem 46 * @rem;
        }

        &.type3 {
          background-image: url(~@/assets/images/recharge/surprise-title3.png);
        }

        &.type4 {
          background-image: url(~@/assets/images/recharge/surprise-title4.png);
        }
      }

      .coupon {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 238 * @rem;
        height: 88 * @rem;
        background: url(~@/assets/images/recharge/surprise-coupon-bg.png)
          no-repeat;
        background-size: 238 * @rem 88 * @rem;
        margin: 0 auto;

        .coupon-title {
          width: 127 * @rem;
          font-size: 18 * @rem;
          line-height: 25 * @rem;
          text-align: center;
          color: #ffffff;
          font-weight: bold;
          overflow: hidden;
        }

        .right {
          width: 93 * @rem;

          .money {
            display: flex;
            justify-content: center;
            font-weight: normal;
            font-size: 24 * @rem;
            color: #f30f03;
            line-height: 28 * @rem;
            text-align: center;
            font-weight: bold;

            span {
              flex-shrink: 0;
              height: 20 * @rem;
              font-weight: bold;
              font-size: 14 * @rem;
              color: #f30f03;
              line-height: 20 * @rem;
            }
          }

          .reach-money {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 17 * @rem;
            font-weight: 500;
            font-size: 12 * @rem;
            color: #d65650;
            line-height: 17 * @rem;
            text-align: center;
          }
        }
      }

      .text-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 170 * @rem;
        padding-top: 60 * @rem;
        box-sizing: border-box;

        .text-title {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 42 * @rem;
          font-weight: bold;
          font-size: 30 * @rem;
          line-height: 42 * @rem;
          text-align: center;

          img {
            width: auto;
            height: 21 * @rem;
          }
        }

        .text-content {
          width: 100%;
          height: 25 * @rem;
          font-size: 18 * @rem;
          color: #992721;
          font-weight: bold;
          line-height: 25 * @rem;
          text-align: center;
          margin-top: 6 * @rem;
        }
      }

      .get {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 238 * @rem;
        height: 40 * @rem;
        font-weight: 500;
        font-size: 15 * @rem;
        color: #ffffff;
        line-height: 21 * @rem;
        text-align: center;
        background: #3cd279;
        border-radius: 24 * @rem;
        margin: 0 auto;
      }
    }

    .preferential-close {
      display: block;
      width: 28 * @rem;
      height: 28 * @rem;
      background: url(~@/assets/images/recharge/welfare-popup-close.png)
        no-repeat;
      background-size: 28 * @rem 28 * @rem;
      position: absolute;
      right: 0;
      top: -24 * @rem;
      // bottom: -68 * @rem;
      // left: 50%;
      // transform: translateX(-50%);
    }
  }
}
</style>
