<template>
  <div class="svip-page">
    <rubber-band :topColor="'#1E1916'" :bottomColor="'#161207'">
      <nav-bar-2
        class="nav-bar"
        :title="$t('会员中心')"
        :placeholder="false"
        :bgStyle="bgStyle"
        :bgColor="`rgba(28, 23, 19, ${navbarOpacity})`"
        :azShow="true"
      >
        <template #right>
          <div
            v-if="!isSdk"
            class="sign-in-btn"
            :class="{ black: bgStyle == 'transparent' }"
            @click="goToSignIn"
          >
            {{ $t('每日签到') }}
          </div>
        </template>
      </nav-bar-2>
      <div class="top-bg">
        <div class="diamond"></div>
        <div class="diamond-bottom"></div>
      </div>
      <div class="top-bar">
        <div class="user-bar">
          <div class="no-login" v-if="!userInfo.username" @click="toLogin">
            <user-avatar class="avatar"></user-avatar>
            <div class="no-login-text">点击登录</div>
          </div>
          <div class="user-info" v-else>
            <user-avatar class="avatar"></user-avatar>
            <div class="info-right">
              <div class="nickname">
                <div class="text">
                  {{ userInfo.nickname }}
                </div>
                <span
                  class="svip-icon"
                  :class="{ had: userSvipInfo.svip_status == 1 }"
                ></span>
              </div>
              <div class="end-date" v-if="userSvipInfo.svip_status == 1">
                {{ $t('尊享13+项特权') }}，{{ $t('额外收益') }}:{{
                  userSvipInfo.extra_gold
                }}{{ $t('金币') }}
              </div>
              <div class="end-date" v-else>{{
                $t('开通SVIP，尊享13+项特权')
              }}</div>
            </div>
          </div>

          <div class="svip-intro" v-if="userSvipInfo.svip_status == 1">
            {{ $t('到期时间') }}：{{ userSvipInfo.svip_time }}
          </div>
          <div class="svip-intro" v-else>
            {{ text_list.vip_fuli }}
          </div>
        </div>
      </div>
      <div class="main">
        <!-- 套餐选择 -->
        <div class="container choose-box">
          <div class="container-title">
            {{ $t('服务选择') }}
          </div>
          <div class="svip-list">
            <template v-for="(item, index) in svipList">
              <div
                class="svip-item"
                :key="index"
                :class="{ on: selectedMeal.amount == item.amount }"
                v-if="
                  item.is_first
                    ? userSvipInfo.is_first_recharge == 1
                      ? false
                      : true
                    : true
                "
                @click="selectedMeal = item"
              >
                <div
                  v-if="item.tips"
                  class="tips"
                  :class="{ tips1: item.is_first }"
                >
                  {{ item.tips }}
                </div>
                <div class="content-top">
                  <div
                    class="right-tip"
                    v-if="item.show_tips.amount || item.show_tips.desc"
                    >{{ item.show_tips.amount || item.show_tips.desc }}</div
                  >
                  <div class="month">{{ item.title }}</div>
                  <div class="money">
                    <div class="now">
                      <span>{{ text_list.pay_symbol }}</span
                      >{{ item.amount }}
                    </div>
                    <div class="old" v-if="item.show_amount">
                      {{ text_list.pay_symbol
                      }}<span>{{ item.show_amount }}</span>
                    </div>
                  </div>
                </div>
                <div class="bottom-desc">
                  {{ $t('开通立返') }}<span>{{ item.rebate_gold }}</span
                  >{{ $t('金币') }}
                </div>
              </div>
            </template>
          </div>
          <div class="buy-fixed">
            <div class="recharge-btn btn" @click="clickRechargeBtn">
              <div class="recharge-text" v-if="userSvipInfo.svip_status == 1">{{
                $t('立即续费')
              }}</div>
              <div class="recharge-text" v-else>{{ $t('立即开通') }}</div>
              <div class="discount" v-if="svipZhekouTxt">{{
                svipZhekouTxt
              }}</div>
              <!-- <div class="time-out">
              {{ $t('距离优惠结束仅剩') }}<span>{{ countDownText }}</span>
            </div> -->
            </div>
          </div>
          <div class="buy-history" v-if="buySvipList.length && swiperShow">
            <div class="laba"></div>
            <swiper :options="swiperOption" class="history-list">
              <swiper-slide
                v-for="(item, index) in buySvipList"
                :key="index"
                class="swiper-no-swiping"
              >
                <div class="history-item">
                  <div class="nickname">{{ item.nickname }}</div>
                  <div class="history-desc">
                    {{ item.time }}{{ $t('购买了') }}<span>{{ item.day }}</span>
                  </div>
                </div>
              </swiper-slide>
            </swiper>
          </div>
        </div>
        <div class="welfare-section-title">
          <span>{{ $t('SVIP会员超值权益，开卡即回本') }}</span>
        </div>
        <div class="tab-btns" :class="{ last: coinTab == 2 }">
          <div
            class="tab-btn"
            :class="{ active: coinTab == 1 }"
            @click="coinTab = 1"
          >
            会员权益
          </div>
          <div
            class="tab-btn"
            :class="{ active: coinTab == 2 }"
            @click="coinTab = 2"
          >
            专属福利
          </div>
        </div>
        <div class="container svip-welfare" v-if="coinTab == 1">
          <div class="welfare-list" v-if="privilege.length">
            <template v-for="item in privilege">
              <div class="welfare-item" :key="item.title">
                <div class="welfare-icon">
                  <img :src="item.img" alt="" />
                </div>
                <div class="welfare-right">
                  <div class="welfare-title">{{ item.title }}</div>
                  <div class="welfare-desc">{{ item.info }}</div>
                </div>
                <div
                  class="welfare-btn"
                  v-if="item.action_code && !isSdk"
                  @click="clickWelfare(item)"
                >
                  {{ item.button_text }}
                </div>
              </div>
            </template>
          </div>
          <div class="explain" v-if="text_list.illustrate">
            {{ text_list.illustrate
            }}<span class="btn" @click="gameDialogShow = true"
              >{{ $t('点击查询') }}＞</span
            >
          </div>
        </div>
        <div class="container gold-exchange" v-if="coinTab == 2">
          <div class="coin-mall">
            <div class="title">
              <div class="text">兑换平台币</div>
              <div class="more" @click="toGoldCoinExchange" v-if="!isSdk"
                >更多</div
              >
            </div>
            <div class="loading-box" v-if="exchangeListLoading">
              <van-loading />
            </div>
            <template v-else>
              <div class="platform-coin-list" v-if="exchangeList.length">
                <div
                  class="platform-coin-item"
                  v-for="(item, index) in exchangeList"
                  :key="index"
                >
                  <div class="icon">
                    <img :src="item.img" :alt="item.title" />
                  </div>
                  <div class="info">
                    <div class="name">{{ item.title }}</div>
                    <div class="desc">
                      {{ $t('消耗') }}{{ item.gold }}{{ $t('金币') }}
                    </div>
                  </div>
                  <div class="right">
                    <div class="btn" @click="getPtb(item)">
                      {{ $t('立即兑换') }}
                    </div>
                    <div class="count">
                      {{ $t('剩余') }}({{ item.inventory }}/2000)
                    </div>
                  </div>
                </div>
              </div>
              <content-empty v-else></content-empty>
            </template>
          </div>
          <div class="game-props-cont" v-if="goldCardList.length">
            <div class="title" @click="toCouponCenter">
              <div class="text">兑换游戏道具</div>
            </div>
            <div class="exchange-content">
              <div class="more-game-btn" @click="clickMoreGame"></div>
              <div class="nav-list">
                <div
                  class="nav-item"
                  :class="{
                    active: game.game_info.id == currentGame.game_info.id,
                  }"
                  v-for="game in goldCardList"
                  :key="game.id"
                  @click="clickNavGame(game)"
                >
                  <div class="game-icon">
                    <img :src="game.game_info.titlepic" alt="" />
                  </div>
                  <div
                    class="game-info"
                    v-if="game.game_info.id == currentGame.game_info.id"
                  >
                    <div class="game-title">
                      <div class="title-content text-scroll">
                        <div class="title-text">
                          {{ game.game_info.title }}
                        </div>
                        <div class="title-tag" v-if="game.game_info.subtitle">
                          {{ game.game_info.subtitle }}
                        </div>
                        <div class="title-text">
                          {{ game.game_info.title }}
                        </div>
                        <div class="title-tag" v-if="game.game_info.subtitle">
                          {{ game.game_info.subtitle }}
                        </div>
                      </div>
                    </div>
                    <div
                      class="discount-tag"
                      v-if="Number(game.game_info.pay_rebate / 10) < 10"
                    >
                      <img
                        class="discount-icon"
                        src="@/assets/images/games/discount-normal.png"
                      />
                      <div class="discount-text"
                        ><span>{{
                          Number(game.game_info.pay_rebate / 10)
                        }}</span
                        >折直充</div
                      >
                    </div>
                  </div>
                </div>
              </div>
              <div class="exchange-list">
                <template v-for="(item, index) in currentGame.card_info">
                  <div
                    class="exchange-item"
                    v-if="!(index > 3 && !isExpand)"
                    :key="item.id"
                  >
                    <div class="limit" v-if="item.redeem_num_text">
                      {{ item.redeem_num_text }}
                    </div>
                    <div class="exchange-icon">
                      <img :src="item.titlepic" alt="" />
                    </div>
                    <div class="exchange-info">
                      <marquee-text
                        class="item-title"
                        :text="item.title"
                      ></marquee-text>
                      <div class="cost">
                        消耗<span>{{ item.need_gold }}</span
                        >金币
                      </div>
                    </div>
                    <div class="exchange-btn" @click="getCard(item)">
                      立即兑换
                    </div>
                  </div>
                </template>
                <template v-if="currentGame.card_info.length > 4">
                  <div
                    class="exchange-down"
                    v-if="!isExpand"
                    @click="isExpand = !isExpand"
                  >
                    更多道具<i></i>
                  </div>
                  <div
                    class="exchange-down up"
                    v-else
                    @click="isExpand = !isExpand"
                  >
                    收起<i></i>
                  </div>
                </template>
              </div>
            </div>
          </div>
          <div class="coupon-box" v-if="specialOrderCouponList.length">
            <div class="title">
              <div class="text">专属代金券</div>
              <div class="more" @click="toCouponCenter" v-if="!isSdk">更多</div>
            </div>
            <div class="loading-box" v-if="specialOrderCouponListLoading">
              <van-loading />
            </div>
            <template v-else>
              <div class="coupon-list" v-if="specialOrderCouponList.length">
                <swiper :options="couponSwiperOption">
                  <swiper-slide
                    class="swiper-slide"
                    v-for="(couponGroup, index) in specialOrderCouponList"
                    :key="index"
                  >
                    <div class="coupon-group">
                      <div
                        class="coupon-item"
                        v-for="(coupon, couponIndex) in couponGroup"
                        :key="couponIndex"
                      >
                        <div class="coupon-left">
                          <div class="price">
                            <em>¥</em>
                            <span>{{ coupon.money }}</span>
                          </div>
                          <div class="threshold">
                            满{{ coupon.reach_money }}可用
                          </div>
                        </div>
                        <div class="info">
                          <div class="name">{{ coupon.remark }}</div>
                          <div class="date">{{ coupon.period_title }}</div>
                        </div>
                        <div class="coupon-right">
                          <div class="get-btn" @click="getCoupon(coupon)">
                            领取
                          </div>
                        </div>
                      </div>
                    </div>
                  </swiper-slide>
                </swiper>
                <div class="swiper-pagination coupon-pagination"></div>
              </div>
              <content-empty v-else></content-empty>
            </template>
          </div>
          <div class="card-box" v-if="specialOrderCardList.length">
            <div class="title" @click="toCouponCenter">
              <div class="text">特权游戏礼包</div>
            </div>
            <div class="loading-box" v-if="specialOrderCouponListLoading">
              <van-loading />
            </div>
            <template v-else>
              <div class="card-list" v-if="specialOrderCardList.length">
                <swiper :options="cardSwiperOption">
                  <swiper-slide
                    class="swiper-slide"
                    v-for="(cardGroup, index) in specialOrderCardList"
                    :key="index"
                  >
                    <div class="card-group">
                      <div
                        class="card-item"
                        v-for="(card, cardIndex) in cardGroup"
                        :key="cardIndex"
                      >
                        <div class="card-info">
                          <div class="card-left">
                            <img :src="card.game_titlepic" alt="" />
                          </div>
                          <div class="info">
                            <div class="name">
                              <marquee-text
                                class="item-title"
                                :text="`《${card.titlegame}》${card.title}`"
                                :time="6"
                              ></marquee-text>
                            </div>
                            <div class="desc">
                              <marquee-text
                                :text="card.cardbody"
                                :time="6"
                              ></marquee-text>
                            </div>
                          </div>
                          <div class="card-right">
                            <div class="get-btn" @click="getCard(card)"
                              >领取</div
                            >
                          </div>
                        </div>
                        <div class="card-count">
                          <div class="process">
                            <span :style="`width: ${card.remain}%`"></span>
                          </div>
                          <div class="number">剩余{{ card.remain }}%</div>
                        </div>
                      </div>
                    </div>
                  </swiper-slide>
                </swiper>
                <div class="swiper-pagination card-pagination"></div>
              </div>
              <content-empty v-else></content-empty>
            </template>
          </div>
        </div>
      </div>
    </rubber-band>

    <!-- 查询可用游戏 -->
    <van-dialog
      v-model="gameDialogShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="search-game-popup"
    >
      <div class="search-container">
        <div class="close-search" @click="gameDialogShow = false"></div>
        <div class="search-bar">
          <div class="input-text">
            <form @submit.prevent="searchGame">
              <input
                type="text"
                v-model.trim="inputGame"
                :placeholder="$t('输入游戏名')"
              />
            </form>
          </div>
          <div class="search-btn" @click="searchGame">{{ $t('搜索') }}</div>
        </div>
        <div class="game-list" v-if="gameList.length">
          <div class="game-item btn" v-for="item in gameList" :key="item.id">
            <div class="game-icon">
              <img :src="item.titlepic" alt="" />
            </div>
            <div class="right">
              <div class="game-name">{{ item.title }}</div>
              <div class="use-card" :class="{ can: item.use_gold_pay == 1 }">
                {{ item.use_gold_pay == 1 ? '' : $t('不')
                }}{{ $t('支持使用金币抵扣') }}
              </div>
            </div>
          </div>
        </div>
        <content-empty :tips="$t('没有相关游戏')" v-else></content-empty>
      </div>
    </van-dialog>
    <!-- 支付弹窗抽屉 -->
    <pay-type-popup
      :show.sync="payPopupShow"
      :list="payList"
      @choosePayType="choosePayType"
      :money="this.selectedMeal.amount"
      :unit="text_list.pay_symbol"
    ></pay-type-popup>
    <!-- 季卡广告弹窗 -->
    <van-popup
      v-model="isSvipAdPopupShow"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      round
      class="svip-ad-popup"
    >
      <div class="popup-container" v-if="hide_discounts">
        <div class="close" @click="closeSvipAdPopupShow"></div>
        <div class="title">{{ hide_discounts.title }}</div>
        <div class="card-container">
          <div class="discount">
            <span>{{ hide_discounts.coupon.discount }}</span
            >{{ $t('折') }}
          </div>
          <div class="card-right">
            <div class="card-title">{{ hide_discounts.coupon.title }}</div>
            <div class="card-desc">{{ hide_discounts.coupon.subtitle }}</div>
          </div>
        </div>
        <div class="tips">
          {{ hide_discounts.offer_information
          }}<span class="now"
            >{{ text_list.pay_symbol }}{{ hide_discounts.amount }}</span
          ><span class="old"
            >{{ text_list.pay_symbol }}{{ hide_discounts.show_amount }}</span
          >
        </div>
        <div class="buy-now" @click="goToSvipDiscount">
          {{ $t('立即抢购') }}
        </div>
      </div>
    </van-popup>
    <!-- 双11弹窗 -->
    <van-popup
      v-model="double11Popup"
      :lock-scroll="false"
      :close-on-click-overlay="false"
      round
      class="double-11-popup"
    >
      <div class="double-11-banner" @click="goToDouble11">
        <img :src="double11_img" alt="" />
      </div>
      <div class="popup-close" @click="double11Popup = false"></div>
    </van-popup>
    <!-- 尚未开通svip -->
    <van-dialog
      v-model="isSvipPopupShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      :closeOnClickOverlay="true"
      class="svip-popup"
    >
      <div class="popup-content" @click.stop="">
        <div class="text">
          抱歉，您当前尚未开通SVIP会员<br />
          请先开通SVIP会员！
        </div>
        <div class="btn" @click="isSvipPopupShow = false">前往开通</div>
      </div>
    </van-dialog>
    <!-- 金币不足 -->
    <van-dialog
      v-model="isCoinPopupShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      :closeOnClickOverlay="true"
      class="coin-popup"
    >
      <div class="popup-content" @click.stop="">
        <div class="text">
          抱歉，您当前金币不足<br />
          可以前往赚取更多金币再来哦～
        </div>
        <div class="btn" @click="toGoldCoin">去赚金币</div>
      </div>
    </van-dialog>
    <!-- 兑换游戏礼包更多游戏弹窗 -->
    <exchange-game-search-popup
      :show.sync="moreGamePopup"
      @onSelectSuccess="onGameSelectSuccess"
    ></exchange-game-search-popup>
    <!-- 小号选择弹窗 -->
    <yy-xh-select
      :show.sync="xhDialogShow"
      :id="Number(xhSelectGame.game_id)"
      @onSelectSuccess="getGift"
    ></yy-xh-select>
    <!-- 礼包领取成功 -->
    <cardpass-copy-popup
      :show.sync="giftCopyPopupShow"
      :info="giftSelected"
      :autoXh="1"
    ></cardpass-copy-popup>
  </div>
</template>

<script>
import exchangeGameSearchPopup from '@/components/exchange-game-search-popup';
import yyXhSelect from '@/components/yy-xh-select';
import {
  ApiGetUserSvipInfo,
  ApiSvipIndex,
  ApiCreateOrderSvip,
  ApiGetPayUrl,
  ApiGameCardSearchGame,
  ApiGetOrderStatus,
  ApiGetPaymentMethod,
} from '@/api/views/recharge.js';
import {
  ApiGetSpecialOrderCouponList,
  ApiCouponTake,
} from '@/api/views/coupon.js';
import { ApiGetSpecialOrderCardList } from '@/api/views/card.js';
import { ApiCardGet } from '@/api/views/gift.js';
import {
  ApigoldToPtbExchangeList,
  ApiGameGetGameCardList,
} from '@/api/views/gold.js';
import {
  BOX_showActivity,
  BOX_showActivityByAction,
  BOX_openInNewWindow,
  platform,
  BOX_login,
  isSdk,
  isIosSdk,
  isAndroidSdk,
  Box_postMessage,
  BOX_goToCouponCenter,
  BOX_takeGift,
  BOX_takeCoupon,
  BOX_setPayParams,
  BOX_openIosAppByAction,
} from '@/utils/box.uni.js';
import { getAdPopupData } from '@/utils/adPopup.js';
import h5Page from '@/utils/h5Page.js';
import { getQueryVariable } from '@/utils/function.js';
export default {
  name: 'Svip',
  components: {
    yyXhSelect,
    exchangeGameSearchPopup,
  },
  data() {
    return {
      isSdk,
      navbarOpacity: 0,
      bgStyle: 'transparent-white',
      selectedMeal: {},
      svipList: [],
      payList: [],
      privilege: [], // svip特权列表
      selectedPayType: 'wx', // 支付方式
      payPopupShow: false,
      userSvipInfo: {},
      overTime: '0000-00-00', // 活动到期时间
      welfareDialogShow: false, // 特权弹窗
      welfareSelectedItem: {}, // 点击的特权项
      svipZhekouTxt: '', //按钮角标
      countdown: {
        endTime: [0, 0, 0],
        nowTime: 0,
      },
      timeClock: null,
      swiperOption: {
        observer: true,
        observeParents: true,
        noSwiping: true,
        direction: 'vertical',
        slidesPerView: 1,
        autoplay: true,
        loop: true,
      },
      buySvipList: [],
      swiperShow: true,
      gameDialogShow: false,
      gameList: [],
      inputGame: '',
      isSvipAdPopupShow: false, // 当前季卡广告弹窗是否显示
      hide_discounts: null, // 季卡广告弹窗内容
      text_list: {}, // 一些带翻译的文案字段
      double11Popup: false,
      double11PopupCache: false,
      double11_img: '',

      // svip专享福利
      coinTab: 1,
      couponSwiperOption: {
        slidesPerView: 1,
        spaceBetween: 20,
        autoHeight: true,
        resizeObserver: true,
        observer: true,
        observeSlideChildren: true,
        pagination: {
          el: '.coupon-pagination',
        },
      },
      cardSwiperOption: {
        slidesPerView: 1,
        spaceBetween: 20,
        autoHeight: true,
        resizeObserver: true,
        observer: true,
        observeSlideChildren: true,
        pagination: {
          el: '.card-pagination',
        },
      },
      isCoinPopupShow: false,
      isSvipPopupShow: false,
      exchangeList: [],
      specialOrderCouponList: [],
      specialOrderCardList: [],
      exchangeListLoading: false,
      specialOrderCouponListLoading: false,
      specialOrderCardListLoading: false,
      xhDialogShow: false, //小号选择弹窗
      xhSelectGame: {},
      getWelfareType: '',
      giftCopyPopupShow: false,
      giftSelected: {},
      goldCardList: [],
      currentGame: {},
      moreGamePopup: false,
      xhGameId: 0,
      exchangeGift: {},
      giftCopyPopupShow: false,
      isExpand: false,
    };
  },
  computed: {
    countDownText() {
      let currentEndTime = this.countdown.endTime.find(item => {
        return item > this.countdown.nowTime;
      });

      if (currentEndTime) {
        return this.formatTime(currentEndTime - this.countdown.nowTime);
      }
    },
  },

  async created() {
    window.addEventListener('scroll', this.handleScroll);
  },
  mounted() {
    window.onResume = this.onResume;
    if (!isIosSdk) {
      this.getAdPopupData();
    }
  },
  async activated() {
    if (getQueryVariable('tab')) {
      this.coinTab = getQueryVariable('tab');
    }
    this.swiperShow = true;
    await this.getSvipInfo();
    if (this.double11_img && !this.double11PopupCache) {
      this.double11Popup = true;
      this.double11PopupCache = true;
    }
    await this.getSvipList();
    await this.getPayMethod();

    await this.getExchangeList();
    await this.getSpecialOrderCouponList();
    await this.getSpecialOrderCardList();
    // this.selectedPayType = this.payList[0].key;
  },
  deactivated() {
    this.swiperShow = false;
    // 清除定时器
    clearInterval(this.timeClock);
    this.timeClock = null;
  },
  beforeDestroy() {
    // 清除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);
    // 清除定时器
    clearInterval(this.timeClock);
    this.timeClock = null;
  },
  beforeRouteLeave(to, from, next) {
    if (this.hide_discounts) {
      if (
        ['ClockIn', 'GoldCoinExchange', 'SvipWelfare', 'CouponCenter'].includes(
          to.name,
        )
      ) {
        next();
        return;
      }
      this.isSvipAdPopupShow = true;
      next(false);
      return;
    }
    document.getElementsByTagName('body')[0].style.background = '';
    next(true);
  },
  methods: {
    async onResume() {
      this.getSpecialOrderCouponList();
      this.getSpecialOrderCardList();
    },
    async getAdPopupData() {
      if (platform == 'android' || platform == 'androidBox') {
        try {
          BOX.showActivityAd(4);
        } catch (error) {}
        return false;
      }
      await getAdPopupData({
        box_position: 4,
        userInfo: this.userInfo,
      });
    },
    closeSvipAdPopupShow() {
      this.hide_discounts = null;
      this.isSvipAdPopupShow = false;
    },
    toLogin() {
      BOX_login();
    },
    goToSvipDiscount() {
      this.closeSvipAdPopupShow();
      this.$nextTick(() => {
        BOX_openInNewWindow(
          { name: 'SvipDiscount' },
          { url: `${window.location.origin}/#/svip_discount` },
        );
      });
    },
    formatTime(timeStamp) {
      timeStamp = Number(timeStamp);
      let hour = this.addZero(Math.floor(timeStamp / 3600));
      let minute = this.addZero(Math.floor((timeStamp % 3600) / 60));
      let second = this.addZero((timeStamp % 3600) % 60);
      return `${hour}时-${minute}分-${second}秒`;
    },
    addZero(num) {
      num = parseInt(num);
      return num < 10 ? '0' + num : num.toString();
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
        // this.bgStyle = 'transparent';
      } else {
        this.navbarOpacity = 0;
        // this.bgStyle = 'transparent-white';
      }
    },
    goToSignIn() {
      try {
        BOX_showActivityByAction({
          action_code: 30,
          web_url: 'Welfare',
          type: 1,
        });
      } catch (e) {
        BOX_showActivity(
          { name: 'Welfare', params: { type: 1 } },
          { page: 'qd' },
        );
      }
    },
    clickWelfare(item) {
      switch (item.action_code) {
        // 签到
        case 1014:
          this.goToSignIn();
          // BOX_showActivity({ name: 'ClockIn' }, { page: 'qd' });
          break;
        // SVIP专属福利
        case 1032:
          this.coinTab = 2;
          // BOX_openInNewWindow(
          //   { name: 'SvipWelfare' },
          //   { url: `${window.location.origin}/#/svip_welfare` },
          // );
          break;
        // 金币商城
        case 33:
          BOX_goToCouponCenter(
            { name: 'CouponCenter', params: { index: 1 } },
            { position: 1 },
          );
          break;
        // 客服页面
        case 1051:
          BOX_openInNewWindow(
            { name: 'Kefu' },
            { url: `${window.location.origin}/#/kefu` },
          );
          break;
        // 账号回收
        case 1048:
          BOX_showActivity({ name: 'Recycle' }, { page: 'xhhs' });
          break;
        // 648福利
        case 20:
          BOX_showActivity(
            { name: 'Welfare648' },
            { page: 'com.a3733.cwbgamebox.ui.welfareCenter.Gift648Activity' },
          );
          break;
        case 13:
          BOX_openInNewWindow({ name: item.web_page }, { url: item.web_url });
          break;
      }
    },
    clickGiftGame(item) {
      this.toPage('GameGift', { game_id: item.id });
    },
    clickcouponGame(item) {
      this.toPage('GameCoupon', { game_id: item.id });
    },
    handleWelfareConfirm() {
      this.welfareDialogShow = false;
      if (this.welfareSelectedItem.routerName) {
        this.toPage(this.welfareSelectedItem.routerName);
      }
    },
    async getSvipList() {
      const res = await ApiSvipIndex();
      this.svipList = res.data.svipList;
      this.selectedMeal =
        this.userSvipInfo.is_first_recharge == 1
          ? this.svipList[1]
          : this.svipList[0]; // 2022年7月7日10:10:36，zyq说默认选中第二个套餐(又变了)
      // this.payList = res.data.payArr;
      this.privilege = res.data.privilege;
      this.text_list = res.data.text_list;
      this.buySvipList = res.data.buySvipList;
      this.svipZhekouTxt = res.data.svip_zhekou_txt;
      this.countdown = res.data.countdown;
      this.overTime = res.data.overTime;
      // 清除定时器
      clearInterval(this.timeClock);
      this.timeClock = null;
      this.timeClock = setInterval(() => {
        this.countdown.nowTime += 1;
      }, 1000);
    },
    async getPayMethod() {
      let res = await ApiGetPaymentMethod({
        orderType: 103,
      });
      this.payList = res.data;
    },
    clickRechargeBtn() {
      this.payPopupShow = true;
    },
    choosePayType(selectedPayType) {
      this.selectedPayType = selectedPayType.symbol;
      this.handlePay();
    },
    handlePay() {
      this.payPopupShow = false;
      const orderParams = {
        day: this.selectedMeal.day,
        amount: this.selectedMeal.amount,
        rebate_gold: this.selectedMeal.rebate_gold,
        payWay: this.selectedPayType,
        is_cycle: 0,
      };
      ApiCreateOrderSvip(orderParams).then(async orderRes => {
        // 安卓sdk下单上报
        BOX_setPayParams({
          order_id: orderRes.data.orderId,
          productname: '充值SVIP',
        });

        // 神策埋点
        this.$sensorsTrack(
          'svip_payment_submit',
          {
            order_id: orderRes.data.orderId,
            card_type: this.selectedMeal.title,
            recharge_type: this.userSvipInfo.svip_status == 1 ? '续费' : '购买',
            recharge_amount: Number(this.selectedMeal.show_amount),
            actual_recharge_amount: Number(this.selectedMeal.amount),
            recharge_source: this.$sensorsChainGet(),
          },
          'recharge_source',
        );

        await ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: 103,
          payWay: this.selectedPayType,
          packageName: '',
        }).finally(() => {
          ApiGetOrderStatus({
            order_id: orderRes.data.orderId,
            order_type: 103,
          });
        });
        await this.getSvipInfo();
        await this.getSvipList();
      });
    },
    async getSvipInfo() {
      const res = await ApiGetUserSvipInfo();
      this.userSvipInfo = res.data;

      // 双十一弹窗图
      this.double11_img = res.data.double11_img;

      // this.hide_discounts = res.data.hide_discounts;
      // 有活动就不显示季卡折扣弹窗了 2024年11月7日16:00:58
      this.hide_discounts = this.double11_img ? null : res.data.hide_discounts;

      // 通知安卓端返回的时候是否要显示折扣季卡
      Box_postMessage({
        isShowSvipCloseDialog: this.hide_discounts ? true : false,
      });
    },
    searchGame() {
      if (!this.inputGame) {
        this.$toast(this.$t('请输入游戏名'));
        return false;
      }
      this.$toast({
        type: 'loading',
        duration: 0,
        message: this.$t('拼命加载中...'),
      });
      ApiGameCardSearchGame({
        keyword: this.inputGame,
        listRows: 10,
      })
        .then(res => {
          this.gameList = res.data.list;
        })
        .finally(() => {
          this.$toast.clear();
        });
    },
    goToDouble11() {
      BOX_openInNewWindow(
        {
          h5_url: `https://${h5Page.env}activity.3733.com/#/double_eleven_activity`,
        },
        {
          url: `https://${h5Page.env}activity.3733.com/#/double_eleven_activity`,
        },
      );
      this.double11Popup = false;
    },

    // svip专属福利
    async getExchangeList() {
      this.exchangeListLoading = true;
      try {
        const res = await ApigoldToPtbExchangeList({
          is_reach: 1,
        });
        this.exchangeList = res.data.list;
        this.goldCardList = res.data.card_list.gold_card_list;
        this.currentGame = this.goldCardList[0];
      } finally {
        this.exchangeListLoading = false;
      }
    },
    async getSpecialOrderCouponList() {
      this.specialOrderCouponListLoading = true;
      try {
        const res = await ApiGetSpecialOrderCouponList({
          is_list: 1,
          type: 1,
          is_648: 0,
        });
        this.specialOrderCouponList = [];
        let list = [...res.data.coupon_list];
        if (list.length) {
          if (list.length > 3) {
            let arr = [];
            list.forEach((item, index) => {
              arr.push(item);
              if (index == list.length - 1) {
                this.specialOrderCouponList.push(arr);
                return false;
              }
              if ((index + 1) % 3 == 0) {
                this.specialOrderCouponList.push(arr);
                arr = [];
              }
            });
          } else {
            this.specialOrderCouponList.push(list);
          }
        }
      } finally {
        this.specialOrderCouponListLoading = false;
      }
    },
    async getSpecialOrderCardList() {
      this.specialOrderCardListLoading = true;
      try {
        const res = await ApiGetSpecialOrderCardList({
          is_list: 1,
          page: 1,
          listRows: 9,
        });
        this.specialOrderCardList = [];
        let list = [...res.data.list];
        if (list.length) {
          if (list.length > 3) {
            let arr = [];
            list.forEach((item, index) => {
              arr.push(item);
              if (index == list.length - 1) {
                this.specialOrderCardList.push(arr);
                return false;
              }
              if ((index + 1) % 3 == 0) {
                this.specialOrderCardList.push(arr);
                arr = [];
              }
            });
          } else {
            this.specialOrderCardList.push(list);
          }
        }
      } finally {
        this.specialOrderCardListLoading = false;
      }
    },
    toGoldCoinExchange() {
      try {
        BOX_showActivityByAction({
          action_code: 30,
          web_url: 'WelfareGoldCoinExchange',
        });
      } catch (e) {
        BOX_openInNewWindow(
          { name: 'GoldCoinExchange' },
          { url: `${window.location.origin}/#/gold_coin_exchange` },
        );
      }
    },
    toGoldCoin() {
      this.isCoinPopupShow = false;
      this.$nextTick(() => {
        try {
          BOX_showActivityByAction({
            action_code: 30,
            web_url: 'Welfare',
          });
        } catch (e) {
          BOX_openInNewWindow(
            { name: 'GoldCoin' },
            { url: `${window.location.origin}/#/gold_coin_task` },
          );
        }
      });
    },
    toCouponCenter() {
      BOX_goToCouponCenter(
        { name: 'CouponCenter', params: { index: 1 } },
        { position: 1 },
      );
    },
    async getPtb(item) {
      if (this.userSvipInfo.svip_status != 1) {
        this.isSvipPopupShow = true;
        return false;
      }
      if (isAndroidSdk) {
        try {
          BOX_showActivityByAction({
            action_code: 13,
            web_url: `${window.location.origin}/?tab=2#/svip`,
            isNeedLogin: true,
          });
        } catch (e) {
          this.$toast(
            '因版本受限，请耐心等待该游戏SDK更新目前请手动打开3733游戏盒-我的-SVIP-金币兑换专享-前往兑换-SVIP专属福利页面',
          );
        }
        return false;
      }
      if (isIosSdk) {
        let res = BOX_openIosAppByAction({
          action_code: 5012,
          web_page: 'SVIP',
          web_url: `${window.location.origin}/?tab=2#/svip`,
        });
        if (!res) {
          this.$toast(
            '因版本受限，请耐心等待该游戏SDK更新目前请手动打开3733游戏盒-我的-SVIP-金币兑换专享-前往兑换-SVIP专属福利页面',
          );
        }
        return false;
      }
      if (!item.exchange_status) {
        this.isCoinPopupShow = true;
        return false;
      }
      this.toPage('ExchangePtb', { id: item.id });
      return false;
    },
    async getCoupon(item) {
      if (this.userSvipInfo.svip_status != 1) {
        this.isSvipPopupShow = true;
        return false;
      }
      if (isAndroidSdk) {
        try {
          BOX_showActivityByAction({
            action_code: 13,
            web_url: `${window.location.origin}/?tab=2#/svip`,
            isNeedLogin: true,
          });
        } catch (e) {
          this.$toast(
            '因版本受限，请耐心等待该游戏SDK更新目前请手动打开3733游戏盒-我的-SVIP-金币兑换专享-前往兑换-SVIP专属福利页面',
          );
        }
        return false;
      }
      if (isIosSdk) {
        let res = BOX_openIosAppByAction({
          action_code: 5012,
          web_page: 'SVIP',
          web_url: `${window.location.origin}/?tab=2#/svip`,
        });
        if (!res) {
          this.$toast(
            '因版本受限，请耐心等待该游戏SDK更新目前请手动打开3733游戏盒-我的-SVIP-金币兑换专享-前往兑换-SVIP专属福利页面',
          );
        }
        return false;
      }
      if (platform == 'android') {
        BOX_takeCoupon(item);
        return;
      }
      this.getWelfareType = 'coupon';
      this.xhDialogShow = true;
      this.xhSelectGame = item;
    },
    async getCard(item) {
      if (this.userSvipInfo.svip_status != 1) {
        this.isSvipPopupShow = true;
        return false;
      }
      if (isAndroidSdk) {
        try {
          BOX_showActivityByAction({
            action_code: 13,
            web_url: `${window.location.origin}/?tab=2#/svip`,
            isNeedLogin: true,
          });
        } catch (e) {
          this.$toast(
            '因版本受限，请耐心等待该游戏SDK更新目前请手动打开3733游戏盒-我的-SVIP-金币兑换专享-前往兑换-SVIP专属福利页面',
          );
        }
        return false;
      }
      if (isIosSdk) {
        let res = BOX_openIosAppByAction({
          action_code: 5012,
          web_page: 'SVIP',
          web_url: `${window.location.origin}/?tab=2#/svip`,
        });
        if (!res) {
          this.$toast(
            '因版本受限，请耐心等待该游戏SDK更新目前请手动打开3733游戏盒-我的-SVIP-金币兑换专享-前往兑换-SVIP专属福利页面',
          );
        }
        return false;
      }
      if (item.get_status === false) {
        this.isCoinPopupShow = true;
        return false;
      }
      if (platform == 'android') {
        BOX_takeGift(item);
        return;
      }
      this.getWelfareType = 'card';
      this.xhDialogShow = true;
      this.xhSelectGame = item;
    },
    async getGift(xhInfo) {
      this.xhDialogShow = false;
      if (this.getWelfareType == 'coupon') {
        try {
          let res = await ApiCouponTake({
            couponId: this.xhSelectGame.id,
            xhId: xhInfo.xhId,
          });

          // 神策埋点
          this.$sensorsTrack('get_voucher', {
            voucher_id: `${this.xhSelectGame.id}`,
            voucher_name: `${this.xhSelectGame.title}`,
            voucher_amount: `${this.xhSelectGame.money}`,
          });

          this.getSpecialOrderCouponList();
        } catch {}
      } else if (this.getWelfareType == 'card') {
        try {
          let res = await ApiCardGet({
            cardId: this.xhSelectGame.id,
            xhId: xhInfo.xhId,
          });
          this.$nextTick(() => {
            this.giftSelected = res.data;
            this.giftCopyPopupShow = true;
          });

          // 神策埋点
          this.$sensorsTrack('game_rewards_claim', {
            game_id: `${this.xhSelectGame.game_id}`,
            adv_id: '暂无',
            game_name: this.xhSelectGame.titlegame,
            game_type: `${this.xhSelectGame.classid}`,
            game_size: '暂无',
            reward_type: this.xhSelectGame.title, // 传礼包名称
            data_source: this.$sensorsChainGet(),
          });

          this.getSpecialOrderCardList();
        } catch {}
      }
    },
    clickMoreGame() {
      this.moreGamePopup = true;
    },
    async onGameSelectSuccess(info) {
      console.log('aaa');
      this.isExpand = false;
      let scrollEl = document.querySelector('.nav-list');
      let findOne = this.goldCardList.find(
        item => item.game_info.id === info.id,
      );
      if (!findOne) {
        this.goldCardList.unshift({
          game_info: info,
          card_info: [],
        });
        this.currentGame = this.goldCardList[0];
        this.$nextTick(() => {
          scrollEl.scrollTo({
            left: 0,
            behavior: 'smooth',
          });
        });
      } else {
        this.currentGame = findOne;
        this.$nextTick(() => {
          let index = this.goldCardList.findIndex(
            item => item.game_info.id === info.id,
          );
          scrollEl.scrollTo({
            left: index * 46,
            behavior: 'smooth',
          });
        });
      }

      await this.updatePropList();
      this.moreGamePopup = false;
    },
    async updatePropList() {
      const res = await ApiGameGetGameCardList({
        id: this.currentGame.game_info.id,
      });
      this.currentGame.card_info = res.data.list;
      this.goldCardList.find(
        item => item.game_info.id === this.currentGame.game_info.id,
      ).card_info = res.data.list;
    },
    clickNavGame(game) {
      this.isExpand = false;
      this.currentGame = game;
    },
  },
};
</script>

<style lang="less" scoped>
.svip-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #1e1916 0%, #161207 100%);
  .nav-bar {
    /deep/ .transparent-white {
      .nav-title {
        color: #f4dfc6;
        font-size: 17 * @rem;
        font-weight: normal;
      }
      .back {
        background-image: url(~@/assets/images/recharge/svip/back-btn.png);
      }
    }
  }
  .sign-in-btn {
    display: flex;
    align-items: center;
    font-size: 13 * @rem;
    line-height: 15 * @rem;
    font-weight: 500;
    color: #fff;
    &.black {
      color: #000000;
    }
  }
  .top-bg {
    width: 100%;
    height: 238 * @rem;
    background: url(~@/assets/images/recharge/svip/svip-page-top-bg.png)
      no-repeat top right;
    background-size: 100% auto;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;

    .diamond {
      width: 90 * @rem;
      height: 70 * @rem;
      background: url(~@/assets/images/recharge/svip/diamond.png) no-repeat;
      background-size: 90 * @rem 70 * @rem;
      position: absolute;
      top: 100 * @rem;
      right: 29 * @rem;
      z-index: 2;
    }
    .diamond-bottom {
      width: 125 * @rem;
      height: 95 * @rem;
      background: url(~@/assets/images/recharge/svip/diamond-bottom.png)
        no-repeat;
      background-size: 125 * @rem 95 * @rem;
      position: absolute;
      top: 137 * @rem;
      right: 10 * @rem;
      z-index: 1;
    }

    &::after {
      content: '';
      display: block;
      width: 100%;
      height: 78 * @rem;
      background: linear-gradient(
        180deg,
        rgba(22, 18, 7, 0) 0%,
        rgba(26, 21, 13, 0.75) 49%,
        #1a160e 100%
      );
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 2;
    }
  }
  .top-bar {
    width: 100%;
    // height: 228 * @rem;
    // height: calc(228 * @rem + @safeAreaTop);
    // height: calc(228 * @rem + @safeAreaTopEnv);

    padding-top: 44 * @rem;
    padding-top: calc(44 * @rem + @safeAreaTop);
    padding-top: calc(44 * @rem + @safeAreaTopEnv);
    box-sizing: border-box;
    padding-bottom: 10 * @rem;
    position: relative;
    z-index: 2;
    .user-bar {
      box-sizing: border-box;
      overflow: hidden;
      width: 343 * @rem;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-top: 21 * @rem;
      .no-svip {
        display: flex;
        align-items: flex-end;
        margin-top: 22 * @rem;
        margin-left: 15 * @rem;
        height: 40 * @rem;
        .no-svip-icon {
          width: 74 * @rem;
          height: 40 * @rem;
          .image-bg('~@/assets/images/recharge/svip-text-new.png');
          background-position: center center;
        }
        .no-svip-text {
          font-size: 14 * @rem;
          color: #8b83fb;
          line-height: 18 * @rem;
          font-weight: bold;
          margin-left: 12 * @rem;
        }
      }
      .no-login {
        display: flex;
        align-items: center;

        .avatar {
          width: 60 * @rem;
          height: 60 * @rem;
          border: 1px solid rgba(249, 220, 165, 0.55);
        }
        .no-login-text {
          flex: 1;
          min-width: 0;
          font-size: 16 * @rem;
          line-height: 20 * @rem;
          font-weight: bold;
          margin-left: 6 * @rem;
          -webkit-background-clip: text;
          color: transparent;
          background-image: linear-gradient(0deg, #efbc7b 0%, #f9dda6 100%);
        }
      }
      .user-info {
        display: flex;
        align-items: center;
        .avatar {
          box-sizing: border-box;
          width: 54 * @rem;
          height: 54 * @rem;
          overflow: hidden;
          border-radius: 50%;
          border: 1px solid rgba(249, 220, 165, 0.55);
        }
        .info-right {
          margin-left: 11 * @rem;
          flex: 1;
          min-width: 0;
          .nickname {
            display: flex;
            align-items: center;

            .text {
              flex-shrink: 1;
              font-size: 16 * @rem;
              -webkit-background-clip: text;
              color: transparent;
              background-image: linear-gradient(0deg, #efbc7b 0%, #f9dda6 100%);
              line-height: 20 * @rem;
              font-weight: bold;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .svip-icon {
              width: 33 * @rem;
              height: 12 * @rem;
              background: url(~@/assets/images/recharge/svip/no-svip-id-icon.png)
                no-repeat;
              background-size: 33 * @rem 12 * @rem;
              margin-left: 3 * @rem;
              margin-top: 2 * @rem;

              &.had {
                width: 29 * @rem;
                height: 12 * @rem;
                background-image: url(~@/assets/images/recharge/svip/svip-id-icon.png);
                background-size: 29 * @rem 12 * @rem;
              }
            }
          }
          .end-date {
            font-size: 12 * @rem;
            color: #f9dca4;
            line-height: 17 * @rem;
            margin-top: 8 * @rem;
          }
        }
      }
      .svip-intro {
        height: 17 * @rem;
        display: flex;
        align-items: center;
        font-size: 12 * @rem;
        line-height: 17 * @rem;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.62);
        margin-top: 32 * @rem;
      }
    }
  }
  .main {
    position: relative;
    z-index: 2;
    padding-bottom: 103 * @rem;
    border-radius: 16 * @rem 16 * @rem 0 0;
    margin-top: 5 * @rem;
    .container {
      position: relative;
      .container-title {
        width: 100%;
        height: 79 * @rem;
        margin: 0 auto;
        font-size: 16 * @rem;
        line-height: 19 * @rem;
        font-weight: bold;
        color: #f9dda6;
        padding: 20 * @rem 20 * @rem 0;
        box-sizing: border-box;
        background: url(~@/assets/images/recharge/svip/select-title-bg.png)
          no-repeat;
        background-size: 100% auto;
      }

      .section-title {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18 * @rem;
        color: #fcdebb;
        font-weight: 600;
        line-height: 25 * @rem;
        margin-top: 20 * @rem;
        span {
          margin-right: 8 * @rem;
          display: block;
          width: 18 * @rem;
          height: 18 * @rem;
          background-size: 18 * @rem 18 * @rem;
          background-position: center center;
          background-repeat: no-repeat;
          &.gift-icon {
            background-image: url(~@/assets/images/recharge/gift-icon.png);
          }
          &.coupon-icon {
            background-image: url(~@/assets/images/recharge/gold-coin-coupon.png);
          }
        }
      }
    }
    .choose-box {
      background-color: #262017;
      border-radius: 20 * @rem;
      padding-bottom: 20 * @rem;
      overflow: hidden;
      .svip-list {
        display: flex;
        padding-left: 17 * @rem;
        overflow-x: auto;
        margin-top: -35 * @rem;
        padding-top: 16 * @rem;
        &::-webkit-scrollbar {
          display: none;
        }
        &::after {
          content: '';
          width: 17 * @rem;
          height: 1px;
          flex-shrink: 0;
        }
        .svip-item {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          box-sizing: border-box;
          flex-shrink: 0;
          width: 147 * @rem;
          height: 126 * @rem;
          background: #272727;
          border-radius: 10 * @rem;
          border: 1px solid transparent;
          margin-left: 15 * @rem;
          position: relative;

          &:first-of-type {
            margin-left: 0;
          }

          &.on {
            border-color: #ffe4bf;
            background: linear-gradient(160deg, #fef7db 0%, #ffefb9 100%);

            .right-tip {
              display: flex;
            }
            .month {
              color: #7f3e00;
            }
            .money {
              .now {
                color: #7f3e00;
                span {
                  color: #7f3e00;
                }
              }
              .old {
                color: #da8646;
              }
            }
            .tip {
              color: rgba(69, 41, 139, 0.5);
              span {
                color: #ff6649;
                font-weight: bold;
              }
            }
            .bottom-desc {
              background: linear-gradient(90deg, #ffe7c5 0%, #ffc983 100%);
              color: #7f3e00;
            }
          }
          .tips {
            position: absolute;
            top: -10 * @rem;
            left: -1 * @rem;
            z-index: 1;
            padding: 0 7 * @rem 0 18 * @rem;
            border-radius: 0 6 * @rem 6 * @rem 0;
            height: 20 * @rem;
            line-height: 20 * @rem;
            background: linear-gradient(76deg, #ffe5c1 0%, #ffd8a9 94%);
            color: #af3b04;
            text-align: center;
            font-size: 10 * @rem;
            font-weight: bold;

            &::before {
              content: '';
              display: block;
              width: 20 * @rem;
              height: 20 * @rem;
              background: url(~@/assets/images/recharge/svip/svip-tips-left-icon.png)
                no-repeat;
              background-size: 20 * @rem 20 * @rem;
              position: absolute;
              top: -3 * @rem;
              left: -5 * @rem;
            }
          }
          .content-top {
            position: relative;
            overflow: hidden;
            flex: 1;
            min-height: 0;
          }
          .right-tip {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 58 * @rem;
            height: 58 * @rem;
            padding: 10 * @rem 5 * @rem 0;
            box-sizing: border-box;
            background: rgba(255, 255, 255, 0.84);
            border-radius: 50%;
            opacity: 0.71;
            font-size: 10 * @rem;
            color: #da8646;
            line-height: 12 * @rem;
            text-align: center;
            position: absolute;
            top: -6 * @rem;
            right: -5 * @rem;
            transform: rotateZ(24.76deg);
            display: none;
          }
          .month {
            font-size: 14 * @rem;
            color: rgba(255, 255, 255, 0.8);
            line-height: 20 * @rem;
            font-weight: bold;
            margin-top: 24 * @rem;
            padding: 0 12 * @rem;
          }
          .money {
            display: flex;
            align-items: center;
            padding: 0 12 * @rem;
            margin-top: 6 * @rem;
            .now {
              display: flex;
              align-items: center;
              font-size: 28 * @rem;
              color: rgba(255, 255, 255, 0.8);
              font-weight: bold;
              line-height: 39 * @rem;

              span {
                font-weight: bold;
                line-height: 16 * @rem;
                font-size: 14 * @rem;
                margin-right: 2 * @rem;
                margin-top: 11 * @rem;
              }
            }
            .old {
              font-size: 12 * @rem;
              color: #93999f;
              text-decoration: line-through;
              height: 14 * @rem;
              line-height: 14 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 4 * @rem;
              margin-top: 12 * @rem;
            }
          }
          .bottom-desc {
            width: calc(100% + 2 * @rem);
            height: 28 * @rem;
            line-height: 1;
            background-color: #353532;
            display: flex;
            align-items: center;
            font-size: 12 * @rem;
            color: #93999f;
            margin-left: -1 * @rem;
            padding: 0 12 * @rem;
            box-sizing: border-box;
            border-radius: 0 0 10 * @rem 10 * @rem;
            margin-bottom: -1 * @rem;

            // 隐藏但是需要占位
            &.hidden {
              opacity: 0;
            }
          }
        }
      }
      .buy-fixed {
        width: 100%;
        max-width: 450px;
        position: fixed;
        bottom: 0;
        left: 50%;
        right: 0;
        transform: translateX(-50%);
        background: #161308;
        padding: 14 * @rem 18 * @rem 24 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        box-sizing: border-box;
        box-shadow: 0 * @rem 6 * @rem 30 * @rem 0 * @rem rgba(0, 0, 0, 0.06);
      }
      .recharge-btn {
        width: 339 * @rem;
        height: 49 * @rem;
        background: linear-gradient(
          92deg,
          #ffefb9 0%,
          #fef7db 37%,
          #ffe29f 87%,
          #fae7c3 99%
        );
        border-radius: 10 * @rem;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        @keyframes smallToBig {
          0% {
            transform: scale(1);
          }
          50% {
            transform: scale(0.85);
          }
          100% {
            transform: scale(1);
          }
        }
        .recharge-text {
          font-family: PingFang SC, PingFang SC;
          font-size: 20 * @rem;
          color: #712b03;
          font-weight: 600;
          line-height: 25 * @rem;
        }
        .time-out {
          font-size: 10 * @rem;
          color: #ffffff;
          line-height: 12 * @rem;
          margin-top: 4 * @rem;
          span {
            color: #ffd600;
            margin-left: 5 * @rem;
          }
        }
        .discount {
          height: 27 * @rem;
          line-height: 27 * @rem;
          font-weight: 600;
          font-size: 14 * @rem;
          color: #ffffff;
          text-shadow: 0 * @rem 1 * @rem 0 * @rem rgba(211, 81, 0, 0.91);
          text-align: center;
          padding: 0 7 * @rem;
          background: linear-gradient(128deg, #ffa55c 0%, #ff4281 100%);
          border: 2 * @rem solid rgba(255, 255, 255, 0.84);
          position: absolute;
          top: -15 * @rem;
          right: 12 * @rem;
          border-radius: 14 * @rem 14 * @rem 14 * @rem 0;
          animation: smallToBig 1.5s infinite;
        }
      }
      .buy-history {
        position: relative;
        width: 351 * @rem;
        margin: 20 * @rem auto 0;
        .laba {
          display: block;
          width: 14 * @rem;
          height: 13 * @rem;
          background: url(~@/assets/images/recharge/svip/laba-icon.png)
            no-repeat;
          background-size: 14 * @rem 13 * @rem;
          position: absolute;
          top: 50%;
          left: 10 * @rem;
          transform: translateY(-50%);
          z-index: 2;
        }

        .history-list {
          width: 100%;
          height: 39 * @rem;
          position: relative;
          background: #332b23;
          border-radius: 8 * @rem;
          padding: 0 10 * @rem 0 30 * @rem;
          box-sizing: border-box;

          .history-item {
            display: flex;
            align-items: center;
            width: 100%;
            height: 39 * @rem;
            box-sizing: border-box;

            .nickname {
              font-size: 11 * @rem;
              color: rgba(255, 255, 255, 0.6);
              flex: 1;
              min-width: 0;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
            .history-desc {
              color: rgba(255, 255, 255, 0.6);
              font-size: 12 * @rem;
              text-align: right;
              span {
                color: #ffc293;
              }
            }
          }
        }
      }
    }
    .tab-btns {
      display: flex;
      align-items: flex-end;
      width: 100%;
      height: 52 * @rem;
      background: url(~@/assets/images/recharge/svip/first-active-tab1.png)
        no-repeat;
      background-size: 100% auto;
      margin-top: 18 * @rem;

      &.last {
        background-image: url(~@/assets/images/recharge/svip/last-active-tab1.png);
      }

      .tab-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        height: 43 * @rem;
        font-weight: 400;
        font-size: 14 * @rem;
        color: rgba(255, 255, 255, 0.6);
        text-align: center;
        box-sizing: border-box;

        &.active {
          height: 52 * @rem;
          font-weight: 600;
          font-size: 18 * @rem;
          color: #fff1d3;
          position: relative;
        }
      }
    }
    .welfare-section-title {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16 * @rem;
      color: #f9dda6;
      font-weight: bold;
      height: 19 * @rem;
      line-height: 19 * @rem;
      text-align: center;
      margin: 30 * @rem auto 0;

      &::before {
        content: '';
        display: block;
        width: 25 * @rem;
        height: 12 * @rem;
        margin-right: 6 * @rem;
        margin-top: 2 * @rem;
        background: url(~@/assets/images/recharge/svip/svip-title-left-wings.png)
          no-repeat;
        background-size: 25 * @rem 12 * @rem;
      }
      &::after {
        content: '';
        display: block;
        width: 25 * @rem;
        height: 12 * @rem;
        margin-left: 6 * @rem;
        margin-top: 2 * @rem;
        background: url(~@/assets/images/recharge/svip/svip-title-right-wings.png)
          no-repeat;
        background-size: 25 * @rem 12 * @rem;
      }
    }
    .svip-welfare {
      background-color: #262017;
      border-radius: 0 0 20 * @rem 20 * @rem;
      padding: 15 * @rem 12 * @rem 0;
      overflow: hidden;

      .welfare-list {
        .welfare-item {
          display: flex;
          align-items: center;
          background-color: #322c26;
          border-radius: 12 * @rem;
          margin-bottom: 10 * @rem;
          padding: 12 * @rem 14 * @rem;
          .welfare-icon {
            width: 40 * @rem;
            height: 40 * @rem;
          }
          .welfare-right {
            flex: 1;
            min-width: 0;
            display: flex;
            justify-content: center;
            flex-direction: column;
            margin-left: 10 * @rem;
            .welfare-title {
              font-size: 15 * @rem;
              color: #ffffff;
              font-weight: bold;
              line-height: 15 * @rem;
            }
            .welfare-desc {
              font-size: 13 * @rem;
              color: rgba(255, 255, 255, 0.5);
              margin-top: 9 * @rem;
              line-height: 16 * @rem;
            }
          }
          .welfare-btn {
            width: 68 * @rem;
            height: 29 * @rem;
            border-radius: 16 * @rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 13 * @rem;
            line-height: 1;
            font-family: PingFang SC, PingFang SC;
            color: #7f3e00;
            font-weight: 500;
            background: linear-gradient(90deg, #ffe7c5 0%, #ffc983 100%);
            margin-left: 10 * @rem;
          }
        }
      }
      .explain {
        color: rgba(255, 255, 255, 0.65);
        font-size: 13 * @rem;
        margin: 13 * @rem 0 12 * @rem;
        line-height: 17 * @rem;
        span {
          color: #ffc293;
        }
      }
    }
    .gold-exchange {
      background-color: #262017;
      border-radius: 0 0 20 * @rem 20 * @rem;
      padding: 0 12 * @rem 12 * @rem;
      overflow: hidden;

      .title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12 * @rem 0;
        margin-top: 4 * @rem;

        .text {
          display: flex;
          align-items: center;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 16 * @rem;
          color: #fff1d3;
          line-height: 20 * @rem;

          &::before {
            content: '';
            display: block;
            width: 16 * @rem;
            height: 16 * @rem;
            background: url(~@/assets/images/recharge/svip/svip-title-icon.png)
              no-repeat;
            background-size: 16 * @rem 16 * @rem;
            margin-right: 2 * @rem;
          }
        }
      }

      .more {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 20 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #93999f;
        line-height: 20 * @rem;
        text-align: center;

        &::after {
          content: '';
          display: block;
          width: 9 * @rem;
          height: 9 * @rem;
          margin-left: 2 * @rem;
          background: url(~@/assets/images/recharge/svip/more-icon.png)
            no-repeat;
          background-size: contain;
        }
      }
    }
    .coin-mall {
      .platform-coin-item {
        display: flex;
        align-items: center;
        width: 100%;
        height: 75 * @rem;
        border-radius: 12 * @rem;
        padding: 0 10 * @rem;
        box-sizing: border-box;
        background: #322c26;
        margin-bottom: 10 * @rem;

        &:last-of-type {
          margin-bottom: 0;
        }
        .icon {
          display: block;
          width: 50 * @rem;
          height: 50 * @rem;
          margin-right: 8 * @rem;
        }

        .info {
          flex: 1;
          min-width: 0;

          .name {
            width: 100%;
            height: 18 * @rem;
            line-height: 18 * @rem;
            font-weight: 400;
            font-size: 15 * @rem;
            color: #ffffff;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .desc {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 11 * @rem;
            color: rgba(255, 255, 255, 0.5);
            line-height: 13 * @rem;
            text-align: center;
            margin-top: 9 * @rem;
          }
        }

        .right {
          flex-shrink: 0;
          margin-left: 10 * @rem;
          width: 90 * @rem;
          display: flex;
          flex-wrap: wrap;
          justify-content: flex-end;

          .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 64 * @rem;
            height: 29 * @rem;
            line-height: 29 * @rem;
            text-align: center;
            font-weight: 500;
            font-size: 12 * @rem;
            color: #7f3e00;
            border-radius: 16 * @rem;
            background: linear-gradient(90deg, #ffe7c5 0%, #ffc983 100%);
          }

          .count {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 15 * @rem;
            line-height: 15 * @rem;
            font-weight: 400;
            font-size: 11 * @rem;
            color: #93999f;
            text-align: center;
            margin-top: 5 * @rem;
          }
        }
      }
    }
    .game-props-cont {
      margin-top: 10 * @rem;

      .exchange-content {
        position: relative;
        .more-game-btn {
          width: 44 * @rem;
          height: 66 * @rem;
          background: url(~@/assets/images/recharge/svip/more-game-btn2.png)
            center center no-repeat;
          background-size: 44 * @rem 66 * @rem;
          position: absolute;
          right: 0;
          top: 0;
          z-index: 10;
        }
        .nav-list {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          overflow-x: auto;
          position: relative;
          &::-webkit-scrollbar {
            display: none;
          }
          .nav-item {
            box-sizing: border-box;
            display: flex;
            align-items: center;
            height: 68 * @rem;
            margin-right: 12 * @rem;
            &:last-of-type {
              margin-right: 40 * @rem;
            }
            .game-icon {
              width: 55 * @rem;
              height: 55 * @rem;
              border-radius: 10 * @rem;
              overflow: hidden;
            }
            .game-info {
              margin-left: 9 * @rem;
              min-width: 0;
              flex: 1;
              .game-title {
                display: flex;
                align-items: center;
                overflow: hidden;
                .title-content {
                  display: flex;
                  align-items: center;
                }
                .title-text {
                  flex-shrink: 0;
                  font-size: 14 * @rem;
                  line-height: 18 * @rem;
                  color: #ffffff;
                  font-weight: 600;
                  overflow: hidden;
                  white-space: nowrap;
                }
                .title-tag {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  box-sizing: border-box;
                  border-radius: 4 * @rem;
                  height: 16 * @rem;
                  margin-left: 4 * @rem;
                  font-size: 10 * @rem;
                  color: #ffffff;
                  white-space: nowrap;
                  padding: 0 4 * @rem;
                  border: 1px solid rgba(227, 229, 232, 0.6);
                  margin-right: 30 * @rem;
                }
              }
              .discount-tag {
                display: flex;
                align-items: center;
                width: fit-content;
                flex-shrink: 0;
                margin-top: 7 * @rem;

                .discount-icon {
                  width: 27 * @rem;
                  height: 18 * @rem;
                  position: relative;
                  z-index: 1;
                }
                .discount-text {
                  display: flex;
                  align-items: center;
                  height: 18 * @rem;
                  padding-right: 3 * @rem;
                  flex: 1;
                  min-width: 0;
                  font-size: 11 * @rem;
                  color: #ff6649;
                  font-weight: 500;
                  white-space: nowrap;
                  background-color: #fff5ed;
                  border-radius: 0 2 * @rem 2 * @rem 0;
                  margin-left: -8 * @rem;
                  padding-left: 8 * @rem;
                }
              }
            }
            &.active {
              flex-shrink: 0;
              width: 192 * @rem;
              height: 66 * @rem;
              padding: 0 8 * @rem 0 10 * @rem;
              border-radius: 12 * @rem;
              position: relative;
              z-index: 2;
              &::before {
                content: '';
                background: linear-gradient(
                  180deg,
                  rgba(71, 62, 52, 1),
                  rgba(71, 62, 52, 0.23)
                );
                width: 192 * @rem;
                height: 66 * @rem;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                border-radius: 12 * @rem;
                z-index: -2;
              }
              &::after {
                content: '';
                background: linear-gradient(
                  180deg,
                  #4b4035 0%,
                  rgba(75, 64, 53, 0) 100%
                );
                width: 190 * @rem;
                height: 64 * @rem;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                border-radius: 12 * @rem;
                z-index: -1;
              }
            }
          }
        }
        .exchange-list {
          padding-top: 12 * @rem;

          .exchange-item {
            box-sizing: border-box;
            width: 100%;
            height: 80 * @rem;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            background: #322c26;
            border-radius: 12 * @rem;
            padding: 0 10 * @rem 0 12 * @rem;
            margin-bottom: 10px;
            .limit {
              position: absolute;
              right: 0;
              top: 0;
              height: 22 * @rem;
              background-color: #4f4842;
              padding: 0 12 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 10 * @rem;
              color: #fff;
              border-radius: 0 12 * @rem 0 12 * @rem;
            }
            .exchange-icon {
              width: 45 * @rem;
              height: 45 * @rem;
              border-radius: 11 * @rem;
              background: #ffffff;
              display: flex;
              justify-content: center;
              align-items: center;
            }
            .exchange-info {
              flex: 1;
              min-width: 0;
              margin-left: 9 * @rem;
              .item-title {
                font-family: PingFang SC, PingFang SC;
                font-size: 14 * @rem;
                color: #ffffff;
                font-weight: 600;
                line-height: 18 * @rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              .cost {
                font-family: PingFang SC, PingFang SC;
                display: flex;
                align-items: center;
                font-size: 12 * @rem;
                color: rgba(255, 255, 255, 0.6);
                line-height: 15 * @rem;
                margin-top: 8 * @rem;
                span {
                  color: #ffc293;
                }
              }
            }
            .exchange-btn {
              width: 64 * @rem;
              height: 29 * @rem;
              color: #7f3e00;
              background: linear-gradient(90deg, #ffe7c5 0%, #ffc983 100%);
              font-size: 12 * @rem;
              font-weight: 500;
              border-radius: 16 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-top: 9 * @rem;
            }
          }
          .exchange-down {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12 * @rem;
            color: #93999f;
            padding-top: 4 * @rem;
            i {
              display: block;
              width: 9 * @rem;
              height: 9 * @rem;
              margin-left: 2 * @rem;
              background: url(~@/assets/images/recharge/svip/down-icon1.png)
                center center no-repeat;
              background-size: 9 * @rem 9 * @rem;
            }
            &.up {
              i {
                transform: rotate(180deg);
              }
            }
          }
        }
      }
    }
    .coupon-box {
      box-sizing: border-box;
      position: relative;
      margin-top: 10 * @rem;

      .coupon-list {
        width: 100%;
        position: relative;
      }
      .coupon-group {
        margin-bottom: 25 * @rem;
      }
      .coupon-item {
        display: flex;
        align-items: center;
        width: 100%;
        height: 74 * @rem;
        border-radius: 12 * @rem;
        padding-right: 10 * @rem;
        margin-bottom: 10 * @rem;
        box-sizing: border-box;
        background-color: #322c26;

        .coupon-left {
          display: block;
          width: 88 * @rem;

          .price {
            display: flex;
            align-items: flex-end;
            justify-content: center;
            height: 26 * @rem;
            text-align: center;

            em {
              font-size: 12 * @rem;
              line-height: 16 * @rem;
              color: #ffc293;
            }

            span {
              font-size: 26 * @rem;
              line-height: 26 * @rem;
              font-weight: bold;
              color: #ffc293;
            }
          }

          .threshold {
            height: 14 * @rem;
            font-weight: 400;
            font-family: PingFang SC, PingFang SC;
            font-size: 11 * @rem;
            color: rgba(255, 255, 255, 0.58);
            line-height: 14 * @rem;
            text-align: center;
            margin-top: 9 * @rem;
            white-space: nowrap;
          }
        }

        .info {
          flex: 1;
          min-width: 0;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          min-height: 48 * @rem;
          margin: 0 10 * @rem;

          .name {
            width: 100%;
            max-height: 32 * @rem;
            font-weight: 500;
            font-size: 14 * @rem;
            color: #ffffff;
            line-height: 16 * @rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .date {
            width: 100%;
            height: 14 * @rem;
            font-weight: 400;
            font-size: 11 * @rem;
            color: rgba(255, 255, 255, 0.6);
            line-height: 14 * @rem;
            margin-top: 4 * @rem;
          }
        }

        .coupon-right {
          .get-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 56 * @rem;
            height: 30 * @rem;
            line-height: 30 * @rem;
            font-weight: 500;
            font-size: 13 * @rem;
            color: #7f3e00;
            text-align: center;
            background: linear-gradient(90deg, #ffe7c5 0%, #ffc983 98%);
            border-radius: 26 * @rem;
          }
        }
      }
    }
    .card-box {
      box-sizing: border-box;
      position: relative;

      .card-list {
        width: 100%;
        position: relative;

        .card-group {
          margin-top: 2 * @rem;
          margin-bottom: 29 * @rem;
        }
        .card-item {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          width: 100%;
          height: 91 * @rem;
          border-radius: 8 * @rem;
          padding: 0 12 * @rem;
          margin-bottom: 10 * @rem;
          box-sizing: border-box;
          background-color: #322c26;
          .card-info {
            display: flex;
            align-items: center;
            width: 100%;

            .card-left {
              margin-right: 7 * @rem;
              img {
                display: block;
                width: 36 * @rem;
                height: 36 * @rem;
              }
            }

            .info {
              flex: 1;
              min-width: 0;

              .name {
                width: 100%;
                height: 16 * @rem;
                font-family: PingFang SC, PingFang SC;
                font-weight: 500;
                font-size: 14 * @rem;
                color: #ffffff;
                line-height: 16 * @rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              .desc {
                width: 100%;
                height: 11 * @rem;
                font-weight: 400;
                font-size: 11 * @rem;
                font-family: PingFang SC, PingFang SC;
                color: #93999f;
                line-height: 11 * @rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-top: 6 * @rem;
              }
            }

            .card-right {
              margin-left: 10 * @rem;
              .get-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 56 * @rem;
                height: 30 * @rem;
                line-height: 30 * @rem;
                font-weight: 500;
                font-size: 13 * @rem;
                color: #7f3e00;
                text-align: center;
                background: linear-gradient(90deg, #ffe7c5 0%, #ffc983 98%);
                border-radius: 26 * @rem;
              }
            }
          }
          .card-count {
            display: flex;
            align-items: center;
            width: 100%;
            margin-top: 11 * @rem;

            .process {
              flex: 1;
              min-width: 0;
              height: 5 * @rem;
              background: rgba(255, 255, 255, 0.25);
              border-radius: 19 * @rem;
              position: relative;
              margin-right: 12 * @rem;

              span {
                display: block;
                height: 5 * @rem;
                background: linear-gradient(90deg, #ffc983 2%, #f4dfc6 100%);
                border-radius: 26 * @rem;
                position: absolute;
                top: 0;
                left: 0;
              }
            }

            .number {
              display: block;
              height: 15 * @rem;
              line-height: 15 * @rem;
              font-weight: 400;
              font-size: 11 * @rem;
              color: #ffdca8;
              padding: 0 5 * @rem;
            }
          }
        }
      }
    }
  }
  .welfare-dialog {
    width: 285 * @rem;
    background-color: transparent;
    .dialog-close {
      width: 26 * @rem;
      height: 26 * @rem;
      margin: 15 * @rem auto 0;
      background: url(~@/assets/images/recharge/svip-dialog-close.png) no-repeat;
      background-size: 26 * @rem 26 * @rem;
    }
    .welfare-dialog-content {
      width: 285 * @rem;
      height: 300 * @rem;
      background: url(~@/assets/images/recharge/svip-dialog-bg.png) no-repeat;
      background-size: 285 * @rem 300 * @rem;
      overflow: hidden;
      .title {
        font-size: 18 * @rem;
        color: #2b242d;
        font-weight: 600;
        margin-top: 94 * @rem;
        text-align: center;
      }
      .desc {
        box-sizing: border-box;
        font-size: 14 * @rem;
        color: #2b242d;
        height: 80 * @rem;
        width: 100%;
        padding: 0 30 * @rem;
        line-height: 20 * @rem;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        margin-top: 20 * @rem;
        text-align: center;
      }
      .welfare-btn {
        width: 166 * @rem;
        height: 38 * @rem;
        border-radius: 19 * @rem;
        font-size: 14 * @rem;
        background: linear-gradient(90deg, #dc7467 0%, #ac6eb7 100%);
        box-shadow: 0 * @rem 2 * @rem 6 * @rem 0 * @rem
          rgba(175, 111, 176, 0.47);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 20 * @rem auto 0;
      }
    }
  }
  .pay-container-popup {
    .pay-container {
      padding: 10 * @rem 14 * @rem 0;
      padding-bottom: @safeAreaBottom;
      padding-bottom: @safeAreaBottomEnv;
      .pay-way-close {
        position: absolute;
        right: 10 * @rem;
        top: 10 * @rem;
        width: 40 * @rem;
        height: 40 * @rem;
        background: url(~@/assets/images/recharge/recharge-popup-close.png)
          center center no-repeat;
        background-size: 25 * @rem 25 * @rem;
      }
      .pay-way-title {
        font-size: 16 * @rem;
        color: #333;
        height: 40 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .pay-list {
        .pay-item {
          display: flex;
          align-items: center;
          padding: 16 * @rem 0;
          border-bottom: 1px solid #eeeeee;
          .icon {
            width: 24 * @rem;
            height: 24 * @rem;
            background-position: center center;
            background-repeat: no-repeat;
            background-size: 24 * @rem 24 * @rem;
            // &.wx {
            //   background-image: url(~@/assets/images/recharge/wx-icon.png);
            //   background-size: 24 * @rem 21 * @rem;
            // }
            // &.zfb_dmf {
            //   background-image: url(~@/assets/images/recharge/zfb-icon.png);
            //   background-size: 24 * @rem 24 * @rem;
            // }
          }
          .pay-center {
            margin-left: 8 * @rem;
            .line {
              display: flex;
              align-items: center;
              .pay-name {
                font-size: 15 * @rem;
                color: #333;
              }
              .recommend-icon {
                width: 39 * @rem;
                height: 17 * @rem;
                background-size: 39 * @rem 17 * @rem;
                background-position: left center;
                background-repeat: no-repeat;
                margin-left: 5 * @rem;
              }
            }
            .subtitle {
              color: #999;
              font-size: 12 * @rem;
              line-height: 20 * @rem;
            }
          }
          .choose {
            width: 18 * @rem;
            height: 18 * @rem;
            background: url(~@/assets/images/recharge/n_radio.png) center center
              no-repeat;
            background-size: 18 * @rem 18 * @rem;
            margin-left: auto;
            &.on {
              background-image: url(~@/assets/images/recharge/c_radio.png);
            }
          }
        }
      }
      .pay-btn {
        width: 290 * @rem;
        height: 45 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #8a60e3;
        font-size: 18 * @rem;
        font-weight: bold;
        color: #ffffff;
        margin: 30 * @rem auto;
        border-radius: 23 * @rem;
        box-shadow: 0 * @rem 2 * @rem 13 * @rem 0 * @rem rgba(99, 68, 24, 0.24);
      }
    }
  }
  .search-container {
    box-sizing: border-box;
    width: 320 * @rem;
    height: 450 * @rem;
    max-height: 100vh;
    padding: 24 * @rem 19 * @rem 10 * @rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: unset;
    .close-search {
      width: 24 * @rem;
      height: 24 * @rem;
      background: url(~@/assets/images/recharge/close-search.png) center center
        no-repeat;
      background-size: 24 * @rem 24 * @rem;
      position: absolute;
      right: -10 * @rem;
      top: -10 * @rem;
    }
    .search-bar {
      display: flex;
      align-items: center;
      .input-text {
        width: 240 * @rem;
        height: 35 * @rem;
        border: 1px solid #e5e5e5;
        border-radius: 18 * @rem;
        flex: 1;
        overflow: hidden;
        form {
          display: block;
          width: 100%;
          height: 100%;
        }
        input {
          box-sizing: border-box;
          display: block;
          width: 100%;
          height: 100%;
          padding: 0 18 * @rem;
          font-size: 15 * @rem;
          color: #333333;
          background-color: #f6f6f6;
        }
      }
      .search-btn {
        font-size: 15 * @rem;
        color: #666666;
        padding-left: 13 * @rem;
        height: 35 * @rem;
        line-height: 35 * @rem;
      }
    }
    .game-list {
      flex: 1;
      overflow: auto;
      margin-top: 10 * @rem;
      .game-item {
        display: flex;
        align-items: center;
        padding: 10 * @rem 0;
        border-bottom: 1px solid #eeeeee;
        .game-icon {
          width: 50 * @rem;
          height: 50 * @rem;
          border-radius: 10 * @rem;
          background-color: #b5b5b5;
        }
        .right {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          .game-name {
            font-size: 16 * @rem;
            font-weight: bold;
            color: #000000;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .use-card {
            font-size: 12 * @rem;
            color: #f72e2e;
            margin-top: 10 * @rem;
            &.can {
              color: #36b150;
            }
          }
        }
      }
    }
  }
  .search-game-popup {
    overflow: unset;
    width: 320 * @rem;
  }
  .svip-ad-popup {
    .popup-container {
      box-sizing: border-box;
      width: 305 * @rem;
      height: 278 * @rem;
      border-radius: 16 * @rem;
      background-color: #fff;
      position: relative;
      padding: 28 * @rem 0 0;
      .close {
        width: 48 * @rem;
        height: 48 * @rem;
        background: url(~@/assets/images/recharge/svip-ad-close.png) center
          center no-repeat;
        background-size: 16 * @rem 16 * @rem;
        position: absolute;
        right: 0;
        top: 0;
      }
      .title {
        font-size: 18 * @rem;
        color: #333333;
        line-height: 23 * @rem;
        font-weight: 600;
        text-align: center;
      }
      .card-container {
        width: 227 * @rem;
        height: 72 * @rem;
        background: url(~@/assets/images/recharge/svip-ad-card-bg-new.png)
          no-repeat;
        background-size: 227 * @rem 72 * @rem;
        margin: 30 * @rem auto 0;
        display: flex;
        align-items: center;
        .discount {
          width: 82 * @rem;
          font-size: 16 * @rem;
          color: #fff;
          text-align: center;
          span {
            font-size: 26 * @rem;
          }
        }
        .card-right {
          flex: 1;
          min-width: 0;
          margin-left: 20 * @rem;
          .card-title {
            font-size: 14 * @rem;
            color: #ffffff;
            line-height: 18 * @rem;
          }
          .card-desc {
            font-size: 11 * @rem;
            color: #ffffff;
            line-height: 14 * @rem;
            margin-top: 5 * @rem;
          }
        }
      }
      .tips {
        font-size: 15 * @rem;
        color: #333333;
        line-height: 25 * @rem;
        text-align: center;
        margin: 10 * @rem auto 0;
        .old {
          font-size: 15 * @rem;
          color: #999999;
          text-decoration: line-through;
        }
        .now {
          font-size: 20 * @rem;
          color: #fd2727;
          font-weight: 600;
        }
      }
      .buy-now {
        font-size: 15 * @rem;
        color: #fff;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 20 * @rem;
        width: 165 * @rem;
        height: 40 * @rem;
        margin: 25 * @rem auto 0;
        background-color: #895ff6;
        box-shadow: 0 * @rem 4 * @rem 8 * @rem 0 * @rem rgba(28, 36, 233, 0.2);
      }
    }
  }
  .double-11-popup {
    background: transparent;
    .double-11-banner {
      width: 300 * @rem;
      height: 350 * @rem;
    }
    .popup-close {
      width: 28 * @rem;
      height: 28 * @rem;
      background: url(~@/assets/images/recharge/activity-popup-close.png) center
        center no-repeat;
      background-size: 28 * @rem 28 * @rem;
      margin: 28 * @rem auto 0;
    }
  }
  .svip-popup {
    .popup-content {
      width: 100%;
      box-sizing: border-box;
      background: linear-gradient(
        180deg,
        #ffddc4 0%,
        #fdf5eb 31%,
        #fdf5eb 100%
      );
      border-radius: 8 * @rem;
      padding: 30 * @rem;

      .text {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 16 * @rem;
        color: #7f3e00;
        line-height: 20 * @rem;
        text-align: center;
        margin: 0 auto;
      }

      .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 149 * @rem;
        height: 40 * @rem;
        line-height: 40 * @rem;
        font-family: PingFang SC, PingFang SC;
        text-align: center;
        font-weight: 500;
        font-size: 16 * @rem;
        color: #7f3e00;
        background: linear-gradient(180deg, #ffdbb0 0%, #ffb673 98%);
        border-radius: 26 * @rem;
        margin: 46 * @rem auto 0;
      }
    }
  }
  .coin-popup {
    .popup-content {
      width: 100%;
      box-sizing: border-box;
      background: linear-gradient(
        180deg,
        #ffddc4 0%,
        #fdf5eb 31%,
        #fdf5eb 100%
      );
      border-radius: 8 * @rem;
      padding: 30 * @rem;

      .text {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 16 * @rem;
        color: #7f3e00;
        line-height: 20 * @rem;
        text-align: center;
        margin: 0 auto;
      }

      .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 149 * @rem;
        height: 40 * @rem;
        line-height: 40 * @rem;
        font-family: PingFang SC, PingFang SC;
        text-align: center;
        font-weight: 500;
        font-size: 16 * @rem;
        color: #7f3e00;
        background: linear-gradient(180deg, #ffdbb0 0%, #ffb673 98%);
        border-radius: 26 * @rem;
        margin: 46 * @rem auto 0;
      }
    }
  }
  ::v-deep {
    .swiper-pagination {
      bottom: 7 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 6 * @rem;

      .swiper-pagination-bullet {
        display: block;
        width: 6 * @rem;
        height: 6 * @rem;
        margin-right: 12 * @rem;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.4);

        &:last-of-type {
          margin-right: 0;
        }

        &.swiper-pagination-bullet-active {
          width: 15 * @rem;
          border-radius: 108 * @rem;
          background-color: #ffffff;
        }
      }
    }
  }
  .text-scroll {
    flex-shrink: 0;
    flex-grow: 1;
    white-space: nowrap;
    animation: scroll-left 5s linear forwards infinite;
  }
  @keyframes scroll-left {
    0% {
      transform: translateX(0);
    }
    20% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-50%);
    }
  }
}
</style>
