export default [
  {
    path: '/task_new',
    name: 'TaskNew',
    component: () =>
      import(/* webpackChunkName: "welfare" */ '@/views/Welfare/TaskNew'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
      pageTitle: '新手任务',
    },
  },
  {
    path: '/task_daily',
    name: 'TaskDaily',
    component: () =>
      import(/* webpackChunkName: "welfare" */ '@/views/Welfare/TaskDaily'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
      pageTitle: '每日任务',
    },
  },
  {
    path: '/task_achievement',
    name: 'TaskAchievement',
    component: () =>
      import(
        /* webpackChunkName: "welfare" */
        '@/views/Welfare/TaskAchievement'
      ),
    meta: {
      keepAlive: false,
      requiresAuth: true,
      pageTitle: '成就任务',
    },
  },
  {
    path: '/hall_of_fame',
    name: 'HallOfFame',
    component: () =>
      import(/* webpackChunkName: "welfare" */ '@/views/Welfare/HallOfFame'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
      pageTitle: '名人堂',
    },
  },
  {
    path: '/game_tester',
    name: 'GameTester',
    component: () =>
      import(/* webpackChunkName: "welfare" */ '@/views/Welfare/GameTester'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/clock_in',
    name: 'ClockIn',
    component: () =>
      import(/* webpackChunkName: "welfare" */ '@/views/Welfare/ClockIn'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
      pageTitle: '签到',
    },
  },
  {
    path: '/turn_table',
    name: 'TurnTable',
    component: () =>
      import(/* webpackChunkName: "welfare" */ '@/views/Welfare/TurnTable'),
    meta: {
      keepAlive: false,
      requiresAuth: true,
      pageTitle: '金币大转盘',
    },
  },
  {
    path: '/invite',
    name: 'Invite',
    component: () =>
      import(/* webpackChunkName: "welfare" */ '@/views/Welfare/Invite'),
    meta: {
      requiresAuth: true,
      pageTitle: '邀请好友',
    },
  },
  {
    path: '/invite/exchange_gold',
    name: 'ExchangeGold',
    component: () =>
      import(
        /* webpackChunkName: "welfare" */
        '@/views/Welfare/Invite2/ExchangeGold'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/invite/cash_out',
    name: 'CashOut',
    component: () =>
      import(
        /* webpackChunkName: "welfare" */
        '@/views/Welfare/Invite2/CashOut'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/invite/record/:type',
    name: 'InviteCostRecord',
    component: () =>
      import(
        /* webpackChunkName: "welfare" */
        '@/views/Welfare/Invite2/InviteCostRecord'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/invite/detail/:type',
    name: 'InviteDetail',
    component: () =>
      import(
        /* webpackChunkName: "welfare" */
        '@/views/Welfare/Invite2/InviteDetail'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/invite/course',
    name: 'InviteCourse',
    component: () =>
      import(
        /* webpackChunkName: "welfare" */
        '@/views/Welfare/Invite2/InviteCourse'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/invite/rule',
    name: 'InviteRule',
    component: () =>
      import(
        /* webpackChunkName: "welfare" */
        '@/views/Welfare/Invite2/InviteRule'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/free_play',
    name: 'FreePlay',
    component: () =>
      import(/* webpackChunkName: "welfare" */ '@/views/Welfare/FreePlay'),
    meta: {
      requiresAuth: false,
      pageTitle: '0元畅玩',
    },
  },
  {
    path: '/01_discount',
    name: '01Discount',
    component: () =>
      import(/* webpackChunkName: "welfare" */ '@/views/Welfare/01Discount'),
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: '/gold_mall',
    name: 'GoldMall',
    component: () =>
      import(/* webpackChunkName: "welfare" */ '@/views/Welfare/GoldMall'),
    meta: {
      requiresAuth: true,
      pageTitle: '金币商城',
    },
  },
  {
    path: '/gold_gamble',
    name: 'GoldGamble',
    component: () =>
      import(/* webpackChunkName: "welfare" */ '@/views/Welfare/GoldGamble'),
    meta: {
      requiresAuth: true,
      keepAlive: true,
      pageTitle: '金币夺宝',
    },
  },
  {
    path: '/gamble_detail/:id',
    name: 'GambleDetail',
    component: () =>
      import(
        /* webpackChunkName: "welfare" */
        '@/views/Welfare/GoldGamble/GambleDetail'
      ),
    meta: {
      requiresAuth: true,
      pageTitle: '金币夺宝详情',
    },
  },
  {
    path: '/gamble_rule',
    name: 'GambleRule',
    component: () =>
      import(
        /* webpackChunkName: "welfare" */
        '@/views/Welfare/GoldGamble/GambleRule'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/my_gamble',
    name: 'MyGamble',
    component: () =>
      import(
        /* webpackChunkName: "welfare" */
        '@/views/Welfare/GoldGamble/MyGamble'
      ),
    meta: {
      requiresAuth: true,
      keepAlive: true,
    },
  },
  {
    path: '/game_try',
    name: 'GameTry',
    component: () =>
      import(/* webpackChunkName: "welfare" */ '@/views/Welfare/GameTry'),
    meta: {
      requiresAuth: true,
      pageTitle: '新游试玩',
    },
  },
  {
    path: '/game_try_detail/:id',
    name: 'GameTryDetail',
    component: () =>
      import(
        /* webpackChunkName: "welfare" */
        '@/views/Welfare/GameTry/GameTryDetail'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/game_try_rule',
    name: 'GameTryRule',
    component: () =>
      import(
        /* webpackChunkName: "welfare" */
        '@/views/Welfare/GameTry/GameTryRule'
      ),
  },
  {
    path: '/clock_challenge',
    name: 'ClockChallenge',
    component: () =>
      import(
        /* webpackChunkName: "welfare" */
        '@/views/Welfare/ClockChallenge'
      ),
    meta: {
      pageTitle: '打卡挑战',
    },
  },
  {
    path: '/tx_recharge_area',
    name: 'TxRechargeArea',
    component: () =>
      import(
        /* webpackChunkName: "welfare" */
        '@/views/Welfare/TxRechargeArea'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/welfare_648',
    name: 'Welfare648',
    component: () =>
      import(/* webpackChunkName: "welfare" */ '@/views/Welfare/Welfare648'),
    meta: {
      requiresAuth: false,
      pageTitle: '648福利',
    },
  },
  {
    path: '/bounty_task',
    name: 'BountyTask',
    component: () =>
      import(/* webpackChunkName: "welfare" */ '@/views/Welfare/BountyTask'),
    meta: {
      requiresAuth: true,
      pageTitle: '赏金任务',
    },
  },
  {
    path: '/bounty_task_rule',
    name: 'BountyTaskRule',
    component: () =>
      import(
        /* webpackChunkName: "welfare" */
        '@/views/Welfare/BountyTask/BountyTaskRule'
      ),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/svip_welfare',
    name: 'SvipWelfare',
    component: () =>
      import(/* webpackChunkName: "welfare" */ '@/views/Welfare/SvipWelfare'),
    meta: {
      requiresAuth: true,
      pageTitle: 'SVIP专属福利',
    },
  },
  {
    path: '/platform_coin_swap_props',
    name: 'PlatformCoinSwapProps',
    component: () =>
      import( /* webpackChunkName: "welfare" */ '@/views/Welfare/PlatformCoinSwapProps'),
    meta: {
      requiresAuth: false,
      keepAlive: true,
      pageTitle: '平台币兑换道具',
    },
  },
  {
    path: '/clock_in_rule',
    name: 'ClockInRule',
    component: () =>
      import(/* webpackChunkName: "welfare" */ '@/views/Welfare/ClockInRule'),
    meta: {
      keepAlive: true,
      pageTitle: '签到规则',
    },
  },
];
