<template>
  <div class="buy-list-normal">
    <deal-item v-for="item in list" :key="item.id" :info="item"></deal-item>
  </div>
</template>

<script>
export default {
  name: 'buyListNormal',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
};
</script>

<style lang="less" scoped>
.buy-list-normal {
  box-sizing: border-box;
  width: 100%;
  // padding: 0 14 * @rem;
  /deep/.deal-item-component {
    border-bottom: 1 * @rem solid #ebebeb;
  }
  .buy-item {
    display: flex;
    padding: 20 * @rem 0 15 * @rem;
    border-bottom: 1px solid #eeeeee;
    &:nth-of-type(1) {
      padding-top: 5 * @rem;
    }
    .left {
      flex: 1;
      min-width: 0;
      .info {
        display: flex;
        .pic {
          width: 70 * @rem;
          height: 70 * @rem;
          border-radius: 10 * @rem;
          background-color: #dcdcdc;
          overflow: hidden;
          img {
            object-fit: cover;
          }
        }
        .game-info {
          flex: 1;
          min-width: 0;
          margin-left: 14 * @rem;
          display: flex;
          flex-direction: column;
          justify-content: center;
          .title {
            font-size: 16 * @rem;
            color: #000000;
            // font-weight: bold;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .game-name {
            font-size: 12 * @rem;
            color: #ff8c05;
            margin-top: 6 * @rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .game-port {
            font-size: 12 * @rem;
            color: #666666;
            margin-top: 8 * @rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
      .desc {
        font-size: 12 * @rem;
        color: #999999;
        margin-top: 10 * @rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        span {
          color: #ff8c05;
        }
      }
    }
    .right {
      width: 90 * @rem;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      .price {
        font-size: 15 * @rem;
        color: #ee1d44;
        font-weight: bold;
        margin-top: 15 * @rem;
      }
      .gold {
        font-size: 10 * @rem;
        color: #ff395e;
        background-color: #ff395d10;
        border-radius: 3 * @rem;
        height: 15 * @rem;
        padding: 0 4 * @rem;
        display: flex;
        align-items: center;
        margin-top: 5 * @rem;
      }
      .platform {
        display: flex;
        align-items: center;
        margin-top: 20 * @rem;
        .plat-icon {
          width: 17 * @rem;
          height: 17 * @rem;
        }
      }
    }
  }
}
</style>
