<template>
  <div class="fragment-game-list">
    <div class="fragment-title">
      <div class="title-text">{{ info.header_title }}</div>
      <div class="right-icon" v-if="info.action_code" @click="clickMore"></div>
    </div>
    <div class="game-list">
      <div
        class="game-item"
        v-for="(game, index) in info.game_list"
        @click="goToGame(game, index)"
        :key="index"
        v-sensors-exposure="gameExposure(game, index)"
      >
        <div class="game-icon">
          <img :src="game.titlepic" alt="" />
        </div>
        <div class="game-name">{{ game.main_title }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { BOX_goToGame } from '@/utils/box.uni.js';
import { handleActionCode } from '@/utils/actionCode.js';
export default {
  name: 'FragmentGameListX',
  props: {
    info: {
      type: Object,
      required: true,
      default: () => {},
    },
  },
  methods: {
    gameExposure(item, index) {
      return {
        'event-name': 'game_exposure',
        'property-page_name': this.$sensorsPageGet(),
        'property-section_name': this.info.header_title || '暂无',
        'property-game_id': `${item.id}`,
        'property-game_name': item.title,
        'property-game_index': `${index}`,
      };
    },
    goToGame(item, index) {
      // 神策埋点
      this.$sensorsTrack('game_click', {
        page_name: this.$sensorsPageGet(),
        section_name: this.info.header_title || '暂无',
        game_id: `${item.id}`,
        game_name: item.title,
        game_index: `${index}`,
      });
      BOX_goToGame(
        {
          params: {
            id: item.id,
            gameInfo: item,
          },
        },
        { id: item.id },
      );
    },
    clickMore() {
      handleActionCode(this.info);
    },
  },
};
</script>

<style lang="less" scoped>
.fragment-game-list {
  padding: 7 * @rem 0;
  margin: 0 12 * @rem;
  background: #fff;
  border-radius: 12 * @rem;
  .fragment-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 7 * @rem 0;
    margin: 0 12 * @rem;
    .title-text {
      font-size: 16 * @rem;
      font-weight: bold;
      color: #191b1f;
      line-height: 16 * @rem;
    }
    .right-icon {
      width: 6 * @rem;
      height: 10 * @rem;
      background: url(~@/assets/images/right-icon.png) right center no-repeat;
      background-size: 6 * @rem 10 * @rem;
      padding-left: 20 * @rem;
    }
  }
  .game-list {
    display: flex;
    overflow-x: auto;
    // margin: 0 12*@rem;
    padding: 7 * @rem 3 * @rem;
    &::-webkit-scrollbar {
      display: none;
    }

    .game-item {
      flex-shrink: 0;
      width: 80 * @rem;

      &:last-of-type {
        margin-right: 0;
      }

      .game-icon {
        width: 59 * @rem;
        height: 59 * @rem;
        margin: 0 auto;

        img {
          border-radius: 12 * @rem;
        }
      }

      .game-name {
        box-sizing: border-box;
        width: 100%;
        height: 11 * @rem;
        font-weight: 400;
        font-size: 11 * @rem;
        color: #60666c;
        line-height: 11 * @rem;
        margin-top: 8 * @rem;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>
