<template>
  <!-- 金刚区V2 -->
  <div class="nav-list-component">
    <swiper
      id="navListSwiperV2"
      ref="navListSwiperV2"
      :options="swiperOptions"
      :auto-update="true"
      style="width: 100%; margin: 0 auto"
      v-if="show && navList.length > 0"
    >
      <swiper-slide
        class="nav-item btn"
        v-for="(item, index) in navList"
        :key="index"
        v-sensors-exposure="iconExposure(item, index)"
      >
        <template>
          <yy-lottie
            class="lottie-icon"
            :width="55"
            :height="51"
            :options="{
              autoplay: true,
              loop: true,
              path: item.icon_lottie,
            }"
            v-if="item.icon_lottie"
          ></yy-lottie>
          <div class="nav-icon" v-else>
            <div
              class="img"
              :style="{ backgroundImage: `url(${item.icon_url})` }"
            ></div>
          </div>
          <div class="nav-name">{{ item.text1 }}</div>
        </template>
      </swiper-slide>
    </swiper>
    <!-- <div class="swiper-scrollbar" :class="`swiper-scrollbar-${nowTime}`"></div> -->
  </div>
</template>

<script>
import { PageName, handleActionCode } from '@/utils/actionCode.js';
export default {
  name: 'NavList',
  props: {
    info: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    let that = this;
    return {
      show: false,
      nowTime: Date.now(),
      navList: [],
      swiperOptions: {
        slidesPerView: 'auto',
        freeMode: true,
        freeModeMomentumRatio: 0.3, // 运动惯量
        observer: true, //开启动态检查器，监测swiper和slide
        observeSlideChildren: true, //监测Swiper的子元素wrapper、pagination、navigation、scrollbar或其他一级子元素
        // scrollbar: {
        //   el: '.swiper-scrollbar',
        // },
        on: {
          init() {
            // 金刚区首次加载动画演示
            let navAnimation = localStorage.getItem('NAV_ANIMATION');
            if (!navAnimation) {
              this.detachEvents();
              that.$nextTick(() => {
                let navItemWidth = parseInt(
                  document.querySelector('body').clientWidth / 5,
                );
                let navNum = that.navList.length;
                let duration = 1000;
                if (navNum > 5) {
                  this.translateTo(
                    -navItemWidth * (navNum - 5),
                    duration,
                    true,
                    false,
                  );
                  setTimeout(() => {
                    this.slideTo(0, duration, true);
                  }, 1300);
                  setTimeout(() => {
                    this.attachEvents();
                    localStorage.setItem('NAV_ANIMATION', 1);
                  }, 2300);
                }
              });
            }
          },
          click: function () {
            setTimeout(() => {
              let nav = that.navList[this.clickedIndex];
              // 神策埋点
              that.$sensorsTrack('icon_click', {
                page_name: that.$sensorsPageGet(),
                icon_index: `${this.clickedIndex}`,
                icon_name: nav.text1,
              });
              that.CLICK_EVENT(nav.click_id);
              const info = {
                ...nav,
                header_title: nav.text1,
              };
              that.handleActionCode(info);
            }, 0);
          },
        },
      },
    };
  },
  created() {
    this.navList = this.info.tab_action;
    // this.swiperOptions.scrollbar.el = `.swiper-scrollbar-${this.nowTime}`;
    this.show = true;
  },
  mounted() {
    this.$nextTick(() => {
      // 解决金刚区点击穿透的问题
      document
        .querySelector('#navListSwiperV2')
        .addEventListener('touchstart', function (e) {
          e.preventDefault();
        }),
        {
          passive: false,
        };
    });
  },
  methods: {
    handleActionCode,
    
    iconExposure(item, index) {
      return {
        'event-name': 'icon_exposure',
        'property-page_name': this.$sensorsPageGet(),
        'property-icon_index': `${index}`,
        'property-icon_name': item.text1,
      }
    },
  },
};
</script>

<style lang="less" scoped>
.nav-list-component {
  padding-left: 8 * @rem;
  width: 100%;
  box-sizing: border-box;
  .swiper-scrollbar {
    margin: 8 * @rem auto 0;
    width: 17 * @rem;
    height: 3 * @rem;
    background-color: #e3e5e8;
  }
  /deep/ .swiper-scrollbar-drag {
    width: 6 * @rem;
    height: 3 * @rem;
    background: #93999f;
  }
  .nav-item {
    width: 77 * @rem;
    flex-shrink: 0;
    &:nth-of-type(1) {
      margin-left: 0;
    }
    .lottie-icon {
      width: 55 * @rem;
      height: 51 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .nav-icon {
      width: 55 * @rem;
      height: 51 * @rem;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      // background-position: center center;
      // background-repeat: no-repeat;
      // background-size: 50 * @rem 50 * @rem;
      margin: 0 auto;
      // &.changwan-icon {
      //   background-image: url(~@/assets/images/home/<USER>
      // }
      // &.lingquan-icon {
      //   background-image: url(~@/assets/images/home/<USER>
      // }
      // &.chongzhi-icon {
      //   background-image: url(~@/assets/images/home/<USER>
      // }
      // &.kaifu-icon {
      //   background-image: url(~@/assets/images/home/<USER>
      // }
      img {
        width: 100%;
        height: 100%;
      }
      .img {
        width: 100%;
        height: 100%;
        background-size: 100%;
      }
    }
    .nav-name {
      text-align: center;
      font-size: 12 * @rem;
      color: #000000;
      font-weight: 500;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      // margin-top: 6 * @rem;
    }
  }
}
</style>
