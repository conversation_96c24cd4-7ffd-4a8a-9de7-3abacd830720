<template>
  <div class="exclusive-activity-page">
    <nav-bar-2
      v-if="!isInFragment"
      bgStyle="transparent-white"
      :placeholder="false"
      :azShow="true"
    >
    </nav-bar-2>
    <div
      v-if="isInFragment && platform != 'android' && isEmbedded"
      class="placeholder"
      :style="{ backgroundColor: `${config.background_color}` }"
    ></div>
    <div
      class="main"
      :style="{ backgroundColor: `${config.background_color}` }"
    >
      <div class="fixed-btns">
        <div
          v-if="config.game_detail_button"
          class="btn fixed-btn fixed-detail"
          :style="{ backgroundImage: `url(${config.game_detail_button})` }"
          @click="goToGame"
        ></div>
        <div
          v-if="config.activity_rules_button"
          class="btn fixed-btn fixed-rule"
          @click="rulePopup = true"
          :style="{ backgroundImage: `url(${config.activity_rules_button})` }"
        ></div>
        <div
          v-if="config.win_record_button"
          class="btn fixed-btn fixed-record"
          :style="{ backgroundImage: `url(${config.win_record_button})` }"
          @click="getPrizeLog"
        ></div>
      </div>

      <div class="top-bg">
        <img class="top-bg-img" :src="config.cover" alt="" />
        <div
          class="top-bg-linear"
          :style="{
            backgroundImage: `linear-gradient(180deg, rgba(0, 0, 0, 0), ${config.background_color})`,
          }"
        ></div>
        <div class="download-btn" @click="goToGame">
          <img :src="config.down_button" alt="" />
        </div>
      </div>
      <!-- roll_status：广播开关 -->
      <div
        class="broadcast"
        :style="{ backgroundImage: `url(${config.roll_underlay})` }"
        v-if="config.roll_status"
      >
        <div
          class="broadcast-icon"
          :style="{ backgroundImage: `url(${config.notice_icon})` }"
        ></div>
        <van-swipe
          class="broadcast-swiper"
          vertical
          :autoplay="2000"
          :touchable="false"
          :show-indicators="false"
          v-if="lottery_log.length > 0"
        >
          <van-swipe-item v-for="(item, index) in lottery_log" :key="index">
            <div
              class="broadcast-item"
              :style="{ color: `${config.roll_font}` }"
            >
              恭喜 {{ item.nickname }} 获得 {{ item.title }}
            </div>
          </van-swipe-item>
        </van-swipe>
      </div>

      <!-- 抽奖container -->
      <div class="section-container" v-if="config.lottery_model_switch">
        <div class="section-title">
          <img :src="config.title_diagram" alt="" />
        </div>
        <div
          class="raffle-content"
          :style="{ backgroundColor: `${config.module_bg_color}` }"
        >
          <!-- 转盘 -->
          <div class="table-container">
            <div class="table-content">
              <div class="table-list">
                <div
                  class="table-item"
                  :style="{
                    backgroundImage: `url(${current == index + 1 ? config.prize_base_map : config.prize_border})`,
                  }"
                  v-for="(item, index) in lottery_list"
                  :key="index"
                >
                  <div class="reward-icon">
                    <img class="reward-img" :src="item.cover" alt="" />
                  </div>
                  <div
                    class="reward-text"
                    :style="{ color: `${config.price_title_color}` }"
                    >{{ item.title }}</div
                  >
                </div>
                <div
                  class="table-item turn-btn-1 btn"
                  :style="{ backgroundImage: `url(${config.prize_button})` }"
                  @click="handleRaffle"
                >
                </div>
              </div>
            </div>
          </div>
          <div class="task-list">
            <div
              class="task-item"
              v-for="(item, index) in task_list"
              :key="index"
            >
              <div
                class="task-title"
                :style="{ color: `${config.compliance_font}` }"
                >{{ item.title }}
                <span v-if="item.recharge_amount"
                  >({{ item.amount || 0 }}/{{ item.recharge_amount }})</span
                ></div
              >
              <div
                class="task-reward"
                :style="{ color: `${config.lottery_font}` }"
                >抽奖次数+{{ item.lottery_num }}</div
              >
              <!-- 0 去完成 1 领取  2 已完成 -->
              <div
                class="task-btn"
                :style="{ backgroundImage: `url(${item.image})` }"
                @click="taskBtnClick(item)"
              ></div>
            </div>
          </div>
        </div>
        <div class="raffle-tips" @click="handleRefresh" :style="{ color: `${config.lottery_font_color || '#fff'}` }"
          >抽奖次数 : {{ lottery_num }}
          <span :style="{ color: `${config.flash_font}` }">刷新</span></div
        >
      </div>

      <!-- 游戏视频 -->
      <div class="section-container" v-if="config.video_model_switch">
        <div class="section-title">
          <img :src="config.model_diagram" alt="" />
        </div>
        <div
          class="video-container"
          :style="{
            border: `${config.video_line_width}px solid ${config.video_border_color}`,
          }"
        >
          <video
            class="video"
            id="video"
            :poster="config.video_cover"
            :src="config.video_url"
            :controls="videoPlaying"
          ></video>
          <div class="video-play" @click="playVideo" v-if="!videoPlaying"></div>
        </div>
      </div>

      <!-- 游戏特色 -->
      <div class="section-container" v-if="config.game_image_switch">
        <div class="section-title">
          <img :src="config.game_diagram" alt="" />
        </div>
        <div class="pic-swiper">
          <swiper
            :options="swiperOption"
            ref="mySwiper"
            class="swiper-container"
            v-if="game_info.morepic"
          >
            <!-- 这部分放置需要渲染的内容 -->
            <swiper-slide
              v-for="(item, index) in game_info.morepic.big"
              :key="index"
            >
              <img class="pic-img" :src="item" alt=""
            /></swiper-slide>
            <div class="swiper-pagination" slot="pagination"></div>
          </swiper>
          <!-- 悬浮下载按钮 -->
          <div
            class="down-mask"
            :class="{
              'show': downloadFixedShow,
              'home-show':
                isInFragment && platform != 'android' && downloadFixedShow,
            }"
          >
            <div
              class="down-btn"
              :style="{ backgroundImage: `url(${config.down_button})` }"
              @click="goToGame"
            ></div>
          </div>
        </div>
      </div>

      <!-- 游戏福利 -->
      <div class="section-container" v-if="config.activity_model_switch">
        <div class="section-title" v-if="config.activity_diagram">
          <img :src="config.activity_diagram" alt=""
        /></div>
        <div
          class="welfare-list"
          v-if="config.activity_desc"
          :style="{
            backgroundColor: `${config.activity_bg_color}`,
            border: `${config.activity_line_width}px solid ${config.activity_border_color}`,
          }"
        >
          <div
            class="rich-text"
            :style="{ color: config.activity_font }"
            v-html="config.activity_desc"
          ></div>
        </div>
      </div>
      <!-- <div class="copy-right" v-if="!pageLoading"
        >本活动最终解释权归官方所有</div
      > -->
    </div>
    <div
      class="return-top"
      :class="{ az: platform == 'android' }"
      v-show="showReturnTop"
      @click="handleReturnTop"
    ></div>

    <!-- 活动规则弹窗 -->
    <van-popup class="popup-container" v-model="rulePopup" :lock-scroll="false">
      <div class="popup-main">
        <div class="popup-title">活动规则</div>
        <div class="popup-content">
          <div class="rule-content" v-html="config.activity_rules_con"></div>
        </div>
      </div>
      <div class="popup-close" @click="rulePopup = false">我知道了</div>
    </van-popup>

    <!-- 中奖记录弹窗 -->
    <van-popup
      class="popup-container"
      v-model="recordPopup"
      :lock-scroll="false"
    >
      <div class="popup-main">
        <div class="popup-title">中奖记录</div>
        <div class="popup-content">
          <div class="empty" v-if="!prizeList.length">
            <content-empty></content-empty>
          </div>
          <div class="record-list" v-else>
            <div
              class="record-item"
              v-for="(item, index) in prizeList"
              :key="index"
            >
              <div class="time">{{ formatDate(item.create_time) }}</div>
              <div class="reward">{{ item.title }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="popup-close" @click="recordPopup = false">我知道了</div>
    </van-popup>

    <!-- 抽奖结果弹窗 -->
    <van-popup
      class="popup-container"
      v-model="lottery_result_popup"
      :lock-scroll="false"
    >
      <div class="popup-main small">
        <div class="popup-content">
          <div class="lottery-result" v-if="lottery_result.lottery">
            <div class="result-title">{{
              lottery_result.lottery.type == 5 ? '很遗憾' : '恭喜获得'
            }}</div>
            <img
              class="result-icon"
              :src="lottery_result.lottery.cover"
              alt=""
            />
            <div class="result-content">{{
              lottery_result.lottery.type == 5
                ? '别气馁，下次再试试~'
                : lottery_result.lottery.title
            }}</div>
            <div class="result-tips" v-if="lottery_result.lottery.tip">{{
              lottery_result.lottery.tip
            }}</div>
          </div>
        </div>
      </div>
      <div class="popup-close" @click="lottery_result_popup = false"
        >我知道了</div
      >
    </van-popup>

    <!-- 任务提示弹窗 -->
    <van-popup
      class="popup-container"
      v-model="guideCopyPopup"
      :lock-scroll="false"
    >
      <div class="popup-main small">
        <div class="popup-content">
          <div class="guide-copy">
            <div class="guide-copy-title">任务提示</div>
            <div class="guide-copy-content">
              {{ guideCopy }}
            </div>
          </div>
        </div>
      </div>
      <div class="popup-close" @click="guideCopyPopup = false">我知道了</div>
    </van-popup>
  </div>
</template>

<script>
import {
  ApiV2024ZfIndex,
  ApiV2024ZfShare,
  ApiV2024ZfTake,
  ApiV2024ZfLottery,
  ApiV2024PrizeLog,
} from '@/api/views/exclusiveActivity.js';
import {
  BOX_goToGame,
  BOX_login,
  platform,
  BOX_isInFragment,
} from '@/utils/box.uni.js';
import { mapGetters } from 'vuex';
import { ApiCommonShareInfo } from '@/api/views/system.js';
export default {
  data() {
    return {
      isInFragment: false, // 安卓 是否在碎片里
      isEmbedded: 0, // ios 从接口返回是否是嵌入式的

      platform,
      pageLoading: true,

      activity_id: '',
      operationLoading: false, // 操作loadling，防止误触

      current: 0,
      showReturnTop: false,
      isStarting: false,

      rulePopup: false,
      recordPopup: false,

      recordEmpty: true,

      swiperOption: {
        pagination: {
          el: '.swiper-pagination',
        },
        effect: 'coverflow',
        coverflowEffect: {
          rotate: 45,
          stretch: 30,
          depth: 100,
          modifier: 1,
          slideShadows: true,
        },
        loop: true,
        disableOnInteraction: false,
        autoplay: {
          delay: 2000,
          disableOnInteraction: false,
        },
        slidesPerView: 'auto',
        centeredSlides: true,
        paginationClickable: true,
        spaceBetween: 0,
        observer: true,
      },
      // 活动状态 2=未开始 3=进行中 5=已结束
      activity_status: 2,
      config: {},
      game_info: {},
      illustrate: '',
      lottery_list: [],
      lottery_log: [],
      lottery_num: 0,
      task_list: [],

      prizeList: [],

      lottery_result: {},
      lottery_result_popup: false,

      videoPlaying: false,

      shareInfo: {},

      guideCopy: '',
      guideCopyPopup: false,

      downloadFixedShow: false, // 下载按钮悬浮层
    };
  },
  computed: {
    ...mapGetters({
      initData: 'system/initData',
    }),
    activity_status_text() {
      if (!this.config.id) {
        return '';
      }
      let text = '';
      switch (this.activity_status) {
        case 2: // 未开始
          text = this.config.not_start_tip;
          break;
        case 3: // 进行中
          break;
        case 5: // 已结束
          text = this.config.end_tip;
          break;
      }
      return text;
    },
  },
  created() {
    if (this.platform == 'android') {
      this.isInFragment = BOX_isInFragment();
    } else {
      if (this.$route.name == 'ExclusiveActivityHome') {
        this.isInFragment = true;
      }
    }
  },
  mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
  },
  async activated() {
    this.pageLoading = true;
    this.$toast.loading();

    try {
      this.activity_id = Number(this.$route.params.id);
      this.addScrollEvent();

      await this.getPageData();
      this.$nextTick(() => {
        if (this.config.video_model_switch) {
          this.videoInit();
        }
      });
    } finally {
      this.pageLoading = false;
      this.$toast.clear();
    }
  },
  deactivated() {
    this.removeScrollEvent();
  },
  methods: {
    onResume() {
      this.SET_USER_INFO(false);
    },
    async taskBtnClick(item) {
      // 未登录
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      // 活动未开始或已结束
      if ([2, 5].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return;
      }

      if (item.status == 0) {
        //type  1 下载 2 登陆 3 累充 4 首充 5 分享
        // 去完成
        if ([2, 3, 4].includes(item.type)) {
          if (item.guide_copy) {
            this.guideCopy = item.guide_copy;
            this.guideCopyPopup = true;
          }
        } else if (item.type == 1) {
          this.goToGame();
        } else if (item.type == 5) {
          this.handleShare(item);
        }
        return false;
      }
      if (item.status == 2) {
        return;
      }

      if (item.status == 1) {
        // 去完成
        this.$toast.loading('正在领取...');
        const res = await ApiV2024ZfTake({
          task_id: item.id,
          id: this.activity_id,
        });
        await this.getPageData();
        return;
      }
    },
    async getShareInfo() {
      let data = {
        type: 8,
        user_id: this.userInfo.user_id ? this.userInfo.user_id : 1,
        id: this.activity_id,
      };
      const res = await ApiCommonShareInfo(data);
      this.shareInfo = res.data;
    },
    async handleShare(item) {
      // 未登录
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      // 活动未开始或已结束
      if ([2, 5].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return;
      }

      await this.getShareInfo();
      if (this.initData?.share_info?.length) {
        if (this.operationLoading) {
          return false;
        }
        this.operationLoading = true;
        setTimeout(() => {
          this.operationLoading = false;
        }, 1000);
        window.BOX.mobShare(8, this.activity_id);
      } else {
        this.$copyText(this.shareInfo.share_text + this.shareInfo.url).then(
          async res => {
            this.$toast('链接已复制到剪贴板，快去邀请好友吧~');
          },
          err => {
            this.$dialog.alert({
              message: '复制失败',
              lockScroll: false,
            });
          },
        );
      }
      this.$nextTick(async () => {
        await this.handleZfShare(item);
        await this.getPageData();
      });
    },
    async handleZfShare(item) {
      const res = await ApiV2024ZfShare({
        task_id: item.id,
        id: this.activity_id,
      });
    },
    async getPageData() {
      const res = await ApiV2024ZfIndex({
        id: this.activity_id,
      });
      this.activity_status = res.data.activity_status;
      this.config = res.data.config;
      // this.config.activity_desc = `<h3>12313</h3><p>456456456</p><h3>12313</h3><p>456456456</p><h3>12313</h3><p>456456456</p>`;
      this.game_info = res.data.game_info || {};
      this.illustrate = res.data.illustrate;
      this.lottery_list = res.data.lottery_list;
      this.lottery_log = res.data.lottery_log;
      this.lottery_num = res.data.lottery_num;
      this.task_list = res.data.task_list;
      this.isEmbedded = res.data.config?.is_embedded || 0;
    },
    async handleRefresh() {
      this.$toast.loading('刷新中...');
      await this.getPageData();
      this.$toast.clear();
    },
    async getPrizeLog() {
      // 未登录
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      // 活动未开始或已结束
      if ([2].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return;
      }

      this.$toast.loading('加载中...');
      const res = await ApiV2024PrizeLog({
        page: 1,
        listRows: 1000,
        id: this.activity_id,
      });
      this.prizeList = res.data;
      this.$toast.clear();
      this.recordPopup = true;
    },
    async handleRaffle() {
      // 未登录
      if (!this.userInfo.token) {
        BOX_login();
        return false;
      }
      // 活动未开始或已结束
      if ([2, 5].includes(this.activity_status)) {
        this.$toast(this.activity_status_text);
        return;
      }

      if (!this.lottery_num) {
        this.$toast(this.config.no_lottery_tip);
        return;
      }
      if (this.isStarting) {
        return false;
      }
      this.isStarting = true;
      this.current = 0;
      this.$toast.loading('加载中...');
      try {
        const res = await ApiV2024ZfLottery({ id: this.activity_id });
        this.$toast.clear();
        await this.turning(res.data.lottery.id);
        if (res.code == 3 || res.code == 1) {
          this.lottery_result = res.data;
          this.lottery_result_popup = true;
        }
        await this.getPageData();
      } finally {
        this.isStarting = false;
      }
    },
    turning(id) {
      return new Promise((resolve, reject) => {
        this.turnCount = this.lottery_list.findIndex(item => {
          return item.id == id;
        });
        this.turnCount = this.turnCount + 1 + 40;
        this.timer = setInterval(() => {
          if (this.turnCount > 10) {
            this.currentChange();
            this.turnCount--;
          } else {
            clearInterval(this.timer);
            this.timer = setInterval(() => {
              if (this.turnCount > 0) {
                this.currentChange();
                this.turnCount--;
              } else {
                clearInterval(this.timer);
                this.timer = null;
                resolve();
              }
            }, 300);
          }
        }, 100);
      });
    },

    currentChange() {
      if (this.current > 7) {
        this.current = 1;
      } else {
        this.current++;
      }
    },

    addScrollEvent() {
      window.addEventListener('scroll', this.handleScroll);
    },
    removeScrollEvent() {
      window.removeEventListener('scroll', this.handleScroll);
    },
    handleScroll() {
      let windowScrollTop =
        document.documentElement.scrollTop || document.body.scrollTop;

      if (!this.showReturnTop && windowScrollTop > 400) {
        this.downloadFixedShow = true;
        this.showReturnTop = true;
      } else if (this.showReturnTop && windowScrollTop <= 400) {
        this.showReturnTop = false;
        this.downloadFixedShow = false;
      }
    },
    handleReturnTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    goToGame() {
      BOX_goToGame(
        {
          params: {
            id: this.config.game_id,
          },
        },
        { id: this.config.game_id },
      );
    },
    videoInit() {
      const oVideo = document.getElementById('video');
      // 视频播放暂停切换
      oVideo.addEventListener('play', () => {
        this.videoPlaying = true;
      });

      oVideo.addEventListener('ended', () => {
        this.videoPlaying = false;
      });
    },
    playVideo() {
      const oVideo = document.getElementById('video');
      if (this.videoPlaying) {
        oVideo.pause();
      } else {
        oVideo.play();
      }
    },
    formatDate(val) {
      let { year, month, day, time } = this.$handleTimestamp(val);
      return `${month}月${day}日 ${time}`;
    },
  },
};
</script>

<style lang="less" scoped>
.font-songti {
  font-family: '微软雅黑,宋体';
}
.exclusive-activity-page {
  background-color: #0e0e0e;
  .placeholder {
    content: '';
    position: relative;
    display: block;
    width: 100%;
    height: calc(80 * @rem + @safeAreaTop);
    height: calc(80 * @rem + @safeAreaTopEnv);
    background: #000;
  }
  .return-top {
    position: fixed;
    right: 5 * @rem;
    bottom: 200 * @rem;
    width: 50 * @rem;
    height: 50 * @rem;
    background: url(~@/assets/images/exclusive-activity/return-top.png) center
      center no-repeat;
    background-size: 50 * @rem 50 * @rem;
    z-index: 100;
    &.az {
      bottom: 100 * @rem;
    }
  }
  .main {
    background-color: #0e0e0e;
    padding-bottom: 100 * @rem;
    position: relative;
    min-height: 100vh;
    .fixed-btns {
      position: fixed;
      right: 0;
      // top: 200 * @rem;
      // top: calc(164 * @rem + @safeAreaTop);
      // top: calc(164 * @rem + @safeAreaTopEnv);
      top: 30%;
      width: 26 * @rem;
      z-index: 9;
      .fixed-btn {
        width: 26 * @rem;
        height: 62 * @rem;
        background-size: 26 * @rem 62 * @rem;
        background-position: center center;
        background-repeat: no-repeat;
        &:not(:first-of-type) {
          margin-top: 10 * @rem;
        }
      }
    }
    .top-bg {
      position: relative;
      width: 100%;
      .top-bg-img {
        width: 100%;
        object-fit: cover;
      }
      .top-bg-linear {
        width: 100%;
        height: 70 * @rem;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: -1 * @rem;
        z-index: 2;
        background-image: linear-gradient(180deg, rgba(0, 0, 0, 0), #0e0e0e);
      }
      .download-btn {
        position: absolute;
        height: 44 * @rem;
        width: auto;
        bottom: -5 * @rem;
        left: 50%;
        transform: translateX(-50%);
        z-index: 3;
      }
    }

    .broadcast {
      box-sizing: border-box;
      width: 253 * @rem;
      height: 29 * @rem;
      position: relative;
      z-index: 2;
      display: flex;
      margin: 36 * @rem auto 0;
      background-position: center center;
      background-repeat: no-repeat;
      background-size: 253 * @rem 29 * @rem;
      overflow: hidden;
      .broadcast-icon {
        width: 14 * @rem;
        height: 14 * @rem;
        background-position: center center;
        background-repeat: no-repeat;
        background-size: 14 * @rem 14 * @rem;
        margin-left: 17 * @rem;
        margin-top: 10 * @rem;
      }
      .broadcast-swiper {
        flex: 1;
        height: 25 * @rem;
        margin: 5 * @rem 20 * @rem 0 10 * @rem;
        .broadcast-item {
          height: 25 * @rem;
          line-height: 25 * @rem;
          font-size: 10 * @rem;
          color: #ffffff;
          text-align: center;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }

    .section-container {
      margin: 30 * @rem auto 0;
      .section-title {
        height: 19 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          object-fit: contain;
        }
      }
      .raffle-content {
        background-color: rgba(217, 217, 217, 0.07);
        width: 342 * @rem;
        margin: 16 * @rem auto 0;
        min-height: 300 * @rem;
        border-radius: 2 * @rem;
        .table-container {
          box-sizing: border-box;
          width: 100%;
          margin: 16 * @rem auto 0;
          height: 339 * @rem;
          width: 323 * @rem;
          padding: 16 * @rem 0;

          .table-content {
            box-sizing: border-box;
            height: 100% * @rem;

            .table-list {
              position: relative;
              height: 100% * @rem;

              .table-item {
                position: absolute;
                width: 98 * @rem;
                height: 98 * @rem;
                .image-bg('~@/assets/images/exclusive-activity/turntable-item.png');

                .reward-icon {
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 100%;
                  height: 100%;

                  .reward-img {
                    width: 100%;
                    height: 100%;
                  }
                }

                .reward-text {
                  box-sizing: border-box;
                  position: absolute;
                  left: 0;
                  top: 67 * @rem;
                  width: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  font-size: 11 * @rem;
                  line-height: 13 * @rem;
                  color: rgba(215, 202, 156, 1);
                  font-weight: 500;
                  text-align: center;
                  padding: 0 5 * @rem;
                  max-height: 26 * @rem;
                  overflow: hidden;
                }

                &:nth-of-type(1) {
                  left: 0;
                  top: 0;
                }

                &:nth-of-type(2) {
                  left: 114 * @rem;
                  top: 0;
                }

                &:nth-of-type(3) {
                  left: 228 * @rem;
                  top: 0;
                }

                &:nth-of-type(4) {
                  left: 228 * @rem;
                  top: 114 * @rem;
                }

                &:nth-of-type(5) {
                  left: 228 * @rem;
                  top: 228 * @rem;
                }

                &:nth-of-type(6) {
                  left: 114 * @rem;
                  top: 228 * @rem;
                }

                &:nth-of-type(7) {
                  left: 0;
                  top: 228 * @rem;
                }

                &:nth-of-type(8) {
                  left: 0;
                  top: 114 * @rem;
                }

                &.turn-btn-1 {
                  left: 114 * @rem;
                  top: 114 * @rem;
                  width: 98 * @rem;
                  height: 98 * @rem;
                  overflow: hidden;
                  .image-bg(
                      '~@/assets/images/exclusive-activity/turntable-btn.png'
                    );
                }

                &.on {
                  .image-bg('~@/assets/images/exclusive-activity/turntable-item-on.png');
                }
              }
            }
          }
        }
        .task-list {
          padding: 10 * @rem 14 * @rem 5 * @rem;
          .task-item {
            display: flex;
            align-items: center;
            height: 46 * @rem;
            &:not(:last-of-type) {
              border-bottom: 1 * @rem solid rgba(255, 255, 255, 0.05);
            }
            .task-title {
              text-align: left;
              margin-right: auto;
              font-size: 12 * @rem;
              line-height: 15 * @rem;
              color: rgba(212, 194, 129, 1);
              .font-songti;
              font-weight: 600;
              flex: 1;
              min-width: 0;
              margin-right: 10 * @rem;
              display: -webkit-box;
              overflow: hidden;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
            }
            .task-reward {
              font-size: 10 * @rem;
              color: rgba(129, 153, 212, 1);
              .font-songti;
              flex-shrink: 0;
            }
            .task-btn {
              flex-shrink: 0;
              width: 70 * @rem;
              height: 25 * @rem;
              background: url(~@/assets/images/exclusive-activity/task-btn.png)
                center center no-repeat;
              background-size: 70 * @rem 25 * @rem;
              margin-left: 10 * @rem;
              &.task-btn-done {
                background-image: url(~@/assets/images/exclusive-activity/task-btn-done.png);
              }
              &.task-btn-take {
                background-image: url(~@/assets/images/exclusive-activity/task-btn-take.png);
              }
            }
          }
        }
      }
      .raffle-tips {
        font-size: 11 * @rem;
        line-height: 14 * @rem;
        color: #fff;
        text-align: center;
        margin: 10 * @rem auto 0;
        span {
          text-decoration: underline;
          margin-left: 10 * @rem;
        }
      }
    }

    .section-container {
      .video-container {
        width: 330 * @rem;
        height: 186 * @rem;
        margin: 20 * @rem auto 0;
        border: 1px solid rgba(173, 156, 102, 1);
        border-radius: 8 * @rem;
        overflow: hidden;
        position: relative;
        .video {
          display: block;
          width: 100%;
          height: 100%;
          border: 0;
          outline: 0;
          background: #000;
        }
        .video-play {
          position: absolute;
          width: 50 * @rem;
          height: 50 * @rem;
          background: url(~@/assets/images/exclusive-activity/play-btn.png)
            center center no-repeat;
          background-size: 50 * @rem 50 * @rem;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
      }

      .pic-swiper {
        height: 290 * @rem;
        margin: 20 * @rem auto 0;
        position: relative;
        .swiper-container {
          height: 290 * @rem;
          position: relative;
          .swiper-pagination {
            position: absolute;
            bottom: 2 * @rem;
            /deep/ .swiper-pagination-bullet {
              width: 8 * @rem;
              height: 8 * @rem;
              background-color: rgba(217, 217, 217, 1);
              border-radius: 4 * @rem;
              transition: 0.2s;
              &.swiper-pagination-bullet-active {
                width: 16 * @rem;
                background-color: rgba(255, 255, 255, 1);
              }
            }
          }
        }
        .swiper-slide {
          width: 154 * @rem;
          height: 260 * @rem;
        }
        .pic-img {
          display: block;
          width: 154 * @rem;
          height: 260 * @rem;
          margin: 0 auto;
          border-radius: 4 * @rem;
          object-fit: cover;
        }
        .down-mask {
          width: 100%;
          height: 75 * @rem;
          position: fixed;
          bottom: calc(-25 * @rem + @safeAreaBottom);
          bottom: calc(-25 * @rem + @safeAreaBottomEnv);
          .fixed-center;
          background: rgba(0, 0, 0, 0.4);
          z-index: 2;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: 0.3s ease;
          opacity: 0;
          &.show {
            bottom: 0;
            opacity: 1;
          }
          &.home-show {
            bottom: calc(50 * @rem + @safeAreaBottom);
            bottom: calc(50 * @rem + @safeAreaBottomEnv);
            opacity: 1;
          }
          .down-btn {
            width: 192 * @rem;
            height: 44 * @rem;
            background-position: center center;
            background-repeat: no-repeat;
            background-size: 100% auto;
          }
        }
      }

      .welfare-list {
        box-sizing: border-box;
        width: 330 * @rem;
        background: rgba(217, 217, 217, 0.07);
        border: 1px solid rgba(217, 217, 217, 0.07);
        margin: 20 * @rem auto 0;
        padding: 30 * @rem 22 * @rem;
        border-radius: 2 * @rem;

        .rich-text {
          color: rgba(212, 194, 129, 1);
          font-size: 13 * @rem;
          line-height: 13 * @rem;
          /deep/ h3 {
            .font-songti;
            font-size: 16 * @rem;
            line-height: 18 * @rem;
            font-weight: bold;
            opacity: 1;
            &:not(:first-of-type) {
              margin-top: 20 * @rem;
            }
          }
          /deep/ p {
            .font-songti;
            font-size: 13 * @rem;
            opacity: 0.65;
            line-height: 16 * @rem;
            margin-top: 13 * @rem;
          }
        }
      }
    }

    .copy-right {
      font-size: 12 * @rem;
      color: rgba(252, 242, 216, 0.6);
      text-align: center;
      line-height: 15 * @rem;
      margin: 38 * @rem auto 0;
    }
  }

  .popup-container {
    background: transparent;
    .popup-main {
      box-sizing: border-box;
      background: #fff;
      width: 340 * @rem;
      border-radius: 12 * @rem;
      overflow: hidden;
      padding: 25 * @rem 0;
      &.small {
        width: 290 * @rem;
      }
      .popup-title {
        height: 24 * @rem;
        line-height: 24 * @rem;
        width: max-content;
        font-weight: bold;
        font-family: '宋体';
        font-size: 18 * @rem;
        color: rgba(102, 73, 61, 1);
        margin: 0 auto;
        text-align: center;
        position: relative;
        &::before {
          content: '';
          position: absolute;
          left: -80 * @rem;
          top: 50%;
          transform: translateY(-50%);
          width: 69 * @rem;
          height: 7 * @rem;
          background: url(~@/assets/images/exclusive-activity/title-left-bg.png)
            no-repeat;
          background-size: 69 * @rem 7 * @rem;
        }
        &::after {
          content: '';
          position: absolute;
          right: -80 * @rem;
          top: 50%;
          transform: translateY(-50%);
          width: 69 * @rem;
          height: 7 * @rem;
          background: url(~@/assets/images/exclusive-activity/title-right-bg.png)
            no-repeat;
          background-size: 69 * @rem 7 * @rem;
        }
      }
      .popup-content {
        .empty {
          padding-bottom: 20 * @rem;
        }
        .rule-content {
          font-size: 12 * @rem;
          padding: 0 26 * @rem 30 * @rem;
          margin-top: 20 * @rem;
          /deep/ h3 {
            font-size: 14 * @rem;
            color: rgba(102, 73, 61, 1);
            font-weight: bold;
            line-height: 18 * @rem;
            margin-top: 15 * @rem;
          }
          /deep/ p {
            font-size: 12 * @rem;
            color: rgba(102, 73, 61, 1);
            line-height: 15 * @rem;
            margin-top: 5 * @rem;
          }
        }

        .record-list {
          height: 300 * @rem;
          overflow-y: auto;
          padding: 0 26 * @rem;
          margin-top: 20 * @rem;
          .record-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 30 * @rem;

            .time {
              font-size: 12 * @rem;
              color: rgba(102, 73, 61, 1);
              font-weight: 500;
            }
            .reward {
              font-size: 12 * @rem;
              color: rgba(102, 73, 61, 1);
              font-weight: 600;
            }
          }
        }
        .lottery-result {
          padding: 0 0 15 * @rem;
          position: relative;
          .result-title {
            font-size: 20 * @rem;
            line-height: 20 * @rem;
            color: #222222;
            font-weight: bold;
            text-align: center;
          }
          .result-icon {
            width: 150 * @rem;
            height: 150 * @rem;
            display: block;
            margin: -10 * @rem auto 0;
          }
          .result-content {
            font-size: 14 * @rem;
            color: #666666;
            margin-top: -40 * @rem;
            line-height: 14 * @rem;
            text-align: center;
            font-weight: bold;
          }
          .result-tips {
            width: 100%;
            position: absolute;
            left: 0;
            bottom: -10 * @rem;
            color: #636b79;
            font-size: 11 * @rem;
            height: 15 * @rem;
            line-height: 15 * @rem;
            margin-top: 20 * @rem;
            text-align: center;
          }
        }

        .guide-copy {
          .guide-copy-title {
            color: #222222;
            font-size: 20 * @rem;
            text-align: center;
            line-height: 20 * @rem;
            font-weight: bold;
          }
          .guide-copy-content {
            box-sizing: border-box;
            padding: 0 40 * @rem;
            font-size: 14 * @rem;
            line-height: 20 * @rem;
            color: #666666;
            margin-top: 34 * @rem;
            text-align: center;
            padding-bottom: 17 * @rem;
          }
        }
      }
    }
    .popup-close {
      width: 190 * @rem;
      height: 40 * @rem;
      margin: 15 * @rem auto 0;
      font-size: 15 * @rem;
      color: #ffffff;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #e9753c;
      border-radius: 20 * @rem;
    }
  }
}
</style>
