<template>
  <rubber-band topColor="#ff8a62" bottomColor="#FFFFFF">
    <div class="gold-gamble-page">
      <nav-bar-2 bgStyle="transparent" :placeholder="false" :azShow="true">
        <template #right>
          <div class="nav-right">
            <!-- <div class="my-gamble nav-right-item btn" @click="toPage('MyGamble')">
            <div>{{ $t('我的') }}</div>
            <div>{{ $t('夺宝') }}</div>
          </div> -->
            <div
              class="gamble-rule nav-right-item btn"
              @click="toPage('GambleRule')"
            >
              {{ $t('规则') }}
            </div>
          </div>
        </template>
      </nav-bar-2>
      <div class="fixed-btns right-fixed">
        <div class="fixed-gamble-btn" @click="toPage('MyGamble')"
          >{{ $t('我的') }}<br />{{ $t('夺宝') }}</div
        >
      </div>
      <div class="main">
        <div class="top-banner">
          <img
            src="@/assets/images/welfare/gold-gamble/gamble-banner.png"
            alt=""
          />
        </div>
        <div class="gamble-container">
          <div
            class="nav-list nav-list-bg1"
            :class="{ 'nav-list-bg2': current == 1 }"
          >
            <div
              class="nav-item"
              :class="{ current: current == index }"
              v-for="(nav, index) in navList"
              :key="index"
              @click="tapNav(index)"
            >
              <!-- {{ nav.title }} -->
            </div>
          </div>
          <template v-if="gambleType == 1">
            <div class="gamble-tips gamble-tips-2">
              {{ $t('为确保活动公平性，夺宝前需完成实名认证和手机') }}<br />
              <span>
                {{ $t('绑定，部分游戏不适用代金券') }}
              </span>
              <span class="btn-look" @click="clickNoCouponGame"
                >{{ $t('点击查看') }}
              </span>
            </div>
          </template>
          <template v-else-if="gambleType == 2">
            <div class="gold-tips">
              <p>{{ $t('RMB与金币兑换比例为【1:100】') }}</p>
              <p>
                {{
                  $t('金币可用于游戏内充值使用(PS:仅限3733平台账号登录的游戏)')
                }}
              </p>
            </div>
            <div class="gamble-tips">
              {{ $t('为确保活动公平性，夺宝前需完成实名认证和手机绑定') }}
            </div>
          </template>
          <yy-list
            class="gamble-list-container"
            v-model="loadingObj"
            :finished="finished"
            @refresh="onRefresh"
            @loadMore="loadMore"
            :empty="empty"
            :check="false"
          >
            <div class="gamble-list">
              <div
                class="gamble-item"
                v-for="item in gambleList"
                :key="item.id"
                @click="start(item.id)"
              >
                <div class="gamble-icon">
                  <img :src="item.cover" alt="" />
                </div>
                <div class="gamble-info">
                  <div class="title">{{ item.title }}</div>
                  <div class="line-container">
                    <div
                      class="line"
                      :style="{
                        width: `${Math.floor(
                          (item.use_total / item.total) * 100,
                        )}%`,
                      }"
                    ></div>
                  </div>
                  <div class="progress">
                    {{ $t('开奖进度') }}
                    {{ Math.floor((item.use_total / item.total) * 100) }}%
                  </div>
                  <div class="gold">{{ item.gold }}{{ $t('金币') }}</div>
                </div>
                <div
                  class="start"
                  @click.stop="start(item.id)"
                  v-if="item.state == 1"
                >
                  {{ $t('立即夺宝') }}
                </div>
                <div class="start end" v-else-if="item.state == 2">
                  {{ $t('已售空') }}
                </div>
                <div class="start end" v-else>{{ $t('已结束') }}</div>
              </div>
            </div>
          </yy-list>
        </div>
      </div>
      <!-- 不可用代金券的游戏弹窗 -->
      <coupon-game-dialog :show.sync="noCouponPopupShow"></coupon-game-dialog>
    </div>
  </rubber-band>
</template>

<script>
import { ApiGoldDuobaoInfoList } from '@/api/views/goldGamble.js';
// import { ApiGameCardGetGameAjax } from "@/api/views/game.js";
import { platform } from '@/utils/box.uni.js';
export default {
  name: 'GoldGamble',
  data() {
    return {
      navList: [
        {
          title: this.$t('金币'),
          type: 2,
        },
        {
          title: this.$t('代金券'),
          type: 1,
        },
      ],
      current: 0,
      loadingObj: {
        loading: false,
        reloading: false,
      },
      finished: false,
      page: 1,
      listRows: 10,
      empty: false,
      gambleType: 2,
      gambleList: [],
      noCouponPopupShow: false,
    };
  },
  created() {
    if (platform == 'android') {
      document.title = this.$t('金币夺宝');
    }
  },
  async activated() {
    await this.getGambleList();
  },
  methods: {
    async tapNav(index) {
      if (this.current == index) return;
      this.current = index;
      this.gambleType = this.navList[index].type;
      this.gambleList = [];
      await this.getGambleList();
    },
    async clickNoCouponGame() {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      this.noCouponPopupShow = true;
      this.$toast.clear();
    },
    start(id) {
      this.toPage('GambleDetail', { id, over: 0 });
    },
    async getGambleList(action = 1) {
      if (action === 1) {
        this.finished = false;
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiGoldDuobaoInfoList({
        type: this.gambleType,
        page: this.page,
        listRows: this.listRows,
      });
      let { info_list } = res.data;
      if (action === 1 || this.page == 1) {
        this.gambleList = [];
        if (!info_list.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.gambleList.push(...info_list);
      if (info_list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished) {
          this.finished = false;
        }
      }
    },
    async onRefresh() {
      await this.getGambleList();
      this.loadingObj.reloading = false;
    },
    async loadMore() {
      await this.getGambleList(2);
    },
  },
};
</script>

<style lang="less" scoped>
.gold-gamble-page {
  background-color: #fff;
  min-height: 100vh;
  /deep/ .van-nav-bar__right:active {
    opacity: 1;
  }
  .nav-right {
    display: flex;
    align-items: center;
    .nav-right-item {
      width: 36 * @rem;
      height: 36 * @rem;
      line-height: 18 * @rem;
      background: rgba(0, 0, 0, 0.25);
      border-radius: 10 * @rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: 11 * @rem;
      color: #fff;
      box-sizing: border-box;
      padding: 0 7 * @rem;
      white-space: nowrap;
      div {
        width: 100%;
        height: 14 * @rem;
        line-height: 14 * @rem;
        text-align: center;
      }
      &:not(:last-of-type) {
        margin-right: 11 * @rem;
      }
    }
  }
  .fixed-btns {
    position: fixed;
    top: calc(50 * @rem + @safeAreaTop);
    top: calc(50 * @rem + @safeAreaTopEnv);
    padding-top: @safeAreaTop;
    padding-top: @safeAreaTopEnv;
    z-index: 999;
    padding: 0 16px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    &.right-fixed {
      right: 0;
    }
    .fixed-gamble-btn {
      box-sizing: border-box;
      padding: 0 7 * @rem;
      min-width: 36 * @rem;
      height: 36 * @rem;
      background: rgba(0, 0, 0, 0.25);
      border-radius: 10 * @rem;
      font-size: 11 * @rem;
      color: #ffffff;
      flex-wrap: wrap;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .main {
    min-height: 100vh;
    .top-banner {
      position: fixed;
      top: 0;
      .fixed-center;
      width: 100%;
      height: 280 * @rem;
      z-index: 1;
    }
    .gamble-container {
      box-sizing: border-box;
      padding-top: 293 * @rem;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      background: #ffffff;
      .nav-list {
        width: 100%;
        height: 48 * @rem;
        display: flex;
        border-radius: 20 * @rem 20 * @rem 0 0;
        background-color: #fff;
        position: fixed;
        &.nav-list-bg1 {
          background-image: url('~@/assets/images/welfare/gold-gamble/gamble-icon-1.png');
          background-size: 100% auto;
        }
        &.nav-list-bg2 {
          background-image: url('~@/assets/images/welfare/gold-gamble/gamble-icon-2.png');
          background-size: 100% auto;
        }
        .fixed-center;
        top: 239 * @rem;
        overflow: hidden;
        z-index: 1;
        .nav-item {
          width: 100%;
          height: 48 * @rem;
          // display: flex;
          // align-items: center;
          // justify-content: center;
          // background-color: #ebebeb;
          // font-weight: 400;
          // font-size: 16 * @rem;
          // color: #b6a091;
          // transition: 0.2s;
          // &.current {
          //   font-weight: bold;
          //   color: #622218;
          //   background-color: #fff;
          // }
        }
      }
      .gold-tips {
        margin: 0 auto 0;
        width: 351 * @rem;
        height: 44 * @rem;
        background: #ffedeb;
        border-radius: 8 * @rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        padding: 5 * @rem 0;
        p {
          font-size: 11 * @rem;
          color: #b24d2f;
          text-align: center;
          line-height: 17 * @rem;
        }
      }
      .gamble-tips {
        font-size: 12 * @rem;
        color: #93999f;
        text-align: center;
        padding-top: 10 * @rem;
        padding-bottom: 14 * @rem;
        line-height: 15 * @rem;
        box-sizing: border-box;
        &.gamble-tips-2 {
          padding-top: 0 * @rem;
          padding-bottom: 16 * @rem;
          span {
            margin-top: 6 * @rem;
          }
        }
        .btn-look {
          color: @themeColor;
          height: 15 * @rem;
          line-height: 15 * @rem;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          &::after {
            content: '';
            display: inline-block;
            width: 8 * @rem;
            height: 15 * @rem;
            background-image: url('~@/assets/images/welfare/gold-gamble/gamble-icon-3.png');
            background-size: 8 * @rem 100%;
            background-repeat: no-repeat;
          }
        }
      }
      .gamble-list-container {
        .gamble-list {
          padding: 0 12 * @rem;
          .gamble-item {
            display: flex;
            padding: 16 * @rem 14 * @rem 16 * @rem 12 * @rem;
            background: #fff7f0;
            border-radius: 12 * @rem;
            &:not(:first-of-type) {
              margin-top: 10 * @rem;
            }
            .gamble-icon {
              width: 52 * @rem;
              height: 52 * @rem;
            }
            .gamble-info {
              margin: 0 12 * @rem;
              flex: 1;
              min-width: 0;
              .title {
                font-size: 15 * @rem;
                font-weight: bold;
                color: #30343b;
                line-height: 21 * @rem;
              }
              .line-container {
                width: 154 * @rem;
                height: 3 * @rem;
                border-radius: 3 * @rem;
                background-color: #efeceb;
                margin-top: 8 * @rem;
                overflow: hidden;
                .line {
                  width: 0%;
                  height: 3 * @rem;
                  border-radius: 3 * @rem;
                  background-color: #fd6a53;
                }
              }
              .progress {
                font-size: 11 * @rem;
                color: #93999f;
                line-height: 16 * @rem;
                margin-top: 4 * @rem;
              }
              .gold {
                font-size: 13 * @rem;
                color: #fd6a53;
                line-height: 18 * @rem;
                margin-top: 8 * @rem;
              }
            }
            .start {
              width: 72 * @rem;
              height: 32 * @rem;
              background: linear-gradient(90deg, #ff5c27 0%, #ffa045 100%);
              border-radius: 20 * @rem;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12 * @rem;
              font-weight: 600;
              color: #ffffff;
              margin-top: 12 * @rem;
              &.end {
                background: #d8d1cf;
              }
            }
          }
        }
      }
    }
  }
}
.no-coupon-game-popup {
  box-sizing: border-box;
  width: 315 * @rem;
  border-radius: 16 * @rem;
  background-color: #fff;
  padding: 21 * @rem 16 * @rem 16 * @rem;
  .popup-title {
    text-align: center;
    font-size: 18 * @rem;
    font-weight: bold;
    line-height: 25 * @rem;
    color: #000000;
  }
  .popup-games {
    padding: 0 5 * @rem;
    font-size: 13 * @rem;
    color: #000000;
    line-height: 22 * @rem;
    margin-top: 10 * @rem;
    text-align: justify;
    max-height: 264 * @rem;
    min-height: 164 * @rem;
    overflow-y: auto;
  }
  .confirm-btn {
    width: 204 * @rem;
    height: 40 * @rem;
    background: @themeBg;
    border-radius: 20 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14 * @rem;
    font-weight: bold;
    color: #ffffff;
    margin: 15 * @rem auto 0;
  }
}
</style>
