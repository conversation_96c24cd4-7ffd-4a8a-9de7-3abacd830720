<template>
  <div class="page xiaohao-detail-page">
    <nav-bar-2 :border="true" :title="$t('角色详情')">
      <template #right>
        <div class="kefu-btn btn" @click="toPage('Kefu')"></div>
        <div class="share-btn btn" @click="handleShare"></div>
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="acount-info" v-if="xiaohaoInfo.game">
        <div
          class="top"
          @click="toPage('GameDetail', { id: xiaohaoInfo.game.id })"
        >
          <div class="game-icon">
            <img :src="xiaohaoInfo.game.titlepic" alt="" />
          </div>
          <div class="top-info">
            <div class="game-name">
              {{ xiaohaoInfo.game.main_title
              }}<span class="game-subtitle">{{
                xiaohaoInfo.game.subtitle
              }}</span>
            </div>
            <div class="platform">
              <div
                class="plat-icon"
                v-for="(plat, platIndex) in xiaohaoInfo.platforms"
                :key="platIndex"
              >
                <img :src="plat.icon" alt="" />
              </div>
            </div>
          </div>
          <div class="top-right">
            <div class="price">
              ¥<span>{{ Number(xiaohaoInfo.price).toFixed(1) }}</span>
            </div>
            <div
              class="record-btn btn"
              @click.stop="historyShow = true"
              v-if="trade_list.length || jl_list.length"
            >
              {{ $t('小号历史') }}
            </div>
          </div>
        </div>
        <div class="center">
          <div class="main-info" v-html="info_detail"></div>
          <div class="sub-info">
            <div class="sub-text" v-if="false">
              {{ $t('购号后可立返') }}{{ xiaohaoInfo.gold_num_normal
              }}<span v-if="!Number(xiaohaoInfo.specify_mem_id)"
                >（SVIP{{ xiaohaoInfo.gold_num_vip }}）</span
              >{{ $t('金币') }}
            </div>
            <div
              class="appoint-tag"
              v-if="!!Number(xiaohaoInfo.specify_mem_id)"
            >
              <div class="appoint-icon"></div>
              <div class="appoint-text">{{ $t('指定出售') }}</div>
            </div>
          </div>
          <div class="status">
            <img :src="xiaohaoInfo.status_info.image" alt="" />
          </div>
        </div>
      </div>
      <!-- 主要充值 -->
      <div class="servers-info" v-if="servers.length">
        <div class="section-title">
          <div class="title-text">
            {{ $t('主要充值')
            }}<span>{{ $t('（仅展示充值最多的3个区服）') }}</span>
          </div>
        </div>
        <div class="server-list">
          <div
            class="server-item"
            v-for="(item, index) in servers"
            :key="index"
          >
            <div class="server-id">{{ $t('区服ID') }}：{{ item.server }}</div>
            <div class="server-money">{{ item.suma }}</div>
          </div>
        </div>
        <div class="server-tips">
          {{
            $t('区服ID为所充值区服代码，购买后找不到区服右上角撩客服协助查询')
          }}
        </div>
      </div>
      <!-- 商品信息 -->
      <div class="container goods-info">
        <div class="section-title">
          <div class="title-text">{{ xiaohaoInfo.title }}</div>
        </div>
        <div class="content">{{ xiaohaoInfo.desc }}</div>
        <div class="video-container" v-if="xiaohaoInfo.video_url">
          <video
            ref="videoPlayer"
            id="video"
            :src="xiaohaoInfo.video_url"
            :controls="isPlaying"
            x5-playsinline=""
            x-webkit-airplay="true"
            x5-video-player-type="h5"
            x5-video-player-fullscreen=""
            x5-video-orientation="portraint"
            muted
          ></video>
          <div class="mask" v-show="!isPlaying">
            <div class="play-btn" @click="handlePlay"></div>
          </div>
        </div>
        <div class="img-list">
          <div
            class="img-item"
            v-for="(item, index) in xiaohaoInfo.images"
            :key="index"
          >
            <img
              :src="item"
              alt=""
              @click="showBigImage(xiaohaoInfo.images, index)"
            />
          </div>
          <div class="img-item" style="height: 0; margin-top: 0"></div>
        </div>
      </div>
      <!-- 游戏 -->
      <div class="container game-info" v-if="xiaohaoInfo.game">
        <game-item-2 :gameInfo="xiaohaoInfo.game"></game-item-2>
      </div>
      <!-- 砍价区 -->
      <!-- <div
        class="container bargain"
        v-if="Number(xiaohaoInfo.specify_mem_id) == 0"
      >
        <div class="section-title">
          <div class="title-icon"></div>
          <div class="title-text">{{ $t("砍价区") }}</div>
          <div
            class="more btn"
            @click="hanleBargainMore"
            v-if="bargainList.length"
          >
            <div class="more-text">{{ $t("更多") }}</div>
          </div>
        </div>
        <content-empty
          v-if="!bargainList.length"
          tips="还没有人砍价，快来砍价吧~"
        ></content-empty>
        <div class="bargain-list" v-else>
          <template v-for="(item, index) in bargainList">
            <bargain-item
              class="bargain-item"
              v-if="index < 3"
              :key="item.id"
              :info="item"
              :trade_status="xiaohaoInfo.status"
              @refresh="getXiaohaoInfo(tradeId)"
            ></bargain-item>
          </template>
        </div>
      </div> -->
      <!-- 相关商品 -->
      <div class="container related" v-if="relatedList.length">
        <div class="section-title">
          <div class="title-icon"></div>
          <div class="title-text">{{ $t('相关商品') }}</div>
          <div class="more btn" @click="hanleMore">
            <div class="more-text">{{ $t('更多') }}</div>
          </div>
        </div>
        <div class="related-list">
          <template v-for="(item, index) in relatedList">
            <deal-item
              class="related-item"
              v-if="index < 3"
              :key="item.id"
              :info="item"
            ></deal-item>
          </template>
        </div>
      </div>
    </div>
    <div class="bottom-bar fixed-center" v-if="xiaohaoInfo.status_info">
      <div class="collect" @click="setCollectStatus" v-if="!isSelf">
        <div class="collect-icon btn" :class="{ had: collected == 1 }"></div>
        <div class="collect-text">{{ $t('收藏') }}</div>
      </div>
      <div
        class="bargain"
        @click="clickBargain"
        v-if="
          Number(xiaohaoInfo.specify_mem_id) == 0 && !isSelf && !statusIn([7])
        "
      >
        <!-- <div class="bargain-icon btn"></div> -->
        <!-- <div class="bargain-text">{{ $t("砍价") }}</div> -->
      </div>
      <div class="operate" v-if="isSelf && statusIn([2, 3, 4])">
        <div
          class="publish-btn btn"
          @click="handlePublish"
          v-if="statusIn([2, 4])"
        >
          {{ $t('上架') }}
        </div>
        <div
          class="publish-btn btn"
          @click="handleUnpublish"
          v-if="statusIn([3])"
        >
          {{ $t('下架') }}
        </div>
        <div class="cancel-btn btn" @click="handleCancel">
          {{ $t('取消出售') }}
        </div>
      </div>
      <div
        class="deal-btn btn"
        v-else
        :style="{ background: dealStatusInfo.buttonColor }"
        @click="handleDeal"
      >
        {{ dealStatusInfo.buttonText }}
      </div>
    </div>
    <!-- 小号历史弹窗 -->
    <van-dialog
      v-model="historyShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
    >
      <div class="history-container">
        <div class="close" @click="historyShow = false"></div>
        <div class="title">{{ $t('小号历史') }}</div>
        <content-empty
          v-if="historyEmpty"
          :tips="$t('暂无小号历史记录')"
        ></content-empty>
        <div class="cates" v-else>
          <div class="cate" v-if="trade_list.length">
            <div class="cate-title">{{ $t('近期交易成交历史') }}</div>
            <div class="hao-list">
              <div
                class="hao-item"
                v-for="(item, index) in trade_list"
                :key="index"
              >
                <div class="avatar">
                  <img :src="item.avatar" v-if="item.avatar" />
                  <img
                    src="@/assets/images/avatar/img_user_default.png"
                    v-else
                  />
                </div>
                <div class="nickname">{{ item.nickname }}</div>
                <div class="rmb">¥{{ Number(item.rmb).toFixed(1) }}</div>
                <div class="date">{{ item.update_time | formatTime }}</div>
              </div>
            </div>
          </div>
          <div class="cate" v-if="jl_list.length">
            <div class="cate-title">{{ $t('近期捡漏成交历史') }}</div>
            <div class="hao-list">
              <div
                class="hao-item"
                v-for="(item, index) in jl_list"
                :key="index"
              >
                <div class="avatar">
                  <img :src="item.avatar" v-if="item.avatar" />
                  <img
                    src="@/assets/images/avatar/img_user_default.png"
                    v-else
                  />
                </div>
                <div class="nickname">{{ item.nickname }}</div>
                <div class="rmb">¥{{ Number(item.rmb).toFixed(1) }}</div>
                <div class="date">{{ item.update_time | formatTime }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-dialog>
    <!-- 买家须知 -->
    <van-dialog
      v-model="buyTipShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :closeOnClickOverlay="true"
    >
      <div class="buy-tip-container">
        <div class="title">{{ $t('买家须知') }}</div>
        <div class="content">
          <div class="section-title">{{ $t('购买流程') }}</div>
          <img class="banner" src="@/assets/images/deal/buy-tip-banner-2.png" />
          <div class="content-text">
            <p>{{ $t('1、角色信息已通过官方核验。') }}</p>
            <p>
              {{
                $t(
                  '2、购买后角色直接转入您的账号，登录游戏【选择小号】处即可查收角色。',
                )
              }}
            </p>
            <p>
              {{
                $t(
                  '3、因时间因素造成的角色数据变化（如排行榜，称号，装备到期等）不视为信息失实，买家需理解并接受。',
                )
              }}
            </p>
            <p>
              4、<span
                >{{ $t('该账号仅限') }}{{ platformText
                }}{{ $t('平台可玩，购买后不可退货！') }}</span
              >
            </p>
            <p>
              {{
                $t(
                  '5、交易过程仅限小号转移，不涉及账号交易或换绑操作，无需担心购买的号被找回。',
                )
              }}
            </p>
          </div>
          <div class="agree" @click="buyTipAgree = !buyTipAgree">
            <div class="agree-btn" :class="{ yes: buyTipAgree }"></div>
            <div class="agree-text">{{ $t('我已认真阅读买家须知') }}</div>
          </div>
          <div class="confirm-btn btn" @click="handleBuy">
            {{ $t('我知道了') }}
          </div>
        </div>
      </div>
    </van-dialog>
    <bargain-popup
      :tradeId="tradeId"
      :isShow.sync="bargainPopupShow"
      @success="getXiaohaoInfo(tradeId)"
    ></bargain-popup>
  </div>
</template>

<script>
import {
  ApiXiaohaoTradeItem,
  ApiXiaohaoTradeList,
  ApiXiaohaoTradeHistory,
  ApiXiaohaoChangeTradeStatus,
  ApiXiaohaoCreateOrder,
  ApiXiaohaoCheckBargain,
} from '@/api/views/xiaohao.js';
import {
  ApiResourceCollect,
  ApiResourceCollectStatus,
} from '@/api/views/game.js';

import { ImagePreview } from 'vant';
import { handleTimestamp } from '@/utils/datetime.js';
import bargainItem from '../components/bargain-item/index.vue';
import bargainPopup from '../components/bargain-popup/index.vue';
export default {
  name: 'XiaohaoDetail',
  components: {
    bargainItem,
    bargainPopup,
  },
  data() {
    return {
      xiaohaoInfo: {},
      tradeId: 0,
      servers: [],
      info_detail: '', // html内容文案
      relatedList: [], // 相关商品
      collected: 0, // 是否已收藏
      historyShow: false,
      trade_list: [],
      jl_list: [],
      historyEmpty: false,
      buyTipShow: false, //买家须知
      buyTipAgree: false,
      isPlaying: false, //是否正在播放
      bargainList: [], // 砍价列表
      bargainPopupShow: false,
    };
  },
  computed: {
    notSellToMe() {
      return (
        this.userInfo.user_id &&
        this.xiaohaoInfo.specify_mem_id > 0 &&
        this.userInfo.user_id != this.xiaohaoInfo.specify_mem_id
      );
    },
    dealStatusInfo() {
      let buttonText = '',
        buttonColor = '';
      switch (Number(this.xiaohaoInfo.status)) {
        case 0:
          buttonText = this.$t('小号正在审核中');
          buttonColor = '#FE6600';
          break;
        case 1:
          // 审核未通过
          buttonText = this.$t('取消出售');
          buttonColor = '#a0a0a0';
          break;
        case 2:
          buttonText = this.$t('审核已通过');
          break;
        case 3:
          if (this.notSellToMe) {
            buttonText = this.$t('仅出售给指定玩家');
            buttonColor = '#cccccc';
          } else {
            buttonText = this.$t('立即购买');
            buttonColor = '#FE6600';
          }
          break;
        case 4:
          buttonText = this.$t('已下架');
          break;
        case 5:
          // 已取消
          buttonText = this.$t('删除');
          buttonColor = '#cccccc';

          break;
        case 6:
          buttonText = this.$t('小号正在交易中');
          buttonColor = '#FE6600';
          break;
        case 7:
          buttonText = this.$t('角色已卖出');
          buttonColor = '#cccccc';
          break;
        case 9:
          buttonText = this.$t('小号正在审核中');
          buttonColor = '#FE6600';
          break;
        default:
          break;
      }
      return {
        buttonText,
        buttonColor,
      };
    },
    platformText() {
      if (this.xiaohaoInfo.platforms) {
        let platformArr = this.xiaohaoInfo.platforms.map(item => {
          return item.name;
        });
        return platformArr.join('/');
      }
    },
    isSelf() {
      if (
        this.userInfo.user_id &&
        this.userInfo.user_id == this.xiaohaoInfo.mem_id
      ) {
        return true;
      }
      return false;
    },
  },
  filters: {
    formatTime(val) {
      let { year, date, time, second } = handleTimestamp(val);
      return `${year}-${date} ${time}:${second}`;
    },
  },
  async created() {
    this.tradeId = this.$route.params.id;
    await this.getXiaohaoInfo(this.tradeId);
    await this.getRelated(this.tradeId);
    await this.getCollectStatus();
    await this.getHistory();
  },
  mounted() {},
  methods: {
    hanleMore() {
      this.toPage('Deal', { info: this.xiaohaoInfo.game });
    },
    hanleBargainMore() {
      this.toPage('BargainList', { id: this.xiaohaoInfo.id });
    },
    handlePublish() {
      this.$dialog
        .confirm({
          message: this.$t('确认上架？'),
          lockScroll: false,
        })
        .then(() => {
          this.changeTradeStatus(3);
        });
    },
    handleUnpublish() {
      this.$dialog
        .confirm({
          message: this.$t(
            '下架后，您可以重新上架。若要将小号转回您的账号，请在下架后取消出售',
          ),
          lockScroll: false,
        })
        .then(() => {
          this.changeTradeStatus(4);
        });
    },
    // 取消出售
    handleCancel() {
      this.$dialog
        .confirm({
          message: this.$t(
            '取消出售后，小号将转回您的账号。如登录游戏未见小号，请退出账号重新登录。',
          ),
          lockScroll: false,
        })
        .then(() => {
          this.changeTradeStatus(5);
        });
    },
    async changeTradeStatus(status) {
      try {
        this.$toast({
          type: 'loading',
          duration: 0,
          message: this.$t('加载中...'),
        });
        // status   3=上架，4=下架，5=取消，100 = 删除
        const res = await ApiXiaohaoChangeTradeStatus({
          status: status,
          tradeId: this.xiaohaoInfo.id,
        });
      } finally {
        this.getXiaohaoInfo(this.tradeId);
        this.$toast.clear();
      }
    },
    // 底下按钮逻辑
    handleDeal() {
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }    
      switch (Number(this.xiaohaoInfo.status)) {
        case 3:
          if (this.notSellToMe) {
            return false;
          }
          // 立即购买
          this.buyTipShow = true;
          break;
        case 5:
          // 删除
          this.$dialog
            .confirm({
              message: this.$t('删除后无法恢复，确认删除？'),
              lockScroll: false,
            })
            .then(() => {
              this.changeTradeStatus(100);
              this.toPage('MySellList', {}, 1);
            });
          break;
        case 1:
          // 取消出售
          this.handleCancel();
          break;
        default:
          break;
      }
    },
    // 买家须知按钮逻辑
    async handleBuy() {
      if (!this.buyTipAgree) {
        this.$toast(this.$t('请认真阅读买家须知，并勾选'));
        return false;
      }
      this.buyTipShow = false;    
      let orderInfo = await this.createOrder();
      //跳转收银台
      this.toPage('XiaohaoOrderRecharge', {
        info: orderInfo,
        xhInfo: this.xiaohaoInfo,
        back: 'MyBuyList',
      });
    },
    async createOrder() {
      const res = await ApiXiaohaoCreateOrder({
        ssId: this.xiaohaoInfo.ss_id,
        price: this.xiaohaoInfo.price,
      });
      return res.data;
    },
    async getHistory() {
      const res = await ApiXiaohaoTradeHistory({
        xh_id: this.xiaohaoInfo.xh_id,
      });
      this.trade_list = res.data.trade_list;
      this.jl_list = res.data.jl_list;
      if (!this.trade_list.length && !this.jl_list.length) {
        this.historyEmpty = true;
      }
    },
    async getXiaohaoInfo(tradeId) {
      const res = await ApiXiaohaoTradeItem({ tradeId });
      this.xiaohaoInfo = res.data.trade;
      if (res.data.servers) {
        this.servers = res.data.servers;
      }
      if (res.data.bargain_list) {
        this.bargainList = res.data.bargain_list;
      }
      this.info_detail = res.data.info_detail;
    },
    async getRelated(tradeId) {
      const res = await ApiXiaohaoTradeList({
        tradeId,
        gameId: this.xiaohaoInfo.game.id,
      });
      this.relatedList = res.data.list;
    },
    // 截图查看大图
    showBigImage(list, index) {
      ImagePreview({
        images: list,
        startPosition: index,
        lockScroll: false,
      });
    },
    // 点击砍价按钮
    async clickBargain() {
      const res = await ApiXiaohaoCheckBargain({
        tradeId: this.tradeId,
      });
      if (res.code > 0) {
        this.bargainPopupShow = true;
      }
    },
    async getCollectStatus() {
      const res = await ApiResourceCollectStatus({
        classId: 5,
        sourceId: this.tradeId,
      });
      this.collected = res.data.collection_status == 1 ? true : false;
    },
    setCollectStatus() {
      let status;
      if (this.collected == 1) {
        status = -1;
      } else {
        status = 1;
      }
      ApiResourceCollect({
        classId: 5,
        sourceId: this.tradeId,
        status: status,
      })
        .then(res => {
          this.collected = res.data.status;
        })
        .catch(e => {});
    },
    statusIn(statusArr) {
      let index = statusArr.findIndex(item => {
        return Number(this.xiaohaoInfo.status) == item;
      });
      return index == -1 ? false : true;
    },
    handlePlay() {
      this.$refs.videoPlayer.play();
      this.isPlaying = true;
    },
    handlePause() {
      this.$refs.videoPlayer.pause();
      this.isPlaying = false;
    },
    handleShare() {
      let href = window.location.href;
      this.$copyText(href).then(
        res => {
          this.$toast(this.$t('本页链接复制成功，快去粘贴给好友吧~'));
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.xiaohao-detail-page {
  background-color: #f6f6f6;
  .kefu-btn {
    width: 22 * @rem;
    height: 22 * @rem;
    padding: 10 * @rem 0;
    background: url(~@/assets/images/deal/deal-kefu.png) right center no-repeat;
    background-size: 22 * @rem 22 * @rem;
  }
  .share-btn {
    width: 22 * @rem;
    height: 22 * @rem;
    padding: 10 * @rem 0;
    background: url(~@/assets/images/share-icon.png) right center no-repeat;
    background-size: 22 * @rem 22 * @rem;
    margin-left: 15 * @rem;
  }
  .main {
    box-sizing: border-box;
    background-color: #f6f6f6;
    padding-bottom: calc(80 * @rem + @safeAreaBottom);
    padding-bottom: calc(80 * @rem + @safeAreaBottomEnv);
    .container {
      box-sizing: border-box;
      margin: 10 * @rem auto 0;
      background: #ffffff;
      padding: 15 * @rem 18 * @rem;
    }
    .section-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title-icon {
        width: 22 * @rem;
        height: 22 * @rem;
        background: url(~@/assets/images/welfare/section-title-icon.png) center
          center no-repeat;
        background-size: 22 * @rem 22 * @rem;
        margin-right: 5 * @rem;
      }
      .title-text {
        font-size: 18 * @rem;
        color: #000000;
        font-weight: bold;
        flex: 1;
        min-width: 0;
        span {
          font-size: 14 * @rem;
          color: #9a9a9a;
          font-weight: normal;
        }
      }
      .more {
        display: flex;
        align-items: center;
        .more-text {
          font-size: 14 * @rem;
          color: #999999;
        }
        .more-icon {
          width: 14 * @rem;
          height: 14 * @rem;
          background: url(~@/assets/images/right-icon.png) right center
            no-repeat;
          background-size: 6 * @rem 10 * @rem;
        }
      }
    }
    .bargain {
      .section-title {
        .title-icon {
          background-image: url(~@/assets/images/deal/section-title-icon-bargain.png);
        }
      }
      .bargain-list {
        background-color: #fff;
        margin-top: 10 * @rem;
      }
    }
    .acount-info {
      background-color: #ffffff;
      padding: 14 * @rem 18 * @rem;
      .top {
        display: flex;
        align-items: center;
        .game-icon {
          width: 60 * @rem;
          height: 60 * @rem;
          border-radius: 8 * @rem;
          background-color: #bfbfbf;
          overflow: hidden;
        }
        .top-info {
          flex: 1;
          min-width: 0;
          margin-left: 12 * @rem;
          .game-name {
            font-size: 18 * @rem;
            color: #383838;
            line-height: 22 * @rem;
            font-weight: 600;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            display: flex;
            align-items: center;
            .game-subtitle {
              box-sizing: border-box;
              border: 1 * @rem solid fade(@themeColor, 80);
              border-radius: 3 * @rem;
              font-size: 11 * @rem;
              padding: 2 * @rem 3 * @rem;
              color: @themeColor;
              margin-left: 5 * @rem;
              vertical-align: middle;
              line-height: 1;
            }
          }
          .platform {
            display: flex;
            align-items: center;
            margin-top: 11 * @rem;
            .plat-icon {
              width: 16 * @rem;
              height: 16 * @rem;
              margin-right: 6 * @rem;
            }
          }
        }
        .top-right {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          .price {
            font-size: 12 * @rem;
            font-weight: 600;
            color: @themeColor;
            span {
              font-size: 24 * @rem;
              color: @themeColor;
              font-weight: 600;
            }
          }
          .gold {
            font-size: 11 * @rem;
            color: #ff395e;
            background-color: #ff395d10;
            border-radius: 10 * @rem;
            height: 20 * @rem;
            padding: 0 7 * @rem;
            display: flex;
            align-items: center;
            margin: 0 6 * @rem;
            white-space: nowrap;
            overflow: hidden;
          }
          .record-btn {
            box-sizing: border-box;
            height: 22 * @rem;
            border: 0.5 * @rem solid #c1c1c1;
            border-radius: 11 * @rem;
            padding: 0 7 * @rem;
            font-size: 10 * @rem;
            color: #797979;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 5 * @rem;
          }
        }
      }
      .center {
        margin-top: 8 * @rem;
        position: relative;
        .main-info {
          font-size: 13 * @rem;
          color: #000000;
          line-height: 25 * @rem;
        }
        .sub-info {
          font-size: 13 * @rem;
          color: @themeColor;
          line-height: 25 * @rem;
          display: flex;
          align-items: center;
          .sub-text {
            margin-right: 6 * @rem;
          }
          .appoint-tag {
            display: flex;
            align-items: center;
            height: 18 * @rem;
            background-color: #ffefd8;
            border-radius: 9 * @rem;
            padding: 0 5 * @rem;
            .appoint-icon {
              width: 12 * @rem;
              height: 12 * @rem;
              .image-bg('~@/assets/images/deal/ic_finger.png');
              margin-right: 2 * @rem;
            }
            .appoint-text {
              font-size: 10 * @rem;
              color: #f95725;
            }
          }
        }
        .status {
          width: 76 * @rem;
          height: 76 * @rem;
          position: absolute;
          right: 0;
          top: 10 * @rem;
        }
      }
    }
    .servers-info {
      box-sizing: border-box;
      margin: 10 * @rem auto 0;
      background: #ffffff;
      padding: 15 * @rem 18 * @rem;
      .server-list {
        margin-top: 2 * @rem;
        .server-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 37 * @rem;
          &:not(:last-of-type) {
            border-bottom: 0.5 * @rem solid #f3f3f8;
          }
          .server-id {
            font-size: 13 * @rem;
            color: #000000;
          }
          .server-money {
            font-size: 13 * @rem;
            color: @themeColor;
          }
        }
      }
      .server-tips {
        font-size: 11 * @rem;
        color: #999999;
        line-height: 18 * @rem;
      }
    }
    .goods-info {
      .content {
        font-size: 13 * @rem;
        color: #797979;
        line-height: 18 * @rem;
        margin-top: 8 * @rem;
      }
      .video-container {
        width: 100%;
        height: 160 * @rem;
        background-color: #000;
        border-radius: 4 * @rem;
        overflow: hidden;

        margin-top: 12 * @rem;
        position: relative;
        #video {
          width: 320 * @rem;
          height: 160 * @rem;
          margin: 0 auto;
        }
        .mask {
          width: 100%;
          height: 160 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          left: 0;
          top: 0;
          .fixed-center;
          background-color: rgba(0, 0, 0, 0.35);
          z-index: 10;
          .play-btn {
            width: 55 * @rem;
            height: 55 * @rem;
            background: url(~@/assets/images/video-play.png) no-repeat;
            background-size: 55 * @rem 55 * @rem;
          }
        }
      }
      .img-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        margin-top: 12 * @rem;
        .img-item {
          width: 110 * @rem;
          height: 66 * @rem;
          border-radius: 4 * @rem;
          background-color: #ffffff;
          overflow: hidden;
          margin-top: 10 * @rem;
          &:nth-of-type(-n + 3) {
            margin-top: 0;
          }
          img {
            object-fit: cover;
          }
        }
      }
    }
    .game-info {
      box-sizing: border-box;
      width: 347 * @rem;
      margin: 10 * @rem auto 0;
      background: #ffffff;
      border-radius: 12 * @rem;
      padding: 5 * @rem 12 * @rem;
    }
    .related {
      padding-bottom: 0;
      .related-list {
        .related-item {
          border-top: 0.5 * @rem solid #ebebeb;
          padding: 17 * @rem 0;
          &:nth-of-type(1) {
            border-top: 0;
          }
          .top {
            display: flex;
            align-items: center;
            .game-icon {
              width: 25 * @rem;
              height: 25 * @rem;
              border-radius: 6 * @rem;
              background-color: #bfbfbf;
              overflow: hidden;
            }
            .game-name {
              flex: 1;
              min-width: 0;
              font-size: 16 * @rem;
              color: #333333;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              margin-left: 7 * @rem;
            }
            .platform {
              display: flex;
              align-items: center;
              .plat-icon {
                width: 17 * @rem;
                height: 17 * @rem;
                margin-left: 6 * @rem;
              }
            }
          }
          .content {
            display: flex;
            margin-top: 10 * @rem;
            .left {
              flex: 1;
              min-width: 0;
              display: flex;
              flex-direction: column;
              justify-content: center;
              .server {
                font-size: 13 * @rem;
                color: #666666;
              }
              .desc {
                font-size: 12 * @rem;
                color: #999999;
                margin-top: 10 * @rem;
                span {
                  color: #ff9834;
                }
              }
            }
            .right {
              width: 90 * @rem;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: flex-end;
              .price {
                font-size: 18 * @rem;
                color: #ee1d44;
                font-weight: bold;
              }
              .gold {
                font-size: 10 * @rem;
                color: #ff395e;
                height: 16 * @rem;
                padding: 0 5 * @rem;
                display: flex;
                align-items: center;
                border-radius: 3 * @rem;
                background-color: #ff395d10;
                margin-top: 5 * @rem;
              }
            }
          }
        }
      }
    }
  }

  .bottom-bar {
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    width: 100%;
    height: calc(65 * @rem + @safeAreaBottom);
    height: calc(65 * @rem + @safeAreaBottomEnv);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15 * @rem;
    padding-bottom: @safeAreaBottom;
    padding-bottom: @safeAreaBottomEnv;
    background-color: #fff;
    .collect {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-right: 15 * @rem;
      .collect-icon {
        width: 22 * @rem;
        height: 22 * @rem;
        background: url(~@/assets/images/deal/collect.png) center center
          no-repeat;
        background-size: 22 * @rem 22 * @rem;
        &.had {
          background-image: url(~@/assets/images/deal/collect-success.png);
        }
      }
      .collect-text {
        font-size: 13 * @rem;
        color: #000000;
        line-height: 18 * @rem;
        margin-top: 2 * @rem;
      }
    }
    .bargain {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-right: 15 * @rem;
      .bargain-icon {
        width: 22 * @rem;
        height: 22 * @rem;
        background: url(~@/assets/images/deal/bargain-icon.png) center center
          no-repeat;
        background-size: 22 * @rem 22 * @rem;
      }
      .bargain-text {
        font-size: 13 * @rem;
        color: #000000;
        line-height: 18 * @rem;
        margin-top: 2 * @rem;
      }
    }
    .operate {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      .publish-btn {
        flex: 1;
        min-width: 0;
        border-radius: 8 * @rem;
        height: 45 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16 * @rem;
        color: #ffffff;
        background-color: @themeColor;
      }
      .cancel-btn {
        flex: 1;
        min-width: 0;
        border-radius: 8 * @rem;
        height: 45 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16 * @rem;
        color: #ffffff;
        background-color: #a0a0a0;
        margin-left: 10 * @rem;
      }
    }
    .deal-btn {
      flex: 1;
      min-width: 0;
      background: @themeBg;
      border-radius: 8 * @rem;
      height: 45 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16 * @rem;
      color: #ffffff;
    }
  }
}
.history-container {
  padding: 20 * @rem 13 * @rem;
  position: relative;
  .close {
    width: 18 * @rem;
    height: 18 * @rem;
    background: url(~@/assets/images/close-dialog.png) center center no-repeat;
    background-size: 18 * @rem 18 * @rem;
    padding: 16 * @rem;
    position: absolute;
    right: 0;
    top: 0;
  }
  .title {
    font-size: 20 * @rem;
    color: #000000;
    font-weight: bold;
    text-align: center;
  }
  .cates {
    .cate {
      padding: 12 * @rem 0;
      border-top: 1px solid #e5e5e5;
      &:nth-of-type(1) {
        border-top: 0;
      }
      .cate-title {
        font-size: 16 * @rem;
        color: #000000;
        padding: 8 * @rem 0;
      }
      .hao-list {
        .hao-item {
          display: flex;
          align-items: center;
          padding: 7 * @rem 0;
          .avatar {
            display: block;
            width: 24 * @rem;
            height: 24 * @rem;
            border-radius: 50%;
            overflow: hidden;
            img {
              object-fit: cover;
            }
          }
          .nickname {
            font-size: 14 * @rem;
            color: #333333;
            margin-left: 12 * @rem;
            flex: 1;
            min-width: 0;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .rmb {
            font-size: 14 * @rem;
            color: #ff2121;
            margin-left: 5 * @rem;
            width: 60 * @rem;
          }
          .date {
            font-size: 13 * @rem;
            color: #999999;
            margin-left: 5 * @rem;
          }
        }
      }
    }
  }
}
/deep/ .van-dialog {
  width: 315 * @rem;
}
.buy-tip-container {
  box-sizing: border-box;
  padding: 20 * @rem 18 * @rem;
  .title {
    font-size: 18 * @rem;
    color: #000000;
    font-weight: 600;
    text-align: center;
  }
  .content {
    margin-top: 15 * @rem;
    .banner {
      width: 281 * @rem;
      height: 106 * @rem;
      margin: 15 * @rem auto 0;
    }
    .content-text {
      margin-top: 15 * @rem;
      p {
        margin-top: 8 * @rem;
        font-size: 13 * @rem;
        color: #000000;
        line-height: 18 * @rem;
        span {
          color: @themeColor;
        }
      }
    }
    .agree {
      display: flex;
      align-items: center;
      margin-top: 25 * @rem;
      .agree-btn {
        width: 19 * @rem;
        height: 19 * @rem;
        background: url(~@/assets/images/deal/buy-tip-no.png) center center
          no-repeat;
        background-size: 12 * @rem 12 * @rem;
        &.yes {
          background-image: url(~@/assets/images/deal/buy-tip-yes.png);
        }
      }
      .agree-text {
        font-size: 13 * @rem;
        color: #757575;
        margin-left: 5 * @rem;
      }
    }
    .confirm-btn {
      width: 204 * @rem;
      height: 40 * @rem;
      margin: 15 * @rem auto 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14 * @rem;
      color: #ffffff;
      background: @themeBg;
      border-radius: 20 * @rem;
    }
  }
}
</style>
