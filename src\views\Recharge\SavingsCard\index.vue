<template>
  <div class="page savings-card-page">
    <rubber-band :topColor="'#ffffff'" :bottomColor="'#ffffff'">
      <nav-bar-2
        :border="false"
        :title="$t('省钱卡')"
        :placeholder="false"
        :bgStyle="bgStyle"
        :azShow="true"
        :bgColor="`rgba(255, 255, 255, ${navbarOpacity})`"
        :backShow="!hideBack"
      >
        <template #right>
          <div class="coin-tips black" @click="toPage('GoldCoinTips')">
            {{ $t('金币小贴士') }}
          </div>
        </template>
      </nav-bar-2>
      <div class="main">
        <div class="top-container">
          <div class="user-card">
            <div class="user-info">
              <user-avatar
                class="avatar"
                :src="pageInfo.user_info && pageInfo.user_info.avatar"
              ></user-avatar>
              <div class="user-right">
                <div class="line-1">
                  <div class="nickname">
                    {{ pageInfo.user_info && pageInfo.user_info.nickname }}
                  </div>
                  <div class="no-buy" v-if="!pageInfo.is_member">
                    {{ $t('暂未购买') }}
                  </div>
                  <div class="card-time" v-else>
                    {{ $t('有效期至') }}：{{ pageInfo.end_time }}
                  </div>
                </div>
                <div class="card-intro">{{ text_list.gold_welfare }}</div>
              </div>
            </div>
          </div>
          <div class="card-info">
            <div class="card-left">
              <div class="text">{{ $t('累计已省') }}（{{ $t('元') }}）</div>
              <div class="total-gold">{{ pageInfo.payTotal }}</div>
            </div>
            <div class="card-btn btn" v-if="!isSdk" @click="goToGoldCoinCenter">
              {{ $t('金币商城') }}
            </div>
          </div>
        </div>
        <div class="container">
          <template v-if="countdown && countdown.endTime && countdownPopup">
            <div class="countdown-container" @click.stop="">
              <img
                class="seckill-icon"
                src="@/assets/images/recharge/savings-card/seckill-icon.png"
                alt=""
              />
              <div class="countdown-text">倒计时</div>
              <div class="countdown-clock">
                <span>{{ countdownObj.day }}</span
                >天<span>{{ countdownObj.hour }}</span
                >时<span>{{ countdownObj.minute }}</span
                >分<span>{{ countdownObj.second }}</span>
              </div>
              <!-- <div class="countdown-close" @click="clickCloseCountdownPopup"></div> -->
            </div>
          </template>
          <div class="card-list">
            <div
              class="card-item"
              :class="{ current: item.type == selectedCard.type }"
              v-for="(item, index) in cardList"
              :key="index"
              @click="clickCard(item)"
            >
              <div v-if="item.tips_icon" class="tag">
                <img :src="item.tips_icon" />
              </div>
              <div class="card-left">
                <div class="card-title">{{ item.title }}</div>
                <div class="card-day">有效期：{{ item.day }}天</div>
              </div>
              <div class="card-center">
                <div class="title">{{ item.welfare }}</div>
                <div class="desc">{{ text_list.gold_day }}</div>
                <div class="desc">{{ item.total_gold_info.text }}</div>
              </div>
              <div class="card-right">
                <div class="money-now">
                  <span>{{ text_list.pay_symbol }}</span>
                  <div class="price">{{ item.amount }}</div>
                </div>
                <div class="money-old">{{ item.original_price_text }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="buy-fixed">
          <div class="buy-btn" @click="clickBuyBtn" v-if="!pageInfo.is_member">
            <div class="subscript">{{ $t('开通即回本') }}</div>
            {{ $t('立即购买') }}
          </div>
          <template v-else>
            <div
              class="buy-btn"
              v-if="pageInfo.is_receive == 1"
              @click="takeReward()"
            >
              {{ $t('领取金币') }}
            </div>
            <div
              class="buy-btn had"
              v-else-if="pageInfo.is_receive == 2"
              @click="takeReward(true)"
            >
              {{ $t('已领取') }}
            </div>
          </template>
        </div>
        <div class="explain-container">
          <div class="explain-title"><span>省钱卡说明</span></div>
          <div class="content" v-html="text_list.illustrate"></div>
        </div>
        <!-- 支付弹窗抽屉 -->
        <pay-type-popup
          :show.sync="payPopupShow"
          :list="payList"
          @choosePayType="choosePayType"
          :money="selectedCard.amount"
          :unit="text_list.pay_symbol"
        ></pay-type-popup>
      </div>
    </rubber-band>
  </div>
</template>

<script>
import {
  ApiGetPayUrl,
  ApiGetOrderStatus,
  ApiSavingsCardIndex,
  ApiSavingsCardCreateOrder,
  ApiSavingsCardTakeReceive,
  ApiGetPaymentMethod,
} from '@/api/views/recharge.js';
import {
  platform,
  BOX_openInNewNavWindow,
  BOX_openInNewNavWindowRefresh,
  BOX_showActivity,
  BOX_openInNewWindow,
  BOX_showActivityByAction,
  BOX_goToGame,
  isSdk,
  isIosSdk,
  BOX_setPayParams,
  isAndroidSdk,
} from '@/utils/box.uni.js';
import { getAdPopupData } from '@/utils/adPopup.js';
import { getQueryVariable } from '@/utils/function.js';
import { mapActions } from 'vuex';
export default {
  name: 'SavingsCard',
  data() {
    return {
      isSdk,
      isAndroidSdk,
      navbarOpacity: 0,
      bgStyle: 'transparent',
      hideBack: false, //是否隐藏返回按钮
      pageInfo: {},
      cardList: [],
      payPopupShow: false,
      selectedCard: {},
      selectedPayType: 'wx',
      payList: [],

      text_list: {}, // 一些带翻译的文案字段
      countdown: {}, // 显示倒计时对象
      timeClock: null, // 倒计时定时器
      countdownPopup: false, // 倒计时弹窗是否展示
    };
  },
  computed: {
    countdownObj() {
      if (this.countdown && this.countdown.endTime) {
        return this.formatTime(this.countdown.endTime - this.countdown.nowTime);
      } else {
        return {};
      }
    },
  },
  async created() {
    if (platform == 'android') {
      document.title = '省钱卡';
    }
    this.hideBack = !!getQueryVariable('hideBack');
    window.addEventListener('scroll', this.handleScroll);
  },
  async activated() {
    this.SET_USER_INFO(true);
    await this.getSavingsCardIndex();
  },
  mounted() {
    // 官包触发页面刷新（将onResume挂载在window上）
    if (platform == 'android') {
      window.onResume = this.onResume;
    }
    if (!isIosSdk) {
      this.getAdPopupData();
    }
  },
  deactivated() {
    // 清除定时器
    clearInterval(this.timeClock);
    this.timeClock = null;
  },
  beforeDestroy() {
    // 清除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);
    // 清除定时器
    clearInterval(this.timeClock);
    this.timeClock = null;
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    next(true);
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    async getAdPopupData() {
      if (platform == 'android' || platform == 'androidBox') {
        try {
          BOX.showActivityAd(3);
        } catch (error) {}
        return false;
      }
      await getAdPopupData({
        box_position: 3,
        userInfo: this.userInfo,
      });
    },
    async onResume() {
      this.SET_USER_INFO(true);
      await this.getSavingsCardIndex();
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (Math.abs(scrollTop) > 10) {
        this.navbarOpacity = Math.abs(scrollTop) / 120;
        // this.bgStyle = 'transparent';
      } else {
        this.navbarOpacity = 0;
        // this.bgStyle = 'transparent-white';
      }
    },
    goToGoldCoinCenter() {
      try {
        BOX_showActivityByAction({
          action_code: 30,
          web_url: 'WelfareGoldCoinExchange',
        });
      } catch (e) {
        BOX_openInNewWindow(
          { name: 'GoldCoinExchange' },
          { url: `${window.location.origin}/#/gold_coin_exchange` },
        );
      }
    },
    async getSavingsCardIndex() {
      const res = await ApiSavingsCardIndex();
      this.cardList = res.data.card_list;
      // this.payList = res.data.payArr;
      this.pageInfo = res.data.info;
      this.text_list = res.data.text_list;
      // this.selectedPayType = this.payList[0].key;
      this.countdown = res.data.countdown;
      if (!this.pageInfo.is_member) {
        this.selectedCard = this.cardList.find(item => {
          return item.is_default == 1;
        });

        // 判断是否有弹窗显示的缓存
        const SAVINGS_COUNTDOWN_SHOW = localStorage.getItem(
          'SAVINGS_COUNTDOWN_SHOW',
        );
        if (
          SAVINGS_COUNTDOWN_SHOW &&
          SAVINGS_COUNTDOWN_SHOW == new Date().getDate()
        ) {
          // 有缓存，并且是当天
          this.countdownPopup = false;
        } else {
          // 需要弹窗和定时器
          this.countdownPopup = true;
          // 清除定时器
          clearInterval(this.timeClock);
          this.timeClock = null;
          this.timeClock = setInterval(() => {
            this.countdown.nowTime += 1;
            if (this.countdown.endTime - this.countdown.nowTime <= 0) {
              clearInterval(this.timeClock);
              this.timeClock = null;
              this.countdownPopup = false;
            }
          }, 1000);
        }
      } else {
        this.selectedCard = {};
        this.countdownPopup = false;
      }
      this.getPayMethod();
    },
    async getPayMethod() {
      let res = await ApiGetPaymentMethod({
        orderType: 301,
      });
      this.payList = res.data;
    },
    choosePayType(selectedPayType) {
      this.selectedPayType = selectedPayType.symbol;
      this.handlePay();
    },
    handlePay() {
      this.payPopupShow = false;
      ApiSavingsCardCreateOrder({
        type: this.selectedCard.type,
        payWay: this.selectedPayType,
      }).then(orderRes => {
        // 安卓sdk下单上报
        BOX_setPayParams({
          order_id: orderRes.data.orderId,
          productname: '充值省钱卡',
        });

        // 神策埋点
        this.$sensorsTrack('save_card_payment_submit', {
          order_id: orderRes.data.orderId,
          card_type: this.selectedCard.title,
          recharge_type: '购买',
          recharge_amount: Number(this.selectedCard.old_amount),
          actual_recharge_amount: Number(this.selectedCard.amount),
          recharge_source: this.$sensorsChainGet(),
        }, 'recharge_source');

        ApiGetPayUrl({
          orderId: orderRes.data.orderId,
          orderType: 301,
          payWay: this.selectedPayType,
          packageName: '',
        }).finally(() => {
          ApiGetOrderStatus({
            order_id: orderRes.data.orderId,
            order_type: 301,
          })
            .then(async res2 => {
              await this.getSavingsCardIndex();
            })
            .catch(() => {});
        });
      });
    },
    clickCard(card) {
      if (this.pageInfo.is_member) {
        return false;
      }
      this.selectedCard = card;
    },
    clickBuyBtn() {
      this.payPopupShow = true;
    },
    async takeReward(geted = false) {
      if (this.isAndroidSdk && !geted) {
        try {
          BOX_showActivityByAction({
            action_code: 13,
            web_url: this.$h5Page.changwan_url,
          });
          return false;
        } catch (error) {}
      }

      this.$toast.loading('加载中');
      try {
        const res = await ApiSavingsCardTakeReceive();
      } finally {
        await this.getSavingsCardIndex();
      }
    },

    // 格式化时间戳为日时分秒
    formatTime(timeStamp) {
      timeStamp = Number(timeStamp);
      let day = this.addZero(Math.floor(timeStamp / 3600 / 24));
      let hour = this.addZero(Math.floor(timeStamp / 3600) % 24);
      let minute = this.addZero(Math.floor((timeStamp % 3600) / 60));
      let second = this.addZero((timeStamp % 3600) % 60);
      return {
        day,
        hour,
        minute,
        second,
      };
    },
    addZero(num) {
      num = parseInt(num);
      return num < 10 ? '0' + num : num.toString();
    },
    clickCloseCountdownPopup() {
      this.countdownPopup = false;
      localStorage.setItem('SAVINGS_COUNTDOWN_SHOW', new Date().getDate());
    },
  },
};
</script>

<style lang="less" scoped>
.savings-card-page {
  .coin-tips {
    color: #fff;
    font-size: 14 * @rem;
    &.black {
      color: #000000;
    }
  }
  .main {
    background-color: #fff;
    padding-bottom: 60 * @rem;
    .top-container {
      width: 100%;
      height: 300 * @rem;
      height: calc(300 * @rem + @safeAreaTop);
      height: calc(300 * @rem + @safeAreaTopEnv);
      background: url('~@/assets/images/recharge/savings-card/savings-card-top-bg.png')
        center top no-repeat;
      background-size: 100% 300 * @rem;
      background-size: 100% calc(300 * @rem + @safeAreaTop);
      background-size: 100% calc(300 * @rem + @safeAreaTopEnv);
      overflow: hidden;
      .user-card {
        box-sizing: border-box;
        width: 343 * @rem;
        margin: 0 auto;
        margin-top: 50 * @rem;
        margin-top: calc(50 * @rem + @safeAreaTop);
        margin-top: calc(50 * @rem + @safeAreaTopEnv);

        .user-info {
          display: flex;
          align-items: center;
          width: 100%;
          height: 70 * @rem;
          .avatar {
            width: 40 * @rem;
            height: 40 * @rem;
            background-color: #fff;
          }
          .user-right {
            margin-left: 8 * @rem;
            flex: 1;
            min-width: 0;
            .line-1 {
              display: flex;
              align-items: center;
              .nickname {
                font-size: 14 * @rem;
                color: #5c1c0a;
                font-weight: 500;
                line-height: 18 * @rem;
                height: 18 * @rem;
              }
              .no-buy {
                line-height: 13 * @rem;
                border-radius: 8 * @rem 8 * @rem 8 * @rem 1 * @rem;
                margin-left: 6 * @rem;
                padding: 2 * @rem 6 * @rem;
                font-size: 10 * @rem;
                color: #5c1c0a;
                background: rgba(233, 179, 126, 0.4);
              }
              .card-time {
                font-size: 10 * @rem;
                line-height: 13 * @rem;
                color: rgba(118, 45, 16, 0.71);
                margin-left: 6 * @rem;
              }
            }
            .card-intro {
              font-size: 10 * @rem;
              color: #a3918f;
              line-height: 13 * @rem;
              margin-top: 6 * @rem;
              height: 13 * @rem;
            }
          }
        }
      }
      .card-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 341 * @rem;
        height: 77 * @rem;
        background: linear-gradient(270deg, #ffc28d 0%, #ffe6c0 100%);
        border-radius: 14 * @rem 14 * @rem 0 * @rem 0 * @rem;
        margin: 0 auto;
        padding: 0 18 * @rem;
        box-sizing: border-box;
        .card-left {
          .text {
            font-size: 11 * @rem;
            color: #6d301d;
            line-height: 14 * @rem;
          }
          .total-gold {
            font-size: 24 * @rem;
            color: #6d301d;
            line-height: 30 * @rem;
            margin-top: 4 * @rem;
            font-weight: 600;
          }
        }
        .card-btn {
          width: 84 * @rem;
          height: 32 * @rem;
          border-radius: 15 * @rem;
          background: #fffdfa;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          font-size: 13 * @rem;
          color: #6d301d;
          font-weight: 500;
          box-shadow: 0px 0px 6px 0px rgba(252, 163, 104, 0.5);
        }
      }
    }
    .container {
      margin-top: -104 * @rem;
      background: url(~@/assets/images/recharge/savings-card/savings-card-info-bottom-bg.png)
        no-repeat;
      background-size: 100% auto;
      overflow: hidden;
      .countdown-container {
        box-sizing: border-box;
        z-index: 20;
        width: 339 * @rem;
        margin: 22 * @rem auto 8 * @rem;
        height: 42 * @rem;
        background: #ffffff;
        display: flex;
        align-items: center;
        transition: 0.3s;
        overflow: hidden;
        box-shadow: 0px 0px 10px 0px rgba(243, 51, 22, 0.1);
        border-radius: 24 * @rem;
        padding: 0 14 * @rem;

        .seckill-icon {
          flex-shrink: 0;
          width: 84 * @rem;
          height: auto;
        }
        .countdown-text {
          font-family: PingFang SC, PingFang SC;
          font-size: 12 * @rem;
          font-weight: 600;
          color: #191b1f;
          white-space: nowrap;
          margin-left: 15 * @rem;
        }
        .countdown-clock {
          display: flex;
          align-items: center;
          font-size: 12 * @rem;
          font-weight: 600;
          font-family: PingFang SC, PingFang SC;
          color: #191b1f;
          height: 28 * @rem;
          line-height: 28 * @rem;
          margin-left: 8 * @rem;
          span {
            display: block;
            width: 24 * @rem;
            height: 24 * @rem;
            background: #fdf7f1;
            border-radius: 6 * @rem;
            line-height: 24 * @rem;
            text-align: center;
            font-size: 14 * @rem;
            font-family: PingFang SC, PingFang SC;
            color: #fd5635;
            font-weight: 600;
            margin: 0 4 * @rem 0 6 * @rem;
          }
        }
        .countdown-close {
          width: 40 * @rem;
          height: 40 * @rem;
          background: url(~@/assets/images/recharge/savings-card/countdown-close.png)
            center center no-repeat;
          background-size: 12 * @rem 12 * @rem;
        }
      }
    }
    .card-list {
      margin-top: 22 * @rem;
      .card-item {
        display: flex;
        align-items: center;
        box-sizing: border-box;
        width: 341 * @rem;
        height: 92 * @rem;
        border: 1 * @rem solid #e9e9ed;
        border-radius: 12 * @rem;
        background-color: #fff;
        margin: 0 auto 12 * @rem;
        position: relative;
        .tag {
          width: 64 * @rem;
          height: 20 * @rem;
          position: absolute;
          left: -1 * @rem;
          top: -1 * @rem;
          z-index: 2;
        }
        &:last-of-type {
          margin-bottom: 0;
        }
        &.current {
          border-color: #fec172;
          position: relative;
          &::before {
            content: '';
            display: block;
            width: 341 * @rem;
            height: 92 * @rem;
            position: absolute;
            top: -1 * @rem;
            left: -1 * @rem;
            background: url(~@/assets/images/recharge/savings-card/savings-card-selected-bg.png)
              no-repeat;
            background-size: 341 * @rem 92 * @rem;
            z-index: 1;
          }
          &::after {
            content: '';
            width: 22 * @rem;
            height: 22 * @rem;
            background: url('~@/assets/images/recharge/savings-card/savings-card-selected.png')
              no-repeat;
            background-size: 22 * @rem 22 * @rem;
            position: absolute;
            right: 0;
            top: 0;
            z-index: 2;
          }
          .card-left {
            position: relative;
            z-index: 2;
            .card-title {
              color: #6d301d;
            }
            .card-day {
              background-color: #fdecdd;
              color: #6d301d;
            }
          }
          .card-center {
            position: relative;
            z-index: 2;
            .title {
              color: #6d301d;
            }
            .desc {
              color: #6d301d;
            }
          }
          .card-right {
            position: relative;
            z-index: 2;
            .money-now {
              span {
                color: #6d301d;
              }
              .price {
                color: #6d301d;
              }
            }
            .money-old {
              color: #ad8f85;
            }
          }
        }
        .card-left {
          width: 94 * @rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .card-title {
            font-weight: 600;
            color: #30343b;
            font-size: 14 * @rem;
            line-height: 18 * @rem;
          }
          .card-day {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 64 * @rem;
            height: 16 * @rem;
            background: #f0f1f5;
            border-radius: 8 * @rem;
            background-color: #f0f1f5;
            font-size: 8 * @rem;
            color: #93999f;
            text-align: center;
            margin-top: 6 * @rem;
          }
        }
        .card-center {
          flex: 1;
          min-width: 0;
          .title {
            font-size: 14 * @rem;
            color: #30343b;
            line-height: 18 * @rem;
            font-weight: 600;
          }
          .desc {
            font-size: 11 * @rem;
            color: #93999f;
            line-height: 14 * @rem;
            margin-top: 4 * @rem;
          }
        }
        .card-right {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 107 * @rem;
          .money-now {
            display: flex;
            align-items: flex-end;
            span {
              font-size: 13 * @rem;
              color: #30343b;
              font-weight: 600;
              line-height: 16 * @rem;
              margin-bottom: 3 * @rem;
              margin-right: 3 * @rem;
            }
            .price {
              font-weight: 600;
              font-size: 24 * @rem;
              color: #30343b;
              line-height: 30 * @rem;
            }
          }
          .money-old {
            font-size: 11 * @rem;
            color: #93999f;
            line-height: 14 * @rem;
            text-decoration: line-through;
            margin-top: 2 * @rem;
            text-align: center;
          }
        }
      }
    }
    .buy-fixed {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: #fff;
      padding: 10 * @rem 18 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      box-shadow: 0 -5 * @rem 5 * @rem rgba(0, 0, 0, 0.035);
      border-top: 1px solid rgba(0, 0, 0, 0.035);
    }
    .buy-btn {
      width: 339 * @rem;
      height: 48 * @rem;
      background: #261705;
      border-radius: 36 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 16 * @rem;
      color: #ffedd5;
      font-weight: 600;
      margin: 0 auto;
      position: relative;
      &.had {
        background: #d6d6d6;
        color: #ffffff;
      }
      .subscript {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 22 * @rem;
        background: linear-gradient(270deg, #fff1cc 0%, #ffd7b4 100%);
        border-radius: 20 * @rem 20 * @rem 20 * @rem 5 * @rem;
        border: 1 * @rem solid #ffffff;
        font-weight: bold;
        font-size: 12 * @rem;
        color: #5c1c0a;
        line-height: 15 * @rem;
        text-align: center;
        padding: 0 11 * @rem;
        position: absolute;
        top: -14 * @rem;
        right: 1 * @rem;
      }
    }
    .explain-container {
      box-sizing: border-box;
      width: 338 * @rem;
      border-radius: 12 * @rem;
      background-color: #ffffff;
      margin: 24 * @rem auto;
      .explain-title {
        display: flex;
        align-items: center;
        width: 100%;

        span {
          flex-shrink: 0;
          display: block;
          text-align: center;
          font-weight: 600;
          font-size: 16 * @rem;
          color: #30343b;
          line-height: 24 * @rem;
          margin: 0 10 * @rem;
        }

        &::after,
        &::before {
          content: '';
          display: block;
          width: 120 * @rem;
          height: 10 * @rem;
          background: url(~@/assets/images/recharge/savings-card/explain-title-right.png)
            no-repeat;
          background-size: 120 * @rem 10 * @rem;
        }
        &::before {
          background-image: url(~@/assets/images/recharge/savings-card/explain-title-left.png);
        }
      }
      .content {
        padding-top: 12 * @rem;
        font-size: 13 * @rem;
        color: #93999f;
        line-height: 20 * @rem;
      }
    }
  }

  .pay-container-popup {
    .pay-container {
      box-sizing: border-box;
      position: relative;
      padding: 10 * @rem 14 * @rem 0;
      padding-bottom: @safeAreaBottom;
      padding-bottom: @safeAreaBottomEnv;
      .pay-way-close {
        position: absolute;
        right: 10 * @rem;
        top: 10 * @rem;
        width: 40 * @rem;
        height: 40 * @rem;
        background: url(~@/assets/images/recharge/recharge-popup-close.png)
          center center no-repeat;
        background-size: 25 * @rem 25 * @rem;
      }
      .pay-way-title {
        font-size: 16 * @rem;
        color: #333;
        height: 40 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .pay-list {
        .pay-item {
          display: flex;
          align-items: center;
          padding: 16 * @rem 0;
          border-bottom: 1px solid #eeeeee;
          .icon {
            width: 24 * @rem;
            height: 24 * @rem;
            background-position: center center;
            background-repeat: no-repeat;
            background-size: 24 * @rem 24 * @rem;
            // &.wx {
            //   background-image: url(~@/assets/images/recharge/wx-icon.png);
            //   background-size: 24 * @rem 21 * @rem;
            // }
            // &.zfb_dmf {
            //   background-image: url(~@/assets/images/recharge/zfb-icon.png);
            //   background-size: 24 * @rem 24 * @rem;
            // }
          }

          .pay-center {
            margin-left: 8 * @rem;
            .line {
              display: flex;
              align-items: center;
              .pay-name {
                font-size: 15 * @rem;
                color: #333;
              }
              .recommend-icon {
                width: 39 * @rem;
                height: 17 * @rem;
                background-size: 39 * @rem 17 * @rem;
                background-position: left center;
                background-repeat: no-repeat;
                margin-left: 5 * @rem;
              }
            }
            .subtitle {
              color: #999;
              font-size: 12 * @rem;
              line-height: 20 * @rem;
            }
          }
          .choose {
            margin-left: auto;
            width: 18 * @rem;
            height: 18 * @rem;
            background: url(~@/assets/images/recharge/n_radio.png) center center
              no-repeat;
            background-size: 18 * @rem 18 * @rem;
            &.on {
              background-image: url(~@/assets/images/recharge/c_radio.png);
            }
          }
        }
      }
      .pay-btn {
        width: 290 * @rem;
        height: 45 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;

        background: linear-gradient(180deg, #535051 0%, #1f1e1e 100%);
        font-size: 18 * @rem;
        font-weight: bold;
        color: #fff;
        margin: 30 * @rem auto;
        border-radius: 23 * @rem;
      }
    }
  }
  /deep/ .van-dialog {
    overflow: unset;
    width: 320 * @rem;
  }
}
</style>
