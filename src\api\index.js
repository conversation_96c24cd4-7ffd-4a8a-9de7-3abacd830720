import store from '../store';
import router from '../router';
import { service } from './axios';
import { encrypt, decrypt, initKeyIv } from '@/utils/Encrypt';
import { Toast, Dialog } from 'vant';
import BASEPARAMS from '@/utils/baseParams';
import h5Page from '@/utils/h5Page';
import { themeColorLess } from '@/common/styles/_variable.less';
import useCloudDialog from '@/views/CloudHangup/components/cloud-dialog/index.js';
import usePcCloudGameDialog from '@/components/pc-cloud-game-dialog/index.js';
import { openKefu } from '@/utils/tools.js';
import {
  platform,
  getToken,
  from,
  BOX_login,
  authInfo,
  packageName,
  BOX_openInNewNavWindow,
  BOX_openInNewWindow,
  BOX_memAuth,
  Box_changePhone,
} from '@/utils/box.uni.js';
import { isIos, isWebApp } from '@/utils/userAgent';
import qs from 'qs';
import i18n from '@/i18n';
import { devLog, tokenInvalidity } from '@/utils/function.js';
import { getQueryVariable } from '@/utils/function.js';
import useActivityDialog from '@/views/Active/GameDownActivity/components/activity-dialog/index.js';

import { presetProperties } from '@/utils/sensors.js';
/**
 * 封装请求方法
 * @param url
 * @param parmas
 * @param isEncrypt 是否加密
 * @param domain 传输的域名
 * @param res.data.code 返回参数
 *  1.通用成功（明文）
 *  2.通用成功（RSA密文）
 *  3.通用成功（AES密文）
 *  0.通用错误消息，客户端尽可能将此消息通过界面提示给用户
 *  -1.静默错误消息，客户端不要将此消息提示给用户，仅用于调试打印LOG
 *  -2.登录失效，需要重新登录
 *  -3.登录过期，需要重新登录
 *  -4.服务器维护中，弹个什么东西告知下用户吧
 *  -5.RSA秘钥相关错误，需要尝试重新获取RSA秘钥
 *  -6.提示客户端需要升级
 *  -7.设备号错误
 *  -8.直接弹出绑定手机号的界面
 *  -9.弹出对话框展示错误消息，标题：需要实名认证，按钮：去认证、继续游戏
 *  -10.不是SVIP用户
 *  -11.客户端清除登录信息，并弹出使用手机验证码登录的界面
 *  -12.微信第一次登录，弹出绑定手机号界面
 *  -13.拉黑状态下的微信登录弹窗
 *  -14.跳转到SVIP充值界面
 *  -15.AES秘钥相关错误
 *  -16.设置微信提醒
 *  -30.错误信息以弹窗形式展现，标题：错误，按钮：我知道了
 *  -36.云挂机弹出-联系客服
 *  -38.赏金任务
 *  -40.PC云游戏SDK 时长不足弹窗
 *  -41.PC云游戏非会员签到弹窗
 */

const submitApiArray = [
  '/api/user/submitUserInfo',
  '/api/user/submitInstallInfo',
  '/api/user/submitLoginInfo',
  '/api/user/submitRegisterInfo',
  '/api/medium/submitWechatMedium',
  '/api/medium/submitWechatRegister',
];
export function request(
  url = '',
  params = {},
  isEncrypt = true,
  domain = h5Page.api_url,
) {
  // 拦截重复请求
  let currentLoadingUrl = `${url}&${qs.stringify(params)}`; // 要拼接上参数才能保证请求的唯一性
  if (store.getters['system/loadingUrl'] == currentLoadingUrl) {
    devLog('拦截到重复请求：', currentLoadingUrl);
    return Promise.reject({ code: -1000 });
  }
  store.commit('system/setLoadingUrl', currentLoadingUrl);
  // 整理参数
  if (packageName) {
    // 带上包名（不同包名有不同的默认头像）
    BASEPARAMS.packageName = packageName;
  }
  // 带上平台
  if (platform) {
    BASEPARAMS.platform = platform;
    if (platform == 'h5' && isWebApp) {
      BASEPARAMS.platform = 'webapp';
    }
  }
  //  如有登录带上token
  switch (platform) {
    case 'ios':
    case 'android':
      if (getToken()) {
        BASEPARAMS.token = getToken();
      } else {
        BASEPARAMS.token = '';
      }
      if (from) {
        BASEPARAMS.from = from;
      }
      // 官包其他公共参数
      if (authInfo) {
        BASEPARAMS.uuid = authInfo.uuid;
        BASEPARAMS.versionCode = authInfo.versionCode;
        BASEPARAMS.channel = authInfo.channel;
      }
      break;
    case 'androidBox':
      // 安卓马甲包都是用web的公共参数，但是需要一个壳的版本号
      if (authInfo) {
        BASEPARAMS.packageVersionCode = authInfo.versionCode;
      }
    case 'iosBox':
      // if (getToken()) {
      //   BASEPARAMS.token = getToken();
      // }
      // token不存在是要把BASEPARAMS.token转为空字符串
      BASEPARAMS.token = store.getters['user/userInfo']?.token || '';
      if (authInfo.uuid) {
        // 有个包叫“极游社”那个uuid有问题不能用，包名就是这个
        if (authInfo.packageName !== 'com.test.app') {
          BASEPARAMS.uuid = authInfo.uuid;
        }
      }
      // from 测试 ==> 安卓 775 ios 776    正式 ==> 安卓 501 ios 502
      if (process.env.NODE_ENV == 'development') {
        BASEPARAMS.from = isIos ? 776 : 775;
      } else {
        BASEPARAMS.from = isIos ? 502 : 501;
      }
      break;
    default:
      // token不存在是要把BASEPARAMS.token转为空字符串
      BASEPARAMS.token = store.getters['user/userInfo']?.token || '';
      // from 测试 ==> 安卓 775 ios 776    正式 ==> 安卓 501 ios 502
      if (process.env.NODE_ENV == 'development') {
        BASEPARAMS.from = isIos ? 776 : 775;
      } else {
        BASEPARAMS.from = isIos ? 502 : 501;
      }
      break;
  }
  // 自家上报 2024年7月22日15:01:10
  if (store.getters['system/mediumDeviceId']) {
    BASEPARAMS.medium_device_id = store.getters['system/mediumDeviceId'];
  }

  // 四个上报数据的接口在iosBox里要传壳的uuid  （2023年3月21日15:17:39 安卓马甲包也要上报了）
  // web端也要上报了
  (() => {
    if (submitApiArray.includes(url)) {
      if (authInfo) {
        if (platform == 'androidBox') {
          BASEPARAMS.androidid = authInfo.androidid ?? '';
          BASEPARAMS.deviceid = authInfo.deviceid ?? '';
          BASEPARAMS.imei = authInfo.imei ?? '';
          BASEPARAMS.oaid = authInfo.oaid ?? '';
        }
      } else if (platform == 'h5') {
        if (getQueryVariable('deviceid')) {
          BASEPARAMS.deviceid = getQueryVariable('deviceid') ?? '';
        } else if (getQueryVariable('medium_key')) {
          BASEPARAMS.medium_key = getQueryVariable('medium_key') ?? '';
          BASEPARAMS.medium_from = getQueryVariable('medium_from') ?? '';
        }
      }
    }
  })();

  // 神策匿名id
  BASEPARAMS.anonymous_id =
    platform == 'android'
      ? authInfo.anonymousId
      : presetProperties._distinct_id;

  const reqData = { ...BASEPARAMS, ...params };
  if (submitApiArray.includes(url) && authInfo) {
    reqData.uuid = authInfo.uuid;
  }

  url = `${domain}${url}`;

  // 神策 anonymous_id需要放到公共头部
  let headers = {
    'Content-Type': 'multipart/form-data',
  };
  devLog('req:', url, reqData);
  // 判断是否加密
  let data = {};
  let [key, iv] = [];
  if (isEncrypt) {
    // 生成随机数key iv
    [key, iv] = initKeyIv();
    // 加密
    data = encrypt(reqData, key, iv);
    // 旧
    // data.code = 4;
    // 新
    data.code = 113;
  } else {
    data = reqData;
    data.code = 1;
  }
  // 转成formData格式，支持跨域
  let formData = new FormData(); // 创建form对象
  for (let i in data) {
    formData.append(i, data[i]);
  }

  let serviceData = {
    method: 'post',
    url: url,
    data: formData,
    headers: headers,
  };

  // 测试h5游戏互通，登录接口跨域传cookie，通讯方法不一定靠谱，可能以后废弃，放一起好删;还有一块在打开h5游戏方法那
  if (
    url.indexOf('/api/user/login') > -1 &&
    process.env.NODE_ENV !== 'development'
  ) {
    url = url.replace('api.', 'api2.');
    url = url.replace('a3733', '3733');
    serviceData.url = url;
    serviceData.withCredentials = true;
  }

  return new Promise((resolve, reject) => {
    service(serviceData)
      .then(res => {
        devLog('res:', url, res);
        let code = parseInt(res.data.code);
        // points全局判断
        // res.data.points = [{ rule_name: '12313', text: '34234', num: '123' }, { rule_name: '12313', text: '34234', num: '123' }]
        if (
          res.data.points &&
          res.data.points.length > 0 &&
          res.config.url.indexOf('/api/user/activeSign') == -1 &&
          res.config.url.indexOf('/api/user/getGoldBox') == -1
        ) {
          // 是否有金币信息 并且不是签到页面的请求(签到请求单独处理弹窗)
          // 2023年10月9日17:44:23 并且不是领取宝箱的请求 领取宝箱svip和普通用户弹窗不一样，所以去页面处理
          let pointStr = '';
          res.data.points.forEach(point => {
            pointStr += `${point.rule_name}，${point.text}${point.num}\n`;
          });
          Dialog.alert({
            message: pointStr,
            lockScroll: false,
          });
        }

        switch (code) {
          case 3:
            const resData = decrypt(res.data.data, key, iv);
            try {
              res.data.data = JSON.parse(resData);
            } catch {
              res.data.data = resData;
            }
            if (
              res.data.msg &&
              res.config.url.indexOf('/api/user/activeSign') == -1 &&
              res.config.url.indexOf('/api/search/index') == -1 &&
              res.config.url.indexOf('/api/user/getGoldBox') == -1 &&
              res.config.url.indexOf('/api/bounty_task/getTaskReward') == -1 &&
              res.config.url.indexOf('/api/mission/getMissionReward') == -1 &&
              res.config.url.indexOf('/cloud/cloud_task/reward') == -1 &&
              res.config.url.indexOf('/cloud/cloud_task/daySign') == -1
            ) {
              // 签到请求不要全局弹窗
              Toast(res.data.msg);
            }
            resolve(res.data);
            break;
          case 1:
            if (res.data.msg) {
              Toast(res.data.msg);
            }
            resolve(res.data);
            break;
          case 0:
            if (res.data.msg) {
              Toast(res.data.msg);
            }
            reject(res.data);
            break;
          case -1:
            reject(res.data);
            break;
          case -2:
          case -3:
            store.commit('user/setUserInfo', {});
            store.commit('user/setUserInfoEx', {});
            localStorage.setItem('STORE', JSON.stringify(store.state));
            if (router.history.current.name !== 'Mine') {
              Toast.clear();
              Dialog.confirm({
                message: res.data.msg,
                confirmButtonText: i18n.t('确定'),
                confirmButtonColor: themeColorLess,
                lockScroll: false,
              }).then(() => {
                BOX_login();
                // router.push({ name: 'PhoneLogin' });
              });
            } else {
              Toast.clear();
              Dialog.alert({
                message: res.data.msg,
                confirmButtonText: i18n.t('我知道了'),
                confirmButtonColor: themeColorLess,
                lockScroll: false,
              }).then(() => {});
            }
            tokenInvalidity(res.data);
            reject(res.data);
            break;
          case -4:
            Toast(i18n.t('我知道了'));
            break;
          case -8:
            Toast.clear();
            Dialog.confirm({
              message: res.data.msg,
              confirmButtonText: i18n.t('去绑定'),
              cancelButtonText: i18n.t('取消'),
              confirmButtonColor: themeColorLess,
              lockScroll: false,
            }).then(() => {
              Box_changePhone();
            });
            break;
          case -9:
            Toast.clear();
            Toast(i18n.t('请先实名认证'));
            setTimeout(() => {
              BOX_memAuth();
            }, 500);
            break;
          case -10:
            Toast.clear();
            Dialog.confirm({
              message: res.data.msg,
              confirmButtonText: i18n.t('去开通'),
              cancelButtonText: i18n.t('取消'),
              confirmButtonColor: themeColorLess,
              lockScroll: false,
            }).then(() => {
              BOX_openInNewWindow({ name: 'Svip' }, { url: h5Page.svip_url });
            });
            break;
          case -11:
            store.commit('user/setUserInfo', {});
            store.commit('user/setUserInfoEx', {});
            localStorage.setItem('STORE', JSON.stringify(store.state));
            Toast(res.data.msg);
            router.replace({ name: 'PhoneLogin' });
            break;
          case -14:
            resolve(res.data); // svip
            break;
          case -16:
            Toast(res.data.msg);
            reject(res.data);
            break;
          case -30:
            Toast.clear();
            // 特殊场景不需要弹窗给用户 需要忽略弹窗的接口路径列表
            const ignoreUrls = [
              '/cloud/sdk/reportPlay', // pc云游 - 时长上报
            ];
            if (
              res.data.msg &&
              ignoreUrls.some(url => res.config.url.indexOf(url) !== -1)
            ) {
              return;
            }
            Dialog.alert({
              message: res.data.msg,
              confirmButtonText: i18n.t('我知道了'),
              confirmButtonColor: themeColorLess,
              lockScroll: false,
            }).then(() => {});
            break;
          case -33:
            Toast.clear();
            Dialog.confirm({
              message: res.data.msg,
              confirmButtonText: '去充值',
              cancelButtonText: i18n.t('取消'),
              confirmButtonColor: themeColorLess,
              lockScroll: false,
            }).then(() => {
              BOX_openInNewNavWindow(
                { name: 'PlatformCoin' },
                { url: h5Page.ptb_url },
              );
            });
            break;
          //金币不足
          case -34:
            Toast.clear();
            Dialog.confirm({
              message: res.data.msg,
              confirmButtonText: '去赚金币',
              cancelButtonText: i18n.t('取消'),
              confirmButtonColor: themeColorLess,
              lockScroll: false,
            }).then(() => {
              BOX_openInNewWindow(
                { name: 'GoldCoin' },
                { url: h5Page.renwudating },
              );
            });
            break;
          //专属活动报错弹窗
          case -35:
            Toast.clear();
            useActivityDialog({
              title: '提示',
              desc: res.data.msg,
              confirmText: '知道了',
            });
            break;
          // 云挂机-联系客服
          case -36:
            let replaceMsg = res.data.msg.replace(/\n/g, '<br>');
            useCloudDialog({
              title: '提示',
              desc: replaceMsg,
              showCancel: true,
              cancelText: '取消',
              confirmText: '联系客服',
              onCancel: () => {
                router.go(-1);
              },
              onConfirm: () => {
                openKefu({
                  from: 'cloudHangup',
                  gameName: store.getters['cloud_hangup/receiveData'].title,
                  id: store.getters['cloud_hangup/receiveData'].id,
                  package_name:
                    store.getters['cloud_hangup/receiveData'].package_name,
                  is_zx: false,
                });
                // router.push({ name: 'KefuChat', params: { is_zx: false } });
              },
            });
          case -38:
            if (
              res.data.msg &&
              res.config.url.indexOf('/api/bounty_task/getTaskReward') == -1
            ) {
              // 签到请求不要全局弹窗
              Toast(res.data.msg);
            }
            resolve(res.data);
            break;
          // PC云游戏SDK 时长不足弹窗
          case -40:
            resolve(res.data);
            break;
          // PC云游戏非会员签到弹窗
          case -41:
            Toast(res.data.msg);
            usePcCloudGameDialog({
              showCancel: true,
              cancelText: '稍后再说',
              confirmText: '立即开通',
              onCancel: () => {},
              onConfirm: () => {
                router.push({ name: 'CloudGameBuy' });
              },
            });
            break;
          case 500:
            Toast(res.data.msg);
            reject(res.data);
            break;
          default:
            Toast(res.data.msg);
            break;
        }
      })
      .catch(err => {
        reject(err);
        devLog('res:', url, err);
        Toast.clear(); //清除所有正在loading的弹窗
        if (platform != 'android') {
          store.commit('system/setPostError', true);
        }
        Toast(i18n.t('网络不好请稍后重试'));
      })
      .finally(() => {
        store.commit('system/setLoadingUrl');
      });
  });
}
