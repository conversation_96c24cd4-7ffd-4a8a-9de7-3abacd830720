<script>
import { handleTimestamp } from '@/utils/datetime';
import GiftCopyPopup from '@/components/gift-copy-popup';
import {
  ApiGameSignInIndex,
  ApiGameTakeSignPrize,
  ApiGameSupplySign,
} from '@/api/views/game';

export default {
  data() {
    return {
      game_id: 0,
      game_info: {},
      repair_sign_day: 0,
      today_sign: 0,
      prize_list: [],
      init_data: {},
      explain_text: '',
      ruler_text: '',
      start_time: '',
      end_time: '',
      showCalendar: false,
      sign_in_log: [],
      exchange_code_popup: {
        show: false,
      },
    };
  },
  computed: {
    // 游戏角标
    subscript() {
      if (
        this.game_info.classid == 107 &&
        this.game_info.f_pay_rebate &&
        this.game_info.f_pay_rebate != 0 &&
        this.game_info.f_pay_rebate != 100
      ) {
        return `${parseFloat(this.game_info.f_pay_rebate) / 10}${this.$t(
          '折',
        )}`;
      } else if (
        this.game_info.classid == 107 &&
        this.game_info.pay_rebate &&
        this.game_info.pay_rebate != 100
      ) {
        if (parseFloat(this.game_info.pay_rebate) == 100) {
          return `10${this.$t('折')}`;
        } else {
          return `${parseFloat(this.game_info.pay_rebate) / 10}${this.$t(
            '折',
          )}`;
        }
      } else if (this.game_info.first_pay_icon) {
        return this.$t('无门槛');
      } else if (this.game_info.has_coupon) {
        return this.$t('代金券');
      } else {
        return false;
      }
    },
  },
  async created() {
    this.game_id = this.$route.params.id;
    await this.handleInitData();
  },
  methods: {
    formatDate(date) {
      const { month, day } = handleTimestamp(date);
      return `${month}.${day}`;
    },
    formatter(day) {
      let temp_day = this.handleTimestamp(day.date.getTime() / 1000);
      let today = this.handleTimestamp(new Date().getTime() / 1000);
      this.sign_in_log.forEach(ele => {
        if (ele.day === temp_day.normal_date) {
          if (ele.is_repair) {
            day.bottomInfo = '补签';
            if (
              today.month == temp_day.month &&
              today.day - temp_day.day >= 7
            ) {
              day.type = 'disabled';
              day.className = 'cant';
            } else {
              day.className = 'can';
            }
          } else {
            day.bottomInfo = '已签';
            day.type = 'disabled';
            day.className = 'already';
          }
        } else {
          if (temp_day.day >= today.day) {
            day.type = 'disabled';
            day.className = 'future';
          }
        }
      });
      day.className += ' active-time';
      return day;
    },
    async handleInitData() {
      const res = await ApiGameSignInIndex({ game_id: this.game_id });
      this.game_info = res.data.game_info;
      this.explain_text = res.data.head_desc;
      this.ruler_text = res.data.rule_desc.replace(/\r\n/g, '<br/><br/>');
      this.prize_list = res.data.prize_info;
      this.init_data = res.data.act_info;
      this.repair_sign_day = res.data.bqcs;
      this.start_time = new Date(parseInt(this.init_data.start_time) * 1000);
      this.end_time = new Date(parseInt(this.init_data.end_time) * 1000);
      this.sign_in_log = res.data.sign_in_log;
    },
    handleTimestamp,
    handleSign() {
      this.$toast('登录游戏后自动签到');
    },
    selectDate(date) {
      if (new Date().getTime() - date.getTime() >= 604800000) {
        this.$toast('补签只支持7天内');
        return false;
      }
      this.$dialog
        .confirm({
          title: '温馨提示',
          message: `是否消耗${this.init_data.repair_gold}金币进行补签？`,
          lockScroll: false,
        })
        .then(async () => {
          this.$toast.loading({
            message: '补签中',
            forbidClick: false,
            duration: 0,
          });
          await ApiGameSupplySign({
            game_id: this.game_id,
            date: handleTimestamp(date.getTime() / 1000).normal_date,
          });
          this.handleInitData();
        });
    },
    async handleTakePrize(item, type = 0) {
      if (item.status == 0) {
        this.$toast('还未完成签到任务，请先完成任务！');
        return false;
      }
      if (item.status == 1) {
        this.$toast('您已领取过啦~');
        return false;
      }
      if (!this.userInfo.token) {
        this.toPage('PhoneLogin');
        return false;
      }
      if (type == 1 && !this.userInfo.is_svip) {
        this.$dialog
          .confirm({
            title: '温馨提示',
            message: `领取${item.vip_other_prize}金币，需开通SVIP！`,
          })
          .then(() => {
            this.toPage('Svip');
          });
        return false;
      }
      const res = await ApiGameTakeSignPrize({
        id: item.id,
        type: type,
      });
      if (type == 0) {
        this.exchange_code_popup = { ...res.data };
        this.exchange_code_popup.show = true;
      }
      this.init_data();
    },
    copy(text) {
      this.$copyText(text).then(
        res => {
          this.$toast(this.$t('复制成功'));
          this.copyDialogShow = false;
        },
        err => {
          this.$dialog.alert({
            message: this.$t('复制失败，请手动复制'),
            lockScroll: false,
          });
        },
      );
    },
    changeExchangCodePopup(show) {
      this.exchange_code_popup.show = show;
    },
  },
  components: {
    GiftCopyPopup,
  },
};
</script>
<template>
  <div class="sign-in-detail">
    <nav-bar-2 :title="'签到详情'" :border="true"></nav-bar-2>
    <div
      v-if="game_info"
      :style="{
        backgroundImage: `url(${game_info.video_thumb})`,
      }"
      class="game-wrapper"
    >
      <div v-if="game_info" class="game-info">
        <img class="game-icon" :src="game_info.titlepic" />
        <div class="detail">
          <div class="title">
            {{ game_info.title }}
            <div v-if="subscript" class="subscript">{{ subscript }}</div>
          </div>
          <div v-if="init_data.start_time" class="small-text">
            签到时间:{{ formatDate(init_data.start_time) }} -
            {{ formatDate(init_data.end_time) }}
          </div>
        </div>
        <div
          @click="
            toPage('GameDetail', { id: game_info.id, gameInfo: game_info })
          "
          class="button btn"
        >
          立即下载
        </div>
      </div>
    </div>
    <div class="sign-wrapper">
      <div class="explain">{{ explain_text }}</div>
      <div class="button-container">
        <div @click="showCalendar = !showCalendar" class="left-button button">
          <div class="big-text btn">签到日历</div>
          <div class="small-text btn">可补签{{ repair_sign_day }}天</div>
        </div>
        <div @click="handleSign" class="right-button button">签到</div>
      </div>
      <van-calendar
        v-if="showCalendar"
        title="日历"
        :poppable="false"
        :show-title="false"
        :show-confirm="false"
        :min-date="start_time"
        :max-date="end_time"
        :formatter="formatter"
        :default-date="null"
        @select="selectDate"
        class="calendar"
      />
      <div class="sign-list">
        <div
          v-for="(item, index) in prize_list"
          :key="index"
          :class="{ vip: item.vip_other_prize > 0 }"
          class="sign-item"
        >
          <div class="sign-info">
            <img :src="item.img" class="sign-img" />
            <div class="sign-detail">
              <div class="sign-big-text">累计签到{{ item.day }}天</div>
              <div class="sign-small-text">{{ item.info }}</div>
            </div>
            <div
              :class="{ already: item.status == 1, cant: item.status == 0 }"
              @click="handleTakePrize(item)"
              class="sign-button btn"
            >
              {{ item.button_text }}
            </div>
          </div>
          <div v-if="item.vip_other_prize > 0" class="vip-sign">
            <div class="vip-icon">会员专享</div>
            <div class="vip-text">{{ item.vip_other_prize }}金币</div>
            <div
              :class="{
                already: item.vip_status == 1,
                cant: item.vip_status == 0,
              }"
              @click="handleTakePrize(item, 1)"
              class="sign-button btn"
            >
              {{ item.vip_button_text }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="ruler">
      <div class="title">规则说明</div>
      <div class="text" v-html="ruler_text"></div>
    </div>
    <!-- 兑换码弹窗 -->
    <GiftCopyPopup
      :show="exchange_code_popup.show"
      :popup_data="exchange_code_popup"
      @changeShow="changeExchangCodePopup"
    />
  </div>
</template>
<style lang="less">
.sign-in-detail .van-calendar__day.active-time {
  border-radius: 10 * @rem;
  &.already {
    color: #333;
    .van-calendar__bottom-info {
      color: @themeBg;
    }
  }
  &.today {
    color: @themeBg;
  }
  &.future {
    color: #333;
  }
}
</style>
<style lang="less" scoped>
.game-wrapper {
  height: 180 * @rem;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  background-size: 100% auto;
  .game-info {
    height: 120 * @rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 18 * @rem;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #161616 100%);
  }
  .game-icon {
    width: 46 * @rem;
    height: 46 * @rem;
    border-radius: 10 * @rem;
  }
  .detail {
    flex: 1;
    color: #ffffff;
    margin: 0 8 * @rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .title {
      font-weight: 600;
      font-size: 16 * @rem;
      .subscript {
        display: inline-block;
        padding: 3 * @rem 5 * @rem;
        background: rgba(255, 117, 84, 1);
        font-size: 12 * @rem;
        color: #fff;
        border-radius: 10 * @rem 10 * @rem 10 * @rem 0;
        transform: scale(0.75);
      }
    }
    .small-text {
      margin-top: 8 * @rem;
    }
  }
  .button {
    width: 78 * @rem;
    height: 32 * @rem;
    background: @themeColor;
    border-radius: 6 * @rem;
    font-size: 14 * @rem;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.sign-wrapper {
  position: relative;
  top: -20 * @rem;
  .image-bg('~@/assets/images/games/sign-in/game-sign-in-bg.png');
  background-size: 100% auto;
  background-color: #fff;
  border-radius: 15 * @rem 15 * @rem 0 0;
  padding-top: 71 * @rem;
  .explain {
    margin: 0 23 * @rem 18 * @rem;
    line-height: 1.2;
    font-size: 13 * @rem;
    color: #666666;
  }
  .button-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24 * @rem;
    padding: 0 18 * @rem;
    .button {
      box-sizing: border-box;
      width: 160 * @rem;
      height: 56 * @rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      color: #fff;
      &.left-button {
        .image-bg('~@/assets/images/games/sign-in/game-sign-in-bg3.png');
        padding-left: 60 * @rem;
        .big-text {
          font-size: 16 * @rem;
          font-weight: bold;
        }
        .small-text {
          font-size: 11 * @rem;
          margin-top: 4 * @rem;
        }
      }
      &.right-button {
        .image-bg('~@/assets/images/games/sign-in/game-sign-in-bg2.png');
        font-size: 16 * @rem;
        font-weight: bold;
        color: #fff;
        padding-left: 75 * @rem;
      }
    }
  }
  .calendar {
    height: 415 * @rem;
    margin: 0 18 * @rem 15 * @rem;
  }
  .sign-list {
    .sign-item {
      position: relative;
      margin: 0 18 * @rem 12 * @rem;
      padding: 0 11 * @rem;
      background: #f9fafc;
      border-radius: 12 * @rem;
      overflow: hidden;
      &.vip::before {
        content: '会员额外送';
        position: absolute;
        top: 12 * @rem;
        left: -49 * @rem;
        transform: scale(0.8) rotate(-45deg);
        display: flex;
        justify-content: center;
        align-items: center;
        width: 140 * @rem;
        height: 20 * @rem;
        background: linear-gradient(220deg, #ffaa2b 0%, #ff542b 100%);
        font-size: 12 * @rem;
        color: #fff;
      }
      .sign-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 72 * @rem;
        .sign-img {
          flex: 0 0 40 * @rem;
          width: 40 * @rem;
          height: 40 * @rem;
        }
        .sign-detail {
          flex: 1;
          display: flex;
          height: 40 * @rem;
          flex-direction: column;
          justify-content: space-between;
          margin: 0 10 * @rem 0 6 * @rem;
          overflow: hidden;
          .sign-big-text {
            font-size: 16 * @rem;
            color: #443e5b;
          }
          .sign-small-text {
            font-size: 11 * @rem;
            color: rgba(83, 76, 111, 0.8);
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
      .vip-sign {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 11 * @rem 0;
        border-top: 1 * @rem solid #e7e7e7;
        .vip-icon {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 56 * @rem;
          height: 18 * @rem;
          background: linear-gradient(180deg, #ffce51 0%, #ff9a02 100%);
          border-radius: 4 * @rem;
          font-size: 11 * @rem;
          color: #ba571f;
        }
        .vip-text {
          flex: 1;
          margin-left: 10 * @rem;
        }
      }
      .sign-button {
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        width: 68 * @rem;
        height: 30 * @rem;
        border-radius: 22 * @rem;
        background: #8965ff;
        border-radius: 22 * @rem;
        color: #fff;
        &.already {
          background: #d8d7e0;
        }
        &.cant {
          border: 1 * @rem solid rgba(83, 76, 111, 0.2);
          color: #534c6f;
          background: none;
        }
      }
    }
  }
}
.ruler {
  padding: 0 25 * @rem 24 * @rem;
  .title {
    margin-bottom: 12 * @rem;
    font-size: 16 * @rem;
    font-weight: 600;
    color: #333333;
  }
  .text {
    word-break: break-all;
    margin-bottom: 8 * @rem;
    font-size: 13px;
    color: rgba(83, 76, 111, 0.8);
    line-height: 21px;
  }
}
.exchange-code-popup {
  box-sizing: border-box;
  width: 300 * @rem;
  padding: 30 * @rem 30 * @rem 20 * @rem;
  .title {
    text-align: center;
    font-size: 16 * @rem;
    font-weight: 600;
  }
  .big-text {
    margin: 21 * @rem 0 12 * @rem;
    text-align: center;
    font-size: 14 * @rem;
    color: #777777;
  }
  .small-text {
    margin: 5 * @rem 0 15 * @rem;
    text-align: center;
    font-size: 11 * @rem;
    color: #777777;
  }
  .code {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f5f5f5;
    font-size: 16 * @rem;
    font-weight: 600;
    height: 70 * @rem;
    border-radius: 8 * @rem;
  }
  .button {
    display: flex;
    width: 100%;
    height: 38 * @rem;
    color: #fff;
    justify-content: center;
    align-items: center;
    font-size: 14 * @rem;
    background: #8965ff;
    border-radius: 22 * @rem;
  }
}
</style>
