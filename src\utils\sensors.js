// 引入神策分析 SDK
import Vue from 'vue';

import store from '@/store/index.js';

import { devLog, getQueryVariable } from '@/utils/function.js';

import BASEPARAMS from '@/utils/baseParams.js';

import { platform, authInfo } from '@/utils/box.uni.js';

import sensors from 'sa-sdk-javascript';

import Exposure from 'sa-sdk-javascript/dist/web/plugin/exposure/index.es6';

// 神策数据接收地址 http://*************:8106/sa?project=default
const serverUrl = 'https://shence-data.a3733.com:8106/sa?project=default';

// 初始化神策分析
sensors.init({
  server_url: serverUrl,
  heatmap: false,
  send_type: 'beacon', // 数据发送方式，默认为 image，可以设置为 beacon
  useClientTime: true, // 是否使用客户端时间，默认为 false

  show_log: getQueryVariable('sensors_log') == 1 ? true : false, // 是否在控制台打印日志，默认为 false
  max_string_length: 500, // 字符串类型数据最大长度，默认 500
  max_array_length: 100, // 数组类型数据最大长度，默认 100
  datasend_timeout: 3000, // 数据发送超时时间，默认 3000ms
  app_js_bridge: platform == 'android' ? true : false, // 是否启用 app_js_bridge，默认为 true，App打通H5
  use_app_track: false, // 是否开启应用内打点，默认为 false
  batch_send:
    platform == 'android'
      ? false
      : {
          // 批量发送配置，默认关闭
          datasend_timeout: 6000, //一次请求超过多少毫秒的话自动取消，防止请求无响应。
          send_interval: 6000, //间隔多少毫秒发一次数据。
          storage_length: 200, // 存储 localStorage 条数最大值，默认：200 。如 localStorage 条数超过该值，则使用 image 方式立即发送数据。v1.24.8 以上支持。
        },
});

sensors.use(Exposure, {
  area_rate: 1,
  stay_duration: 0,
  repeated: true,
});

// 注册公共属性
sensors.registerPage({
  is_vip: function () {
    return store.getters['user/userInfo']?.is_svip ? true : false;
  },
  is_open_lqk: function () {
    return store.getters['user/userInfo']?.is_sqk_member ? true : false;
  },
  channel_source: function () {
    if (['android', 'ios'].includes(platform) && authInfo) {
      return authInfo.channel;
    }
    return BASEPARAMS.channel;
  },
});

// 神策初始化后生成的数据
let presetProperties = {};
sensors.quick('isReady', function () {
  presetProperties = sensors.getPresetProperties();
});

/**
 *
 * @param {埋点事件} eventName
 * @param {埋点参数} properties
 * @param {安卓端时的来源（前向）字段} scouceName
 * @returns
 */
function sensorsTrack(eventName, properties = {}, scouceName) {
  if (typeof eventName !== 'string') {
    devLog('==========SensorsTrack=============:埋点事件名必须为字符串');
    return;
  }

  if (platform === 'android') {
    try {
      // 安卓端前向页获取
      if (scouceName && !properties[scouceName]) {
        properties[scouceName] = BOX.getSourceName();
      }
    } catch (error) {
      devLog(error);
    }
  }

  try {
    sensors.track(eventName, properties);
    devLog(`==========SensorsTrack=============:${eventName}`, properties);
  } catch (error) {
    devLog(
      `==========SensorsTrack=============:${eventName}事件埋点上报失败`,
      error,
    );
  }
}

/**
 * chain设置，用于想sensors传递前向页面，如果有传参数newPage,则覆盖chain，
 * @param {string} newPage 新的页面名称，可选参数
 */
function sensorsChainSet (newPage) {
  if (newPage) {
    store.commit('sensors/setChainName', newPage);
  } else {
    store.commit('sensors/setChainName', '');
  }
}

function sensorsChainGet () {
  return store.getters['sensors/chainName'];
}

/**
 * Page设置，用于想sensors传递当前页面名称，
 * @param {string} newPage 新的页面名称，可选参数
 */
function sensorsPageSet (newPage) {
  if (newPage) {
    store.commit('sensors/setPageName', newPage);
  } else {
    store.commit('sensors/setPageName', '');
  }
}

function sensorsPageGet () {
  return store.getters['sensors/pageName'];
}

// sensors实例
Vue.prototype.$sensors = sensors;

// sensors埋点方法
Vue.prototype.$sensorsTrack = sensorsTrack;

// sensors chain设置方法(设置前向页)
Vue.prototype.$sensorsChainSet = sensorsChainSet;

Vue.prototype.$sensorsChainGet = sensorsChainGet;

// sensors page设置方法(设置当前页)
Vue.prototype.$sensorsPageSet = sensorsPageSet;

Vue.prototype.$sensorsPageGet = sensorsPageGet;

Vue.directive('sensors-exposure', {
  bind (el, binding) {
    const prefix = binding.arg || 'data-sensors-exposure-'; // 使用指令参数作为前缀，默认为data-

    if (typeof binding.value === 'object' && binding.value !== null) {
      for (const [attrName, attrValue] of Object.entries(binding.value)) {
        el.setAttribute(`${prefix}${attrName}`, attrValue);
      }
    }
  },
});

export {
  sensors,
  sensorsTrack,
  sensorsChainSet,
  sensorsChainGet,
  sensorsPageSet,
  sensorsPageGet,
  presetProperties,
};
