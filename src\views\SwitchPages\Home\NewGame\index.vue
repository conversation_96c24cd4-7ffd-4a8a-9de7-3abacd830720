<template>
  <div
    class="game-page"
    :class="{
      'loading-bg': !pageLoading,
      'game-page-before': resourceList?.length,
    }"
  >
    <van-loading class="loading-box" v-if="pageLoading">{{
      $t('加载中...')
    }}</van-loading>
    <!-- 资源位 -->
    <div class="resource-list" v-if="resourceList && resourceList?.length">
      <div
        class="resource-item"
        :class="{ 'resource-item-default': resourceList?.length === 1 }"
        v-for="item in resourceList"
        :key="item.id"
      >
        <div class="top-title">
          <div class="title"> {{ item.text1 }} </div>
          <div
            class="btn-more"
            v-if="item.action_code !== 0"
            @click="btnMore(item)"
          >
            <span v-if="item.count">共{{ item.count }}款</span>
            <span></span>
          </div>
        </div>
        <div class="box-wrap">
          <div class="box-list">
            <div
              class="box-item"
              v-for="game in item.game_list"
              :key="game.id"
              @click="clickGameItem(game)"
            >
              <div class="icon">
                <img :src="game.titlepic" alt="" />
              </div>
              <span class="name">{{ game.title }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <van-sticky :offset-top="stickyOffsetTop">
      <div
        class="date-list"
        :class="{ 'date-list-reset': resourceList?.length }"
        v-if="calendar.length"
      >
        <swiper
          ref="dateSwiper"
          :options="dateSwiperOption"
          class="date-swiper"
        >
          <template v-for="(date, index) in calendar">
            <swiper-slide
              class="swiper-slide"
              v-if="date.list && date.list.length"
              :key="index"
            >
              <div class="date-item month" v-if="date.week_flag">
                {{ date.day }}
              </div>
              <div
                class="date-item"
                :class="{ active: date.date == selectDay.date }"
                v-else
              >
                <div class="week">{{ date.week }}</div>
                <div class="day">{{ date.day }}</div>
              </div>
            </swiper-slide>
          </template>
        </swiper>
        <div class="filter-btn" @click="openPopup">
          <div class="filter-cont">
            <span>筛选</span>
            <div class="show-cont">全部 <i></i></div>
          </div>
        </div>
      </div>
    </van-sticky>

    <div class="main-list">
      <pull-refresh @refresh="onRefresh" v-model="reloading">
        <template v-for="(date, index) in calendar">
          <div
            class="main-item"
            :ref="date.date"
            :date="date.date"
            v-if="date.date && date.list && date.list.length"
            :key="index"
          >
            <div class="title">
              {{ formatDate(date.date, 3) }}
              <span>{{ formatDate1(date.date, 4) }}</span>
            </div>
            <div class="game-list">
              <div
                class="game-item"
                v-for="(game, index) in date.list"
                :key="game.id + '' + index"
              >
                <game-item4 :gameInfo="game" :iconSize="72" :showHot="true">
                </game-item4>
                <yy-download-btn :gameInfo="game"></yy-download-btn>
              </div>
            </div>
            <!-- 更多新游 -->
            <div
              class="more-new-game"
              v-if="date.tamperGame && date.tamperGame.length"
            >
              <div
                class="more-game-box"
                @click="handleMoreGame(date.tamperGame)"
              >
                <div class="more-game-list">
                  <div
                    class="more-game-item"
                    v-for="(item, index) in date.tamperGame"
                    :key="item.id"
                    :style="{
                      zIndex: date.tamperGame.length - index,
                    }"
                  >
                    <img :src="item.titlepic" alt="" />
                  </div>
                </div>
                <div class="more-game-btn">
                  <span class="more-game-text"> 更多新游 </span>
                  <div class="more-game-arrow">
                    <img
                      src="@/assets/images/home/<USER>/more-game-arrow.png"
                      alt=""
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <!-- 猜你感兴趣 -->
        <div class="guess-you-like" v-if="recommendList?.length">
          <div class="you-like-box" @click="guessYouLikePopupShow = true">
            <div class="like-list">
              <div
                class="list-item"
                v-for="(item, index) in recommendList.slice(
                  0,
                  recommendShowCount,
                )"
                :key="item.id"
                :style="{
                  zIndex:
                    recommendList.slice(0, recommendShowCount).length - index,
                }"
              >
                <img :src="item.titlepic" alt="" />
              </div>
            </div>
            <span class="you-like-text">
              可能感兴趣的<span class="num">{{ recommendCount }}</span
              >款游戏
            </span>
            <div class="you-like-arrow">
              <img
                src="@/assets/images/home/<USER>/new-game-icon-arrow.png"
                alt=""
              />
            </div>
          </div>
        </div>
      </pull-refresh>
    </div>

    <!-- 筛选弹窗 -->
    <van-popup
      class="filter-container"
      v-model="filterPopupShow"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      position="bottom"
      round
      :close-icon-position="'top-right'"
    >
      <div
        class="new-game-filter"
        @touchmove.self="
          e => {
            e.preventDefault();
            e.stopPropagation();
          }
        "
      >
        <div class="popup-name" @touchmove.prevent="">
          <span>新游</span>
        </div>
        <div class="nav-list" v-if="tabList.length" @touchmove.prevent="">
          <div
            class="nav-item"
            v-for="(nav, index) in tabList"
            :key="index"
            :class="{ active: tabIndex == nav.id }"
            @click="navTabClick(nav.id)"
          >
            <div class="nav-title">
              {{ nav.txt }}
            </div>
          </div>
        </div>
        <template v-if="tabIndex != 3">
          <yy-list
            class="game-container"
            v-model="popupLoadingObj"
            :finished="popupFinished"
            @refresh="onPopupRefresh"
            @loadMore="popupLoadMore"
            :empty="popupEmpty"
            :check="false"
          >
            <div class="new-game-list">
              <div
                class="list-item"
                v-for="(item, index) in popupGameList"
                :key="index"
              >
                <div class="title" v-if="item['list'] && item['list'].length">
                  {{ formatDate(item['list'][0].newstime) }}
                  <span>{{ formatDate1(item['list'][0].newstime) }}</span>
                </div>
                <div class="title" v-else>
                  {{ formatDate(item['tamperList'][0].newstime) }}
                  <span>{{ formatDate1(item['tamperList'][0].newstime) }}</span>
                </div>
                <div class="list" v-if="item['list'] && item['list'].length">
                  <div
                    class="game-item"
                    v-for="(game, index) in item['list']"
                    :key="game.id + '' + index"
                  >
                    <game-item4 :gameInfo="game" :iconSize="72" :showHot="true">
                    </game-item4>
                    <yy-download-btn :gameInfo="game"></yy-download-btn>
                  </div>
                </div>
                <!-- 更多新游 -->
                <div
                  class="more-new-game"
                  :class="{
                    'only-more-game': !item['list'].length,
                  }"
                  v-if="
                    item['tamperList'] &&
                    item['tamperList'].length &&
                    (popupGameList[index + 1] || popupFinished)
                  "
                >
                  <div
                    class="more-game-box"
                    @click="handleMoreGame(item['tamperList'])"
                  >
                    <div class="more-game-list">
                      <div
                        class="more-game-item"
                        v-for="(item, index) in item['tamperList']"
                        :key="item.id"
                        :style="{
                          zIndex: item.length - index,
                        }"
                      >
                        <img :src="item.titlepic" alt="" />
                      </div>
                    </div>
                    <div class="more-game-btn">
                      <span class="more-game-text"> 更多新游 </span>
                      <div class="more-game-arrow">
                        <img
                          src="@/assets/images/home/<USER>/more-game-arrow.png"
                          alt=""
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </yy-list>
        </template>
        <template v-else>
          <yy-list
            class="game-container"
            v-model="popupLoadingObj"
            :finished="popupFinished"
            @refresh="onPopupRefresh"
            @loadMore="popupLoadMore"
            :empty="popupEmpty"
            :check="false"
            :tips="'暂无数据'"
            successText="加载成功"
            loosingText="释放查看更多游戏"
            pullingText="下拉查看更多游戏"
            loadingText="拼命加载中..."
          >
            <div class="new-game-list">
              <div
                class="list-item"
                v-for="(item, index) in popupGameList"
                :key="index"
              >
                <div class="title" v-if="item['list'] && item['list'].length">
                  {{ formatDate(item['list'][0].newstime) }}
                  <span>{{ formatDate1(item['list'][0].newstime) }}</span>
                </div>
                <div class="title" v-else>
                  {{ formatDate(item['tamperList'][0].newstime) }}
                  <span>{{ formatDate1(item['tamperList'][0].newstime) }}</span>
                </div>
                <div class="list" v-if="item['list'] && item['list'].length">
                  <div
                    class="game-item"
                    v-for="(game, index) in item['list']"
                    :key="game.id + '' + index"
                  >
                    <game-item4
                      :gameInfo="game.game"
                      :iconSize="72"
                      :showHot="true"
                    >
                      <div class="server-info">{{
                        formatTime(game.newstime)
                      }}</div>
                      <div class="server-info">
                        {{ game.state }}
                      </div>
                    </game-item4>
                    <yy-download-btn :gameInfo="game.game"></yy-download-btn>
                  </div>
                </div>
                <!-- 更多新游 -->
                <div
                  class="more-new-game"
                  :class="{
                    'only-more-game': !item['list'].length,
                  }"
                  v-if="
                    item['tamperList'] &&
                    item['tamperList'].length &&
                    (popupGameList[index + 1] || popupFinished)
                  "
                >
                  <div
                    class="more-game-box"
                    @click="handleMoreGame(item['tamperList'])"
                  >
                    <div class="more-game-list">
                      <div
                        class="more-game-item"
                        v-for="(item, index) in item['tamperList']"
                        :key="item.id"
                        :style="{
                          zIndex: item.length - index,
                        }"
                      >
                        <img :src="item.titlepic" alt="" />
                      </div>
                    </div>
                    <div class="more-game-btn">
                      <span class="more-game-text"> 更多新游 </span>
                      <div class="more-game-arrow">
                        <img
                          src="@/assets/images/home/<USER>/more-game-arrow.png"
                          alt=""
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </yy-list>
        </template>
        <div class="close-btn" @click="filterPopupShow = false">
          <img
            src="~@/assets/images/home/<USER>/filter-game-icon-close.png"
            alt=""
          />
        </div>
      </div>
    </van-popup>

    <!-- 猜你感兴趣弹窗 -->
    <van-popup
      class="guess-you-like-container"
      v-model="guessYouLikePopupShow"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      position="bottom"
      round
      :closeable="false"
      :close-icon-position="'top-right'"
    >
      <div
        class="guess-you-like-filter"
        @touchmove.self="
          e => {
            e.preventDefault();
            e.stopPropagation();
          }
        "
      >
        <div class="popup-name popup-bg" @touchmove.prevent="">
          <span
            >可能感兴趣的<span class="num">{{ recommendCount }}</span
            >款游戏</span
          >
        </div>
        <div class="guess-you-like-list">
          <div class="you-like-container">
            <div class="you-like-game-list">
              <div class="list-item">
                <div
                  class="game-item"
                  v-for="(item, index) in recommendList"
                  :key="index"
                  :class="{
                    'no-margin-b12': index === recommendList?.length - 1,
                  }"
                >
                  <game-item4 :gameInfo="item" :iconSize="72" :showHot="true">
                  </game-item4>
                  <yy-download-btn :gameInfo="item"></yy-download-btn>
                </div>
                <load-more
                  v-show="recommendList?.length > 0"
                  v-model="youLikeLoading"
                  :finished="youLikeFinished"
                  finishedText="已经到底~~"
                >
                </load-more>
              </div>
            </div>
          </div>
        </div>
        <div class="close-btn" @click="guessYouLikePopupShow = false">
          <img
            src="~@/assets/images/home/<USER>/new-game-icon-close.png"
            alt=""
          />
        </div>
      </div>
    </van-popup>

    <!-- 更多新游弹窗 -->
    <van-popup
      class="guess-you-like-container"
      v-model="moreGamePopupShow"
      :close-on-click-overlay="true"
      :lock-scroll="false"
      position="bottom"
      round
      :closeable="false"
      :close-icon-position="'top-right'"
    >
      <div
        class="guess-you-like-filter"
        @touchmove.self="
          e => {
            e.preventDefault();
            e.stopPropagation();
          }
        "
      >
        <div class="popup-name" @touchmove.prevent="">
          <span>更多新游</span>
        </div>
        <div class="guess-you-like-list">
          <div class="you-like-container">
            <div class="you-like-game-list">
              <div class="list-item">
                <div
                  class="game-item"
                  v-for="(item, index) in moreGameList"
                  :key="index"
                  :class="{
                    'no-margin-b12': index === moreGameList?.length - 1,
                  }"
                >
                  <game-item4 :gameInfo="item" :iconSize="72" :showHot="true">
                  </game-item4>
                  <yy-download-btn :gameInfo="item"></yy-download-btn>
                </div>
                <load-more
                  v-show="moreGameList?.length > 0"
                  v-model="youLikeLoading"
                  :finished="youLikeFinished"
                  finishedText="已经到底~~"
                >
                </load-more>
              </div>
            </div>
          </div>
        </div>
        <div class="close-btn" @click="moreGamePopupShow = false">
          <img
            src="~@/assets/images/home/<USER>/new-game-icon-close.png"
            alt=""
          />
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  ApiCwbIndexGameList,
  ApiCwbIndexGameListNew,
  ApiCwbIndexGameScreenList,
} from '@/api/views/home.js';
import { navigateToGameDetail } from '@/utils/function';
import { handleActionCode } from '@/utils/actionCode.js';
export default {
  data() {
    const that = this;
    return {
      calendar: [],
      scrolling: false,
      reloading: false,
      youLikeLoading: false,
      youLikeFinished: true,
      dateSwiperOption: {
        slidesPerView: 'auto',
        freeMode: true,
        initialSlide: 0,
        centeredSlides: true,
        centeredSlidesBounds: true,
        observer: true,
        observeSlideChildren: true,
        on: {
          click() {
            if (that.calendar[this.clickedIndex].date) {
              this.slideTo(this.clickedIndex, 300, true);
              that.selectDay = that.calendar[this.clickedIndex];
              const element =
                that.$refs[that.calendar[this.clickedIndex].date][0];
              const distance = element.offsetTop; // 获取元素相对于父元素的偏移位置
              const top = document.querySelectorAll('.main-list')[0].offsetTop;
              that.timer = setTimeout(() => {
                clearTimeout(this.timer);
                that.timer = null;
              }, 500);
              window.scrollTo({
                left: 0,
                top: distance + 10,
                behavior: 'smooth',
              });
            }
          },
        },
      },
      timer: null,
      selectDay: {},
      filterPopupShow: false, //筛选弹窗
      guessYouLikePopupShow: false, //猜你感兴趣弹窗
      recommendList: [], //猜你感兴趣预览列表
      recommendCount: 0, //猜你感兴趣数量
      recommendShowCount: 5, //猜你感兴趣预览数量
      moreGameList: [], //更多新游列表
      moreGamePopupShow: false, //更多新游弹窗
      tabIndex: 1,
      tabList: [],
      popupLoadingObj: {
        loading: false,
        reloading: false,
      },
      popupPage: 1,
      popupDownPage: 1,
      popupReloadFinished: false,
      popupFinished: false,
      popupEmpty: false,
      popupGameList: [],
      nowTime: 0,
      firstTime: 0,
      resourceList: [], //资源列表
      stickyOffsetTop: '0px', //顶部导航栏的高度
      pageLoading: true,
      isFirstActivation: true,
      listRows: 10,
    };
  },
  async mounted() {
    // 获取顶部导航栏的高度
    this.stickyOffsetTop = document.querySelector('.fixed').offsetHeight + 'px';
  },
  async activated() {
    if (this.isFirstActivation) {
      this.pageLoading = true;
      this.isFirstActivation = false;
    }
    try {
      await this.getGameList();
    } finally {
      this.pageLoading = false;
    }
  },
  deactivated() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    handleMoreGame(list) {
      this.moreGameList = list;
      this.$nextTick(() => {
        this.moreGamePopupShow = true;
      });
    },
    btnMore(item) {
      handleActionCode(item);
    },
    clickGameItem(item) {
      navigateToGameDetail(item);
    },
    handleScroll() {
      if (this.timer) {
        return false;
      } else {
        let top = document.querySelectorAll('.main-list')[0].offsetTop;
        this.timer = setTimeout(() => {
          const contents = document.querySelectorAll('.main-item');
          for (let i = 0; i < contents.length; i++) {
            const content = contents[i];
            const rect = content.getBoundingClientRect();
            if (rect.top <= top && rect.bottom > top) {
              this.calendar.forEach((item, index) => {
                if (item.date == content.getAttribute('date')) {
                  this.$refs.dateSwiper.$swiper.slideTo(index, 0, true);
                  this.selectDay = this.calendar[index];
                }
              });
            }
          }
          clearTimeout(this.timer);
          this.timer = null;
        }, 200);
      }
    },
    async getGameList() {
      let res = await ApiCwbIndexGameListNew();
      this.calendar = res.data.calendar;
      this.resourceList = res.data?.top_list;
      this.recommendList = res.data?.recommendList;
      this.recommendCount = res.data?.recommendCount;
      this.recommendShowCount = res.data?.recommendShowCount;
      this.calendar.forEach((item, index) => {
        if (item.day_flag) {
          this.dateSwiperOption.initialSlide = index;
          this.selectDay = item;
        }
      });
      if (this.calendar.length) {
        if (!this.selectDay.date) {
          this.selectDay = this.calendar[0];
          this.dateSwiperOption.initialSlide = 0;
        }
        this.$nextTick(() => {
          this.$refs.dateSwiper.$swiper.slideTo(0, 0, true);
          window.addEventListener('scroll', this.handleScroll);
        });
      }
    },
    async onRefresh() {
      window.removeEventListener('scroll', this.handleScroll);
      await this.getGameList();
      this.reloading = false;
    },
    async openPopup() {
      this.filterPopupShow = true;
      this.nowTime = 0;
      this.firstTime = 0;
      this.popupEmpty = false;
      this.popupFinished = false;
      this.popupReloadFinished = false;
      this.popupGameList = [];
      this.popupLoadingObj.loading = true;
      this.tabIndex = 1;
      await this.getGameScreenList();
      this.popupLoadingObj.loading = false;
    },
    async getGameScreenList(action = 1, isDown = true) {
      //isDown true上滑 false下拉
      if (action == 1) {
        this.nowTime = 0;
        this.firstTime = 0;
        this.popupFinished = false;
        this.popupReloadFinished = false;
      } else {
        if (isDown && this.popupFinished) {
          return;
        } else if (!isDown && this.popupReloadFinished) {
          return;
        }
      }
      let params = {
        type: this.tabIndex,
        list_rows: this.listRows,
        now_time: this.nowTime,
        first_time: this.firstTime,
      };
      if (this.tabIndex == 3 && isDown) {
        params.page = this.popupPage;
      }
      if (this.tabIndex == 3 && !isDown) {
        params.page = this.popupDownPage;
      }
      let res = await ApiCwbIndexGameScreenList(params);
      if (action == 1) {
        this.popupGameList = [];
      }
      this.tabList = res.data.types;

      if (res.data.list.length == 0) {
        if (isDown) {
          this.popupFinished = true;
        } else {
          this.$toast('没有更多了');
          this.popupReloadFinished = true;
        }
      } else {
        let formatArr = this.classify(
          res.data.list,
          res.data.tamperGame,
          'newstime',
          isDown,
        );
        if (
          this.tabIndex != 3 &&
          action != 1 &&
          res.data.list.length + res.data.tamperGame.length < this.listRows
        ) {
          this.popupFinished = true;
        } else {
          if (this.popupFinished === true) {
            this.popupFinished = false;
          }
        }
        if (isDown) {
          this.popupGameList.push(...formatArr);
        } else {
          this.popupGameList.unshift(...formatArr);
        }
      }
      if (this.popupGameList.length) {
        this.popupEmpty = false;
      } else {
        this.popupEmpty = true;
      }
      this.popupLoadingObj.loading = false;
      this.popupLoadingObj.reloading = false;
    },
    async onPopupRefresh() {
      if (this.tabIndex != 3) {
        this.popupFinished = false;
        this.popupLoadingObj.loading = true;
        await this.getGameScreenList();
        this.popupLoadingObj.loading = false;
        return;
      }
      if (this.popupReloadFinished) {
        this.$toast('没有更多了');
        this.popupLoadingObj.reloading = false;
        return false;
      }
      this.popupDownPage++;
      this.firstTime =
        this.popupGameList[0]['list'][0].newstime ||
        this.popupGameList[0]['tamperList'][0].newstime;
      this.nowTime = 0;
      await this.getGameScreenList(2, false);
      this.popupLoadingObj.reloading = false;
    },
    async popupLoadMore() {
      const lastItem = this.popupGameList[this.popupGameList.length - 1];
      let allItems = [];
      if (lastItem.list && lastItem.list.length > 0) {
        allItems = [...allItems, ...lastItem.list];
      }
      if (lastItem.tamperList && lastItem.tamperList.length > 0) {
        allItems = [...allItems, ...lastItem.tamperList];
      }

      if (this.tabIndex == 1) {
        const minTime =
          allItems.length > 0
            ? Math.min(...allItems.map(item => item.newstime))
            : 0;
        this.nowTime = minTime;
      } else {
        const maxTime =
          allItems.length > 0
            ? Math.max(...allItems.map(item => item.newstime))
            : 0;
        this.nowTime = maxTime;
      }
      this.firstTime = 0;
      this.popupPage++;
      await this.getGameScreenList(2, true);
      this.popupLoadingObj.loading = false;
    },
    async navTabClick(id) {
      if (this.popupLoadingObj.loading || this.popupLoadingObj.reloading)
        return;
      if (this.tabIndex === id) return;
      this.tabIndex = id;
      this.popupGameList = [];
      this.popupDownPage = 1;
      this.popupPage = 1;
      this.popupLoadingObj.loading = true;
      await this.getGameScreenList();
      this.popupLoadingObj.loading = false;
    },
    formatDate(timestamp, flag = '') {
      if (!flag) {
        flag = this.tabIndex;
      }
      let date = new Date(this.getDayZeroTimestamp(timestamp));
      let isYear = date.getFullYear() != new Date().getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      switch (flag) {
        case 1:
          if (this.isToday(timestamp)) {
            return '今天首发';
          } else if (this.isYesterday(timestamp)) {
            return '昨天首发';
          }
          break;
        case 2:
          if (this.isTomorrow(timestamp)) {
            return '明天';
          }
          break;
        case 3:
          if (this.isToday(timestamp)) {
            return '今天';
          } else if (this.isYesterday(timestamp)) {
            return '昨天';
          } else if (this.isTomorrow(timestamp)) {
            return '明天';
          }
          break;
      }
      return isYear
        ? `${date.getFullYear()}年${month}月${day}日`
        : `${month}月${day}日`;
    },
    formatDate1(timestamp, flag = '') {
      if (!flag) {
        flag = this.tabIndex;
      }
      let date = new Date(this.getDayZeroTimestamp(timestamp));
      let isYear = date.getFullYear() != new Date().getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let str = '';
      switch (flag) {
        case 1:
          if (this.isToday(timestamp)) {
            return `${month}月${day}日`;
          } else if (this.isYesterday(timestamp)) {
            return `${month}月${day}日`;
          }
          break;
        case 2:
          if (this.isTomorrow(timestamp)) {
            str = `${month}月${day}日`;
          }
          break;
        case 3:
          if (
            this.isToday(timestamp) ||
            this.isYesterday(timestamp) ||
            this.isTomorrow(timestamp)
          ) {
            str = `${month}月${day}日`;
          }
          break;
        case 4:
          if (this.isToday(timestamp) || this.isTomorrow(timestamp)) {
            str = `${month}月${day}日`;
          }
          break;
        default:
          break;
      }
      return str ? str + ' ' + this.week(timestamp) : this.week(timestamp);
    },
    formatTime(timestamp) {
      let date = new Date(timestamp * 1000);
      let hour = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
      let minute =
        date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
      return `${hour}:${minute}`;
    },
    classify(arr, tamperArr, key, isDown) {
      let obj = {};
      arr.forEach(e => {
        const timestamp = this.getDayZeroTimestamp(e[key]);
        if (typeof obj[timestamp] == 'undefined') {
          obj[timestamp] = {
            list: [],
            tamperList: [],
          };
        }

        if (isDown) {
          if (
            this.popupGameList.length &&
            this.popupGameList[this.popupGameList.length - 1]['list'] &&
            this.popupGameList[this.popupGameList.length - 1]['list'].length &&
            this.getDayZeroTimestamp(
              this.popupGameList[this.popupGameList.length - 1]['list'][0][key],
            ) == timestamp
          ) {
            // 如果该项的时间和popupGameList最后一项的时间一样，则push到popupGameList的最后一项
            this.popupGameList[this.popupGameList.length - 1]['list'].push(e);
            delete obj[timestamp];
          } else {
            obj[timestamp].list.push(e);
          }
        } else {
          if (
            this.popupGameList.length &&
            this.popupGameList[0]['list'] &&
            this.popupGameList[0]['list'].length &&
            this.getDayZeroTimestamp(this.popupGameList[0]['list'][0][key]) ==
              timestamp
          ) {
            // 如果该项的时间和popupGameList第一项的时间一样
            this.popupGameList[0]['list'].unshift(e);
            delete obj[timestamp];
          } else {
            obj[timestamp].list.unshift(e);
          }
        }
      });

      // 处理tamperGame数据
      if (tamperArr && tamperArr.length > 0) {
        tamperArr.forEach(game => {
          const timestamp = this.getDayZeroTimestamp(game[key]);
          let foundMatch = false;

          // 先检查obj中是否已有相同日期的list
          if (obj[timestamp] && obj[timestamp].list.length > 0) {
            obj[timestamp].tamperList.push(game);
            foundMatch = true;
          }
          // 再检查popupGameList中是否有相同日期的组
          else if (this.popupGameList && this.popupGameList.length) {
            for (let i = 0; i < this.popupGameList.length; i++) {
              const group = this.popupGameList[i];
              if (
                group &&
                group.list &&
                group.list.length > 0 &&
                this.getDayZeroTimestamp(group.list[0][key]) == timestamp
              ) {
                // 如果在popupGameList中找到相同日期的组，添加到其tamperList
                if (!group.tamperList) {
                  group.tamperList = [];
                }
                group.tamperList.push(game);
                foundMatch = true;
                break;
              }
            }
          }

          // 如果都没找到相同日期的组 则创建新组
          if (!foundMatch) {
            if (typeof obj[timestamp] == 'undefined') {
              obj[timestamp] = {
                list: [],
                tamperList: [],
              };
            }
            obj[timestamp].tamperList.push(game);
          }
        });
      }

      return Object.values(obj);
    },
    // 获取当天0点的时间戳
    getDayZeroTimestamp(timestamp) {
      timestamp = Number(timestamp) * 1000;
      return new Date(new Date(timestamp).toLocaleDateString()).getTime();
    },
    isToday(timestamp) {
      timestamp = Number(timestamp) * 1000;
      return (
        new Date(timestamp).toLocaleDateString() ==
        new Date().toLocaleDateString()
      );
    },
    isYesterday(timestamp) {
      timestamp = Number(timestamp) * 1000;
      return (
        new Date(timestamp).toLocaleDateString() ==
        new Date(
          new Date().getTime() - 24 * 60 * 60 * 1000,
        ).toLocaleDateString()
      );
    },
    isTomorrow(timestamp) {
      timestamp = Number(timestamp) * 1000;
      return (
        new Date(timestamp).toLocaleDateString() ==
        new Date(
          new Date().getTime() + 24 * 60 * 60 * 1000,
        ).toLocaleDateString()
      );
    },
    week(timestamp) {
      let day = new Date(Number(timestamp) * 1000).getDay();
      switch (day) {
        case 0:
          return '星期日';
          break;
        case 1:
          return '星期一';
          break;
        case 2:
          return '星期二';
          break;
        case 3:
          return '星期三';
          break;
        case 4:
          return '星期四';
          break;
        case 5:
          return '星期五';
          break;
        case 6:
          return '星期六';
          break;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.game-page {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  &.loading-bg {
    background: #f7f8fa;
  }
  &:before {
    content: '';
    position: relative;
    display: block;
    width: 100%;
    height: calc(158 * @rem + @safeAreaTop);
    height: calc(158 * @rem + @safeAreaTopEnv);
    background: #fff;
  }
  .loading-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .resource-list {
    padding: 6 * @rem 12 * @rem 12 * @rem 12 * @rem;
    height: 108 * @rem;
    display: flex;
    align-items: center;
    background: #fff;
    .resource-item {
      flex-shrink: 0;
      flex: 1;
      width: 170 * @rem;
      height: 108 * @rem;
      border-radius: 12 * @rem;
      background: #ecf9fb;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .top-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 14 * @rem;
        line-height: 14 * @rem;
        padding: 12 * @rem 0 0 11 * @rem;
        .title {
          flex: 1;
          min-width: 0;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 500;
          font-size: 14 * @rem;
          color: #496c90;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .btn-more {
          margin-left: 10 * @rem;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          min-width: 12 * @rem;
          padding-right: 12 * @rem;
          height: 14 * @rem;
          > span:first-child {
            font-weight: 400;
            font-size: 10 * @rem;
            color: #496c90;
          }
          > span:last-child {
            margin-left: 2 * @rem;
            background: url('~@/assets/images/home/<USER>/right-arrow-icon1.png')
              no-repeat 0 0;
            background-size: 4 * @rem 6 * @rem;
            width: 4 * @rem;
            height: 6 * @rem;
          }
        }
      }
      .box-wrap {
        padding: 0 10 * @rem 10 * @rem 10 * @rem;
        .box-list {
          display: flex;
          align-items: center;
          overflow-x: auto;
          &::-webkit-scrollbar {
            display: none;
          }
          .box-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 44 * @rem;
            flex-shrink: 0;
            .icon {
              width: 44 * @rem;
              height: 44 * @rem;
              border-radius: 10 * @rem;
              overflow: hidden;
              background: #eeeeee;
            }
            .name {
              width: 44 * @rem;
              margin-top: 4 * @rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font-weight: 400;
              font-size: 10 * @rem;
              color: #496c90;
              text-align: center;
            }
            &:not(:first-of-type) {
              margin-left: 9 * @rem;
            }
          }
        }
      }

      &:nth-child(2) {
        background: #fff7ee;
        margin-left: 10 * @rem;
        .top-title {
          padding: 12 * @rem 0 0 10 * @rem;
          .title {
            color: #c67d4f;
          }
          .btn-more {
            > span:first-child {
              color: #d08e65;
            }
            > span:last-child {
              margin-left: 2 * @rem;
              flex-shrink: 0;
              background: url('~@/assets/images/home/<USER>/right-arrow-icon2.png')
                no-repeat 0 0;
              background-size: 4 * @rem 6 * @rem;
              width: 4 * @rem;
              height: 6 * @rem;
            }
          }
        }
        .box-wrap {
          .box-list {
            .box-item {
              .name {
                color: #d08e65;
              }
              &:not(:first-of-type) {
                margin-left: 9 * @rem;
              }
            }
          }
        }
      }
    }
    .resource-item-default {
      height: 116 * @rem;
      .top-title {
        .title {
          flex: 1;
          min-width: 0;
        }
      }
      .box-wrap {
        padding: 0 0 12 * @rem 12 * @rem;
        .box-list {
          .box-item {
            width: 48 * @rem;
            .icon {
              width: 48 * @rem;
              height: 48 * @rem;
              border-radius: 12 * @rem;
            }
            .name {
              width: 48 * @rem;
              margin-top: 6 * @rem;
            }
            &:not(:first-of-type) {
              margin-left: 17 * @rem;
            }
          }
        }
      }
    }
  }
  .date-list {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 450 * @rem;
    height: 64 * @rem;
    // padding: 16 * @rem 0;
    box-sizing: border-box;
    // background: #fff url(~@/assets/images/home/<USER>/date-bg.png) no-repeat;
    // background-size: 100% 78 * @rem;
    background: #fff;
    position: fixed;
    top: calc(85 * @rem + calc(var(--statusHeight) + env(safe-area-inset-top)));
    z-index: 2;
    &.date-list-reset {
      position: relative;
      top: 0;
    }
    .date-swiper {
      flex: 1;
      min-width: 0;
    }

    .swiper-slide {
      width: auto;

      &:last-of-type .date-item {
        margin-right: 0;
      }
      &:first-child .date-item {
        margin-left: 6 * @rem;
      }
    }

    .date-item {
      width: 36 * @rem;
      height: 46 * @rem;
      margin-right: 12 * @rem;
      text-align: center;
      padding-top: 6 * @rem;
      box-sizing: border-box;
      &.month {
        width: 36 * @rem;
        height: 36 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: 0;
        border-radius: 5 * @rem;
        background-color: #f5f5f5;
        margin-top: 5 * @rem;
      }

      &.active {
        background: linear-gradient(
          180deg,
          rgba(69, 204, 162, 0.2) 0%,
          rgba(255, 255, 255, 0.2) 100%
        );
        border-radius: 4 * @rem;

        .week {
          color: #333;
        }
      }

      .week {
        height: 14 * @rem;
        font-weight: 400;
        font-size: 10 * @rem;
        color: #999999;
        line-height: 14 * @rem;
        text-align: center;
      }
      .day {
        height: 17 * @rem;
        font-weight: 600;
        font-size: 12 * @rem;
        color: #333333;
        line-height: 17 * @rem;
        text-align: center;
        margin-top: 3 * @rem;
      }
    }

    .filter-btn {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60 * @rem;
      height: 46 * @rem;
      background: url(~@/assets/images/home/<USER>/filter-bg.png) no-repeat;
      background-size: 60 * @rem 46 * @rem;
      padding-left: 8 * @rem;
      box-sizing: border-box;

      .filter-cont {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 44 * @rem;
        height: 44 * @rem;
        border: 1 * @rem solid #f1f1f1;
        border-radius: 10 * @rem;
        text-align: center;
        margin: 0 auto;
        box-sizing: border-box;

        span {
          display: block;
          height: 14 * @rem;
          font-weight: 400;
          font-size: 10 * @rem;
          color: #666666;
          line-height: 14 * @rem;
          text-align: center;
        }

        .show-cont {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 17 * @rem;
          font-weight: bold;
          font-size: 12 * @rem;
          color: #333333;
          line-height: 17 * @rem;
          text-align: center;

          i {
            flex-shrink: 0;
            display: block;
            width: 8 * @rem;
            height: 5 * @rem;
            background: url(~@/assets/images/home/<USER>/arrow.png) no-repeat;
            background-size: 8 * @rem 5 * @rem;
            margin-left: 3 * @rem;
          }
        }
      }
    }
  }
  .main-list {
    margin-top: 12 * @rem;
    padding: 0 12 * @rem;
    .main-item {
      background: #ffffff;
      border-radius: 12 * @rem;
      .title {
        display: flex;
        align-items: center;
        padding: 12 * @rem 12 * @rem;
        height: 20 * @rem;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #333333;
        line-height: 20 * @rem;

        &::before {
          content: '';
          display: block;
          width: 4 * @rem;
          height: 10 * @rem;
          border-radius: 16 * @rem;
          background-color: @themeColor;
          margin-right: 6 * @rem;
        }

        span {
          display: block;
          height: 18 * @rem;
          line-height: 18 * @rem;
          padding: 0 6 * @rem;
          background: rgba(231, 231, 231, 0.3);
          border-radius: 2 * @rem;
          font-size: 11 * @rem;
          color: #333333;
          margin-left: 5 * @rem;
        }
      }
    }
    .more-new-game {
      margin: 10 * @rem auto 16 * @rem;
      padding: 0 12 * @rem;
      box-sizing: border-box;
      height: 75 * @rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .more-game-box {
        border-top: 1px solid #f0f1f5;
        flex: 1;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        .more-game-list {
          display: flex;
          align-items: center;
          position: relative;
          width: 248 * @rem;
          overflow-x: auto;
          &::-webkit-scrollbar {
            display: none;
          }
          .more-game-item {
            flex-shrink: 0;
            width: 44 * @rem;
            height: 44 * @rem;
            border-radius: 10 * @rem;
            border: 2 * @rem solid #ffffff;
            overflow: hidden;
            background: #eeeeee;
            box-sizing: border-box;
            position: relative;
            &:not(:first-of-type) {
              margin-left: -10 * @rem;
            }
          }
        }
        .more-game-btn {
          display: flex;
          align-items: center;
          .more-game-text {
            margin-left: 7 * @rem;
            white-space: nowrap;
            font-weight: 400;
            font-size: 12 * @rem;
            color: #191b1f;
            .num {
              margin: 0 2 * @rem;
              font-weight: 600;
              font-size: 13 * @rem;
              color: #191b1f;
            }
          }
          .more-game-arrow {
            margin-left: 4 * @rem;
            width: 10 * @rem;
            height: 10 * @rem;
            background-size: 100% 100%;
          }
        }
      }
    }
    .guess-you-like {
      margin: 12 * @rem auto 24 * @rem;
      width: 351 * @rem;
      height: 88 * @rem;
      background: url(~@/assets/images/home/<USER>/new-game-bg-like.png)
        no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .you-like-box {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 10 * @rem 0 12 * @rem;
        box-sizing: border-box;
        .like-list {
          display: flex;
          align-items: center;
          position: relative;
          width: 184 * @rem;
          overflow-x: auto;
          &::-webkit-scrollbar {
            display: none;
          }
          .list-item {
            flex-shrink: 0;
            width: 44 * @rem;
            height: 44 * @rem;
            border-radius: 10 * @rem;
            border: 2 * @rem solid #ffffff;
            overflow: hidden;
            background: #eeeeee;
            box-sizing: border-box;
            position: relative;
            &:not(:first-of-type) {
              margin-left: -10 * @rem;
            }
          }
        }
        .you-like-text {
          margin-left: 7 * @rem;
          white-space: nowrap;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #191b1f;
          .num {
            margin: 0 2 * @rem;
            font-weight: 600;
            font-size: 13 * @rem;
            color: #191b1f;
          }
        }
        .you-like-arrow {
          margin-left: 4 * @rem;
          width: 10 * @rem;
          height: 10 * @rem;
          background-size: 100% 100%;
        }
      }
    }
  }
  .game-list {
    padding: 0 12 * @rem;

    .game-item {
      display: flex;
      align-items: center;

      .btn {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 64 * @rem;
        height: 30 * @rem;
        background: @themeBg;
        border-radius: 19 * @rem;
        color: #fff;
        line-height: 30 * @rem;
        text-align: center;
        font-weight: 500;
        font-size: 12 * @rem;
      }
    }
  }
  .filter-container {
    box-sizing: border-box;
    height: 500 * @rem;

    /deep/ .van-icon {
      position: absolute;
    }
    /deep/ .van-popup__close-icon::before {
      font-size: 14 * @rem;
    }
  }
  .new-game-filter {
    height: 100%;
    padding: 0 12 * @rem;
    display: flex;
    flex-direction: column;
    background: #f7f8fa;
    .popup-name {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 23 * @rem 0;
      span {
        position: relative;
        height: 25 * @rem;
        font-weight: bold;
        font-size: 20 * @rem;
        color: #333333;
        line-height: 25 * @rem;
        text-align: center;
        &::before {
          content: '';
          display: block;
          width: 18 * @rem;
          height: 18 * @rem;
          border-radius: 50%;
          background-color: rgba(33, 185, 138, 0.23);
          position: absolute;
          top: 50%;
          left: -7 * @rem;
          transform: translateY(-50%);
        }
      }
    }
    .nav-list {
      display: flex;
      justify-content: space-between;
      width: 351 * @rem;
      height: 32 * @rem;
      background-color: #f0f1f5;
      border-radius: 8 * @rem;
      margin-bottom: 10 * @rem;

      .nav-item {
        width: 104 * @rem;
        height: 32 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 400;
        font-size: 13 * @rem;
        color: #333333;
        line-height: 32 * @rem;
        text-align: center;

        &.active {
          background-color: @themeColor;
          color: #fff;
          font-weight: bold;
          border-radius: 6 * @rem;
        }
      }
    }
    .game-container {
      flex: 1;
      min-height: 0;
      // padding-right: 5 * @rem;
      // margin-right: -10 * @rem;
      overflow-y: auto;
      &::-webkit-scrollbar {
        display: none;
      }
      .new-game-list {
        .list-item {
          background: #ffffff;
          border-radius: 12 * @rem;
          padding: 0 12 * @rem;
          margin-bottom: 12 * @rem;

          .title {
            display: flex;
            align-items: center;
            padding: 14 * @rem 0 16 * @rem 0;
            height: 20 * @rem;
            font-weight: 600;
            font-size: 16 * @rem;
            color: #333333;
            line-height: 20 * @rem;

            &::before {
              content: '';
              display: block;
              width: 4 * @rem;
              height: 10 * @rem;
              border-radius: 16 * @rem;
              background-color: @themeColor;
              margin-right: 6 * @rem;
            }

            span {
              display: block;
              height: 18 * @rem;
              line-height: 18 * @rem;
              padding: 0 6 * @rem;
              background: rgba(231, 231, 231, 0.3);
              border-radius: 2 * @rem;
              font-size: 11 * @rem;
              color: #333333;
              margin-left: 5 * @rem;
            }
          }
          .game-item {
            display: flex;
            align-items: center;
            // margin-bottom: 20 * @rem;
            padding-bottom: 20 * @rem;
            /deep/ .game-item-components {
              padding: 0;
            }

            .server-info {
              margin-right: 8 * @rem;
              font-size: 11 * @rem;
              line-height: 14 * @rem;
              color: #999;
            }
          }
          .more-new-game {
            margin: 0 auto 16 * @rem;
            box-sizing: border-box;
            height: 75 * @rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .more-game-box {
              border-top: 1px solid #f0f1f5;
              flex: 1;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: space-between;
              box-sizing: border-box;
              .more-game-list {
                display: flex;
                align-items: center;
                position: relative;
                width: 248 * @rem;
                overflow-x: auto;
                &::-webkit-scrollbar {
                  display: none;
                }
                .more-game-item {
                  flex-shrink: 0;
                  width: 44 * @rem;
                  height: 44 * @rem;
                  border-radius: 10 * @rem;
                  border: 2 * @rem solid #ffffff;
                  overflow: hidden;
                  background: #eeeeee;
                  box-sizing: border-box;
                  position: relative;
                  &:not(:first-of-type) {
                    margin-left: -10 * @rem;
                  }
                }
              }
              .more-game-btn {
                display: flex;
                align-items: center;
                .more-game-text {
                  margin-left: 7 * @rem;
                  white-space: nowrap;
                  font-weight: 400;
                  font-size: 12 * @rem;
                  color: #191b1f;
                  .num {
                    margin: 0 2 * @rem;
                    font-weight: 600;
                    font-size: 13 * @rem;
                    color: #191b1f;
                  }
                }
                .more-game-arrow {
                  margin-left: 4 * @rem;
                  width: 10 * @rem;
                  height: 10 * @rem;
                  background-size: 100% 100%;
                }
              }
            }
          }
          .only-more-game {
            margin: 0;
            .more-game-box {
              border-top: 0;
            }
          }
        }
      }
    }
    .close-btn {
      position: absolute;
      right: 21 * @rem;
      top: 22 * @rem;
      width: 15 * @rem;
      height: 15 * @rem;
      background-size: 100% 100%;
    }
  }
  .guess-you-like-container {
    box-sizing: border-box;
    height: 500 * @rem;
    .guess-you-like-filter {
      height: 100%;
      // padding: 0 18 * @rem;
      display: flex;
      flex-direction: column;
      background: #f7f8fa;
      .popup-name {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        padding: 22 * @rem 0;
        &.popup-bg {
          background: linear-gradient(180deg, #ebfff4 0%, #f7f8fa 100%);
        }
        span {
          position: relative;
          height: 20 * @rem;
          font-weight: 600;
          font-size: 16 * @rem;
          color: #191b1f;
          line-height: 20 * @rem;
          text-align: center;
          .num {
            margin: 0 5 * @rem;
          }
        }
      }
      .guess-you-like-list {
        .you-like-container {
          padding: 0 12 * @rem;
          .list-item {
            max-height: 436 * @rem;
            overflow-x: hidden;
            overflow-y: auto;
            &::-webkit-scrollbar {
              display: none;
            }
            .game-item {
              width: 351 * @rem;
              height: 104 * @rem;
              background: #ffffff;
              border-radius: 12 * @rem;
              box-sizing: border-box;
              display: flex;
              align-items: center;
              padding: 16 * @rem 12 * @rem;
              margin-bottom: 12 * @rem;
              &.no-margin-b12 {
                margin-bottom: 0;
              }
            }
          }
        }
      }
      .close-btn {
        position: absolute;
        right: 12 * @rem;
        top: 14 * @rem;
        width: 18 * @rem;
        height: 18 * @rem;
        background-size: 100% 100%;
      }
    }
    /deep/ .van-icon {
      position: absolute;
    }
    /deep/ .van-popup__close-icon::before {
      font-size: 14 * @rem;
    }
  }
}
.game-page-before {
  &:before {
    height: calc(85 * @rem + @safeAreaTop);
    height: calc(85 * @rem + @safeAreaTopEnv);
  }
}
</style>
