<template>
  <div class="sug-info-box">
    <div class="sug-info-item" v-for="(item, index) in sugList" :key="item.id">
      <div class="sug-top-box" v-if="index == 0">
        <template v-if="item.is_game == 1">
          <game-item-5
            :gameInfo="item.game[0]"
            :showBtnDirect="true"
            @child-click="childClick(item)"
          ></game-item-5>
        </template>
        <template v-if="item.is_game == 0">
          <div
            class="sug-item-type2"
            v-for="(type, typeIndex) in item.type"
            :key="typeIndex"
            @click="oneKeyBtn(item, index, typeIndex)"
          >
            <div class="item-box">
              <div class="type1">
                <img src="~@/assets/images/search/btn_search_empty.png" alt=""
              /></div>
              <div class="title1">{{ type.title }}</div>
            </div>
            <div class="onekey-btn">
              <div>一键直达</div>
              <div class="arrow-icon">
                <img
                  src="~@/assets/images/search/search-right-arrow.png"
                  alt=""
                />
              </div>
            </div>
          </div>
        </template>
      </div>
      <div class="sug-list-box" @click="oneKeyBtn(item, index)" v-else>
        <div class="sug-item-type3">
          <div class="search-icon">
            <img src="~@/assets/images/search/search-sug-icon.png" alt="" />
          </div>
          <div class="title-msg">
            <span>{{ item.main_title }}</span
            ><span v-if="item.subtitle">-{{ item.subtitle }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { navigateToGameDetail } from '@/utils/function';
export default {
  name: 'sugInfoItem',
  props: {
    sugList: {
      type: Array,
      default: [],
    },
  },

  data() {
    return {};
  },
  created() {},
  methods: {
    oneKeyBtn(item, index, typeIndex) {
      if (index === 0) {
        if (item.is_game === 1) {
          navigateToGameDetail(item.game[0]);
          this.$emit('addHistoryItem', { type: 1, ...item.game[0] });
        } else {
          this.toPage('Category', { info: item.type[typeIndex] });
          this.$emit('addHistoryItem', { type: 901, ...item.type[0] });
        }
      } else {
        const newItem = { ...item };
        if (newItem.subtitle) {
          newItem.main_title += `-${newItem.subtitle}`;
        }
        this.$emit('oneKeyBtn', item);
        this.$emit('addHistoryItem', { type: -1, ...newItem });
      }
    },
    childClick(item) {
      this.$emit('addHistoryItem', { type: 1, ...item.game[0] });
    },
  },
};
</script>

<style lang="less" scoped>
.sug-info-box {
  .sug-info-item {
    padding: 0 18 * @rem;
    .sug-top-box {
      .sug-item-type1,
      .sug-item-type2 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .item-box {
          display: flex;
          align-items: center;

          .item-img {
            width: 56 * @rem;
            height: 56 * @rem;
            border-radius: 12 * @rem;
            overflow: hidden;
          }
          .item-info {
            margin-left: 8 * @rem;
            .title-box {
              display: flex;
              align-items: center;
              width: 183 * @rem;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              .title {
                font-weight: 600;
                font-size: 16 * @rem;
                color: #111111;
              }
              .sub_label {
                margin-left: 6 * @rem;
                border-radius: 4 * @rem;
                border: 1px solid #e0e0e0;
                font-weight: 400;
                font-size: 11 * @rem;
                color: #808080;
                padding: 2 * @rem 4 * @rem;
                box-sizing: border-box;
                white-space: nowrap;
                overflow: hidden;
              }
            }
            .info-center {
              margin-top: 9 * @rem;
              display: flex;
              align-items: center;
              .types {
                display: flex;
                align-items: center;
                height: 17 * @rem;
                font-size: 12 * @rem;
                color: #797979;
                .type {
                  padding: 0 5 * @rem;
                  position: relative;
                  display: flex;
                  align-items: center;
                  &:not(:first-child) {
                    &:before {
                      content: '';
                      position: absolute;
                      left: 0;
                      top: 50%;
                      transform: translateY(-50%);
                      width: 1 * @rem;
                      height: 10 * @rem;
                      background-color: #929292;
                    }
                  }
                  &:first-child {
                    padding: 0 5 * @rem 0 0;
                  }
                }
              }
            }
          }
          .type1 {
            width: 27 * @rem;
            height: 16 * @rem;
          }
          .title1 {
            margin-left: 8 * @rem;
            font-weight: 600;
            font-size: 16 * @rem;
            color: #000000;
          }
        }
        .onekey-btn {
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #999999;
          flex-shrink: 0;
          .arrow-icon {
            margin-left: 2 * @rem;
            width: 8 * @rem;
            height: 8 * @rem;
          }
        }
      }
      .sug-item-type2 {
        height: 27 * @rem;
        line-height: 27 * @rem;
      }
    }
    .sug-list-box {
      .sug-item-type3 {
        display: flex;
        align-items: center;
        .search-icon {
          width: 24 * @rem;
          height: 24 * @rem;
        }
        .title-msg {
          margin-left: 8 * @rem;
          width: 186 * @rem;
          font-weight: 400;
          font-size: 15 * @rem;
          color: #222222;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    &:not(:first-child) {
      margin-top: 8 * @rem;
      padding-bottom: 8 * @rem;
    }
  }
}
</style>
