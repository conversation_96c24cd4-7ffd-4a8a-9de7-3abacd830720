<template>
  <div class="comment-tab">
    <!-- <div class="scores-box" v-if="rating.rating">
      <div class="comment-scores">
        <div class="score-left">
          <div class="score-num">
            <span>{{ rating.rating }}</span
            >{{ $t('分') }}
          </div>
          <div class="total">{{ $t('满分10分') }}</div>
          <div class="number">{{ rating.user_num }}评价</div>
        </div>
        <div class="score-right">
          <div class="star-list">
            <div class="star-item" v-for="(item, key) in starList" :key="key">
              <div class="star-row">
                <div
                  class="star-current"
                  :style="{
                    width: `${(5 - key.substring(key.length - 1)) * 20}%`,
                  }"
                ></div>
              </div>
              <div class="score-progress">
                <div
                  class="progress-current"
                  :style="{ width: `${item}%` }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div> -->

    <!-- 广告位 -->
    <div class="banner-fragment" v-if="bannerFragment.length">
      <ad-banner
        :bannerList="bannerFragment"
        :width="375 - bannerFragment[0].padding * 2"
        :height="
          (375 - bannerFragment[0].padding * 2) * bannerFragment[0].scale
        "
        :type="bannerFragment[0].type"
      >
      </ad-banner>
    </div>
    <!-- <div class="total-score-star" v-if="rating.rating">
      {{ rating.user_num }}{{ $t('个评分') }}
    </div> -->
    <!-- <div class="order-list">
      <div
        class="order-item"
        :class="{ current: order == item.order }"
        v-for="(item, index) in orderList"
        :key="index"
        @click="clickOrder(item.order)"
      >
        {{ item.name }}
      </div>
    </div> -->
    <!-- 我的评价 -->

    <div class="my-comments" v-if="myCommentsList.length">
      <div class="top-title">
        <div class="title">我的评价</div>
      </div>
      <div
        class="comment-list my-comment-list"
        v-for="item in myCommentsList"
        :key="`${item.comment_id}-my`"
      >
        <comment-item-2
          class="item"
          :comment="item"
          :gameId="gameId"
          :showMyCommentRevise="true"
          :showScoreTime="true"
        ></comment-item-2>
      </div>
    </div>
    <div class="comment-wrap">
      <div class="title"> 玩家评价 </div>
      <div class="comment-tab-list">
        <div
          class="comment-item active"
          :class="{ current: order == item.order }"
          v-for="(item, index) in orderList"
          :key="index"
          @click="clickOrder(item.order)"
          >{{ item.name }}</div
        >
      </div>
    </div>
    <content-empty
      class="centered"
      v-if="empty"
      :tips="tips"
      :emptyImg="emptyImg"
    ></content-empty>
    <load-more
      v-else
      v-model="loading"
      :finished="finished"
      @loadMore="loadMore"
      :check="false"
    >
      <div class="comment-list">
        <template v-for="item in topList">
          <comment-item-2
            class="item"
            :comment="item"
            :showScoreTime="true"
            v-if="order == 0"
            :key="`${item.comment_id}-top`"
          ></comment-item-2>
        </template>
        <template v-for="item in hotList">
          <comment-item-2
            class="item"
            :comment="item"
            :showScoreTime="true"
            v-if="order == 0"
            :key="`${item.comment_id}-hot`"
          ></comment-item-2>
        </template>
        <template v-for="item in commentList">
          <comment-item-2
            class="item"
            :comment="item"
            :showScoreTime="true"
            :key="`${item.comment_id}`"
          ></comment-item-2>
        </template>     
      </div>
    </load-more>
  </div>
</template>
<script>
import { ApiCommentComments } from '@/api/views/comment.js';
import AdBanner from '../ad-banner';
import CommentItem2 from '@/components/comment-item-2';
import emptyImg from '@/assets/images/games/comment-empty-img.png';
import { mapGetters } from 'vuex';
export default {
  name: 'CommentList',
  components: {
    AdBanner,
    CommentItem2,
  },
  data() {
    return {
      topList: [],
      hotList: [],
      commentList: [],
      myCommentsList: [], //我的评论
      bannerFragment: [], //广告位
      page: 1,
      listRows: 10,
      gameId: '',
      rating: {},
      cmt_sum: 0,
      loading: false,
      finished: false,
      empty: false,
      order: 0,
      orderList: [
        {
          name: this.$t('默认'),
          order: 0,
        },
        {
          name: this.$t('最新'),
          order: 2,
        },
      ],
      tips: '期待你的首次评价',
      emptyImg,
    };
  },
  computed: {
    starList() {
      let percentName = Object.keys(this.rating).filter(key =>
        /percent/.test(key),
      );
      let obj = {};
      percentName.forEach(item => {
        obj[item] = this.rating[item];
      });
      return obj;
    },
    ...mapGetters({
      gameInfo: 'game/gameInfo',
    }),
  },
  async created() {
    this.gameId = this.$route.params.id;
    this.rating = this.gameInfo.rating;
    this.loading = true;
    await this.getCommentList();
  },
  methods: {
    async clickOrder(order) {
      this.page = 1;
      this.finished = false;
      this.loading = true;
      this.order = order;
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      await this.getCommentList();
      this.$toast.clear();
    },
    async getCommentList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiCommentComments({
        page: this.page,
        listRows: this.listRows,
        classId: 103,
        sourceId: this.gameId,
        order: this.order,
      });
      let { hots, comments, rating, tops, cmt_sum, my_comments, ad_list } =
        res.data;
      this.bannerFragment = ad_list || [];
      this.rating = rating;
      this.cmt_sum = cmt_sum;
      this.myCommentsList = my_comments || [];
      if (cmt_sum) {
        this.$emit('updateCommentSum', cmt_sum);
      }
      if (action === 1 || this.page === 1) {
        this.commentList = [];
        if (tops && tops.length) {
          this.topList = tops;
        }
        if (hots && hots.length) {
          this.hotList = hots;
        }
        if (!comments.length) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      }
      this.commentList.push(...comments);
      if (comments.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
      this.$nextTick(() => {
        this.loading = false;
      });
    },
    async loadMore() {
      await this.getCommentList(2);
    },
  },
};
</script>

<style lang="less" scoped>
.comment-tab {
  height: 100%;
  .my-comments {
    background: #f7f8fa;
    padding-bottom: 10 * @rem;
    .top-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 19 * @rem 12 * @rem 0;
      background: #ffffff;
      .title {
        font-weight: 600;
        font-size: 16 * @rem;
        color: #191b1f;
      }
    }
    .my-comment-list {
      margin-bottom: 0;
      padding: 0 12 * @rem;
      &:not(:last-child) {
        .item {
          border-bottom: 1px solid #f9f9f9;
        }
      }
      /deep/.bottom-info {
        padding: 14 * @rem 0;
      }
    }
  }
  .comment-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 19 * @rem 12 * @rem 0;
    background: #ffffff;
    .title {
      font-weight: 600;
      font-size: 16 * @rem;
      color: #191b1f;
    }
    .comment-tab-list {
      width: 87 * @rem;
      height: 25 * @rem;
      background: #f0f1f5;
      border-radius: 12 * @rem;
      display: flex;
      align-items: center;
      padding: 0 2 * @rem;
      .comment-item {
        flex: 1;
        text-align: center;
        color: #60666c;
        font-size: 12 * @rem;
        &.current {
          width: 40 * @rem;
          height: 22 * @rem;
          line-height: 22 * @rem;
          color: #191b1f;
          background: #ffffff;
          border-radius: 12 * @rem;
        }
      }
    }
  }
  .centered {
    height: calc(100% - 44 * @rem);
    display: flex;
    flex-direction: column;
    align-content: center;
    justify-content: center;
  }
  .order-list {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    width: 148 * @rem;
    .order-item {
      width: 74 * @rem;
      height: 42 * @rem;
      font-size: 16 * @rem;
      color: #9a9a9a;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      &:not(:first-of-type) {
        &::before {
          content: '';
          width: 1 * @rem;
          height: 7 * @rem;
          background-color: #c1c1c1;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      &.current {
        color: #000000;
        font-weight: 500;
        &::after {
          content: '';
          width: 10 * @rem;
          height: 3 * @rem;
          background-color: @themeColor;
          border-radius: 2 * @rem;
          position: absolute;
          left: 50%;
          bottom: 0;
          transform: translateX(-50%);
        }
      }
    }
  }
  .scores-box {
    background: #f7f8fa;
    padding: 12 * @rem 0;
    .comment-scores {
      display: flex;
      width: 336 * @rem;
      height: 83 * @rem;
      background: #ffffff;
      border-radius: 8 * @rem;
      padding: 0 16 * @rem;
      box-sizing: border-box;
      margin: 0 auto;
      .score-left {
        width: 136 * @rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .score-num {
          font-size: 16 * @rem;
          color: #1cce94;
          font-weight: 600;
          span {
            font-size: 30 * @rem;
            color: #1cce94;
            font-weight: 600;
          }
        }
        .total {
          font-size: 11 * @rem;
          color: #9a9a9a;
          font-weight: 400;
          margin-top: 4 * @rem;
        }
        .number {
          margin-top: 8 * @rem;
          font-weight: 400;
          font-size: 11 * @rem;
          color: #2bbe88;
        }
      }
      .score-right {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .star-list {
          .star-item {
            display: flex;
            align-items: center;
            &:not(:first-of-type) {
              margin-top: 5 * @rem;
            }
            .star-row {
              width: 60 * @rem;
              height: 8 * @rem;
              background: url(~@/assets/images/games/star-row.png) center center
                no-repeat;
              background-size: 56 * @rem 8 * @rem;
              .star-current {
                width: 20%;
                height: 100%;
                background-color: #fff;
              }
            }
            .score-progress {
              width: 132 * @rem;
              height: 4 * @rem;
              border-radius: 3 * @rem;
              background-color: #d8d8d8;
              margin-left: 8 * @rem;
              .progress-current {
                width: 100%;
                height: 100%;
                background-color: @themeColor;
                border-radius: 3 * @rem;
              }
            }
          }
        }
      }
    }
  }
  .banner-fragment {
    box-sizing: border-box;
    background-color: #fff;
    overflow: hidden;
    /deep/ .swiper-slide {
      border-radius: 0 !important;
    }
  }
  .total-score-star {
    padding-right: 43 * @rem;
    text-align: right;
    font-size: 10 * @rem;
    font-weight: 400;
    color: #797979;
    height: 30 * @rem;
    line-height: 30 * @rem;
  }
}
.comment-list {
  box-sizing: border-box;
  padding: 0 12 * @rem;
  background: #ffffff;
  .item {
    padding-top: 16 * @rem;
    &:not(:last-child) {
      padding-bottom: 16 * @rem;
      border-bottom: 1px solid #f9f9f9;
    }
  }
  width: 100%;
  margin-bottom: 32 * @rem;
}
</style>
