<template>
  <div class="welfare-center-page">
    <div class="main">
      <!-- 每日任务 -->
      <div
        class="task-container"
        v-if="dailyTaskList && Object.keys(dailyTaskList).length"
      >
        <div class="task-title">
          <img
            src="@/assets/images/welfare/welfare-center/task-title-5.png"
            alt=""
          />
        </div>
        <div class="task-list">
          <div
            class="task-item"
            v-for="(item, key) in dailyTaskList"
            :key="key"
          >
            <div class="icon">
              <img :src="item.icon" alt="" />
            </div>
            <div class="task-info">
              <div class="title">{{ item.title }}</div>
              <div class="line">
                完成得
                <div v-if="item.gold_num">
                  <span>金币</span>
                  <span class="reward-info">+{{ item.gold_num }}</span>
                  <span v-if="item.coupon || item.exp_num">、</span>
                </div>
                <div v-if="item.coupon">
                  <span class="reward-info">{{ item.coupon }}</span>
                  <span v-if="item.exp_num">、</span>
                </div>
                <div v-if="item.exp_num">
                  <span>经验</span>
                  <span class="reward-info">+{{ item.exp_num }}</span>
                </div>
                <!-- <div class="gold" v-if="item.gold">{{ item.gold }}</div>
                <div class="coupon" v-if="item.coupon">{{ item.coupon }}</div>
                <div class="exp" v-if="item.exp">{{ item.exp }}</div> -->
              </div>
              <!-- <div class="extra" v-if="item.extra_exp">
                会员加成：{{ item.extra_exp }}
              </div> -->
            </div>
            <div
              class="task-btn btn"
              v-if="item.is_finish == 0"
              @click.stop="handleGo1(item, key)"
            >
              {{ $t('去完成') }}
            </div>
            <div
              class="task-btn btn get"
              v-if="item.is_finish == 1"
              @click.stop="handleGet1(item, key)"
            >
              {{ $t('领取') }}
            </div>

            <div class="task-btn btn had" v-else-if="item.is_finish == 2">
              {{ $t('已领取') }}
            </div>
          </div>
        </div>
      </div>
      <!-- 成就任务 -->
      <div
        class="task-container"
        v-if="achievementTaskList && Object.keys(achievementTaskList).length"
      >
        <div class="task-title">
          <img
            src="@/assets/images/welfare/welfare-center/task-title-6.png"
            alt=""
          />
        </div>
        <div class="task-list">
          <!-- 签到 -->
          <template v-if="achievementTaskList.sign">
            <div class="task-item">
              <div class="icon">
                <img :src="achievementTaskList.sign.mission_info.icon" alt="" />
              </div>
              <div class="task-info">
                <div class="title">
                  累计签到
                  {{ achievementTaskList.sign.mission_info.need_value }}次 ({{
                    achievementTaskList.sign.total
                  }}/{{ achievementTaskList.sign.mission_info.need_value }})
                </div>
                <!-- <div class="subtitle">
                  ({{ achievementTaskList.sign.total }}/{{
                    achievementTaskList.sign.mission_info.need_value
                  }})
                </div> -->
                <div class="line">
                  <!-- <div class="gold" v-if="achievementTaskList.sign.mission_info.reward">
                    {{ achievementTaskList.sign.mission_info.reward }}金币
                  </div> -->
                  <div v-if="achievementTaskList.sign.mission_info.reward">
                    完成得<span>金币</span>
                    <span class="reward-info"
                      >+{{ achievementTaskList.sign.mission_info.reward }}</span
                    >
                  </div>
                </div>
              </div>

              <template
                v-if="
                  !Boolean(achievementTaskList.sign.mission_info.all_finish)
                "
              >
                <div
                  class="task-btn btn get"
                  v-if="
                    achievementTaskList.sign.total >=
                    achievementTaskList.sign.mission_info.need_value
                  "
                  @click="
                    handleGet2(achievementTaskList.sign.mission_info, 'sign')
                  "
                >
                  {{ $t('领取') }}
                </div>
                <div
                  class="task-btn btn"
                  v-else
                  @click.stop="handleGo2(achievementTaskList.sign, 'sign')"
                >
                  {{ $t('去完成') }}
                </div>
              </template>
              <div class="task-btn btn had" v-else>{{ $t('已领取') }}</div>
            </div>
          </template>
          <!-- 充值 -->
          <template v-if="achievementTaskList.pay">
            <template
              v-for="(item, index) in achievementTaskList.pay.mission_info"
            >
              <div
                class="task-item"
                :key="index"
                v-if="!(index > 1 && !isExpand)"
              >
                <div class="icon">
                  <img :src="item.icon" alt="" />
                </div>
                <div class="task-info">
                  <div class="title">
                    {{ item.name }} ({{ achievementTaskList.pay.total }}/{{
                      item.need_value
                    }})
                  </div>
                  <!-- <div class="subtitle">
                    ({{ achievementTaskList.pay.total }}/{{ item.need_value }})
                  </div> -->
                  <div class="line">
                    <!-- <div class="gold" v-if="item.reward">
                      {{ item.reward }}金币
                    </div> -->
                    <div v-if="item.reward">
                      完成得<span>金币</span>
                      <span class="reward-info">+{{ item.reward }}</span>
                    </div>
                  </div>
                </div>

                <div
                  class="task-btn btn get"
                  v-if="item.is_finish == 1"
                  @click="handleGet2(item, 'pay')"
                >
                  {{ $t('领取') }}
                </div>
                <div
                  class="task-btn btn"
                  v-else
                  @click.stop="handleGo2(item, 'pay')"
                >
                  {{ $t('去完成') }}
                </div>
              </div>
            </template>
          </template>
          <template v-if="achievementTaskList.pay.mission_info.length + 1 > 3">
            <div
              class="exchange-down"
              v-if="!isExpand"
              @click="isExpand = !isExpand"
            >
              更多任务<i></i>
            </div>
            <div class="exchange-down up" v-else @click="isExpand = !isExpand">
              收起<i></i>
            </div>
          </template>
        </div>
      </div>
      <!-- 新手任务 -->
      <div
        class="task-container"
        v-if="newbieTaskList && Object.keys(newbieTaskList).length"
      >
        <div class="task-title">
          <img
            src="@/assets/images/welfare/welfare-center/task-title-7.png"
            alt=""
          />
        </div>
        <div class="task-list">
          <div
            class="task-item"
            v-for="(item, key) in newbieTaskList"
            :key="key"
          >
            <div class="icon">
              <img :src="item.icon" alt="" />
            </div>
            <div class="task-info">
              <div class="title">{{ item.title }}</div>
              <div class="line">
                完成得
                <div v-if="item.gold_num">
                  <span>金币</span>
                  <span class="reward-info">+{{ item.gold_num }}</span>
                  <span v-if="item.coupon || item.exp_num">、</span>
                </div>
                <div v-if="item.coupon">
                  <span class="reward-info">{{ item.coupon }}</span>
                  <span v-if="item.exp_num">、</span>
                </div>
                <div v-if="item.exp_num">
                  <span>经验</span>
                  <span class="reward-info">+{{ item.exp_num }}</span>
                </div>
                <!-- <div class="gold" v-if="item.gold">{{ item.gold }}</div>
                <div class="coupon" v-if="item.coupon">{{ item.coupon }}</div>
                <div class="exp" v-if="item.exp">{{ item.exp }}</div> -->
              </div>
              <!-- <div class="extra" v-if="item.extra_exp">
                会员加成：{{ item.extra_exp }}
              </div> -->
            </div>
            <div
              class="task-btn btn"
              v-if="item.is_finish == 0"
              @click.stop="handleGo3(item, key)"
            >
              {{ $t('去完成') }}
            </div>
            <div
              class="task-btn btn get"
              v-if="item.is_finish == 1"
              @click.stop="handleGet3(item, key)"
            >
              {{ $t('领取') }}
            </div>

            <div class="task-btn btn had" v-else-if="item.is_finish == 2">
              {{ $t('已领取') }}
            </div>
          </div>
        </div>
      </div>
      <div
        class="top-bar-container section-container"
        v-if="goldBoxList.length"
      >
        <div class="clock-in-rule" @click="openRulePopup"></div>
        <div class="top-reward" v-sensors-exposure="clockInExposure()">
          <div class="reward-title"></div>
          <div class="reward-subtitle"
            >SVIP会员金币奖励翻倍，连续签到28天宝箱奖励3733金币</div
          >
          <div class="reward-list">
            <!-- state 0:不可领取 1:可领取 2:已领取 -->
            <div
              class="reward-item"
              :class="{ last: index === goldBoxList.length - 1 }"
              v-for="(item, index) in goldBoxList"
              :key="item.gold"
              @click="handleGet(item)"
            >
              <div class="days">{{ item.title }}</div>
              <div
                class="reward-icon"
                :style="{ backgroundImage: `url(${item.icon})` }"
              ></div>

              <div class="coins can" v-if="item.state == 1">
                {{ $t('可领取') }}
              </div>
              <div class="coins had" v-else-if="item.state == 2">
                {{ $t('已领取') }}
              </div>
              <div class="coins" v-else>
                {{ item.gold_text }}
              </div>
            </div>
          </div>
        </div>
        <div class="clock-in-info">
          <div class="operate-info">
            <div>
              <div class="total-days">
                <span>{{ continousDay }}</span
                >(本月已连续签到天数)
              </div>
              <div class="record-item">
                {{ $t('剩余补签次数') }}：<span>{{ surplusRepairNum }}</span>
              </div>
            </div>
            <div
              class="clock-in-btn btn"
              :class="{ had: isSignIn(currentDay) }"
              @click="clockIn"
            >
              <span v-if="isSignIn(currentDay)"></span>
              {{ clockInBtnText }}
            </div>
          </div>
        </div>
        <!-- 日历 -->
        <div class="calendar-container">
          <div class="title">
            {{ currentYear }}{{ $t('年') }}{{ currentMonth + 1 }}{{ $t('月') }}
          </div>
          <div class="content">
            <div class="calendar-top" v-if="calendarOpen">
              <div
                class="top-item"
                v-for="(item, index) in calendarTop"
                :key="index"
              >
                {{ item }}
              </div>
            </div>
            <div class="day-list">
              <template v-for="item in prevLeftDays">
                <div
                  class="day-item day-pre"
                  :key="'pre' + item"
                  v-if="
                    thisWeekList.some(
                      v =>
                        v.day == prevDays + item - prevLeftDays &&
                        v.month != currentMonth + 1,
                    ) || calendarOpen
                  "
                >
                  <div class="day-btn">
                    <div class="num">
                      {{ prevDays + item - prevLeftDays }}
                    </div>
                  </div>
                </div>
              </template>
              <template v-for="item in currentDays">
                <div
                  class="day-item"
                  :key="'cur' + item"
                  v-if="
                    thisWeekList.some(
                      v => v.day == item && v.month == currentMonth + 1,
                    ) || calendarOpen
                  "
                >
                  <div
                    class="day-btn btn"
                    @click="repairSign(item)"
                    :class="{
                      had: isSignIn(item),
                      can: canSignIn(item),
                      today: item == currentDay,
                    }"
                  >
                    <div class="num">
                      {{ item }}
                    </div>

                    <div class="text" v-if="canSignIn(item) || isSignIn(item)">
                      {{ isSignIn(item) ? '' : $t('补签') }}
                    </div>
                    <div class="text" v-else></div>
                  </div>
                </div>
              </template>
              <template v-for="item in nextLeftDays">
                <div
                  class="day-item day-next"
                  :key="'next' + item"
                  v-if="
                    thisWeekList.some(
                      v => v.day == item && v.month != currentMonth + 1,
                    ) || calendarOpen
                  "
                >
                  <div class="day-btn">
                    <div class="num">{{ item }}</div>
                  </div>
                </div>
              </template>
            </div>
          </div>
          <div
            class="calendar-open"
            :class="{ close: calendarOpen }"
            @click="calendarOpen = !calendarOpen"
          ></div>
        </div>
      </div>
      <div class="placeholder" ref="signInContainer"></div>
    </div>
    <!-- 开屏签到弹窗 -->
    <van-dialog
      class="sign-in-popup-dislog"
      v-model="signInPopupShow"
      :show-confirm-button="false"
      :lock-scroll="false"
    >
      <div class="top-container">
        <div class="popup-title"></div>
        <div class="tips"
          >SVIP会员金币奖励翻倍<br />
          连续签到28天宝箱奖励 <span>3733</span> 金币</div
        >
      </div>
      <div class="reward-list">
        <!-- state 0:不可领取 1:可领取 2:已领取 -->
        <div
          class="reward-item"
          :class="{ last: index === goldBoxList.length - 1 }"
          v-for="(item, index) in goldBoxList"
          :key="item.gold"
          @click="handleGet(item)"
        >
          <div class="days">{{ item.title }}</div>
          <div
            class="reward-icon"
            :style="{ backgroundImage: `url(${item.icon})` }"
          ></div>

          <div class="coins can" v-if="item.state == 1">
            {{ $t('可领取') }}
          </div>
          <div class="coins had" v-else-if="item.state == 2">
            {{ $t('已领取') }}
          </div>
          <div class="coins" v-else>
            {{ item.gold_text }}
          </div>
        </div>
      </div>
      <div class="popup-main-box">
        <div class="day-list">
          <div
            class="day-item"
            :class="{
              had: item.status,
              gray: item.is_ban,
            }"
            v-for="(item, index) in weekList"
            :key="index"
            @click="handleRepair(item)"
          >
            <div class="day-between-icon"></div>
            <div class="day-icon" v-if="item.status == 1"></div>
            <div
              class="day-icon"
              :style="{ backgroundImage: `url(${item.icon})` }"
              v-else
            ></div>
            <div class="day-dot" :class="{ today: item.date == '今天' }">
              <div class="day-line-before"></div>
              <div class="day-line-after"></div>
            </div>
            <div class="text" :class="{ active: item.date == '今天' }">{{
              item.date
            }}</div>
          </div>
        </div>
        <div class="operation">
          <div
            class="clock-btn btn"
            @click="clickNormalSign"
            v-if="!userInfo.is_svip"
          >
            {{ $t('普通签到') }}
            <span>{{ common_gold_text }}</span>
          </div>
          <div
            class="clock-btn svip btn"
            @click="clickSvipSign"
            v-if="!userInfo.is_svip"
          >
            {{ $t('SVIP签到') }}
            <span>{{ svip_gold_text }}</span>
            <div class="svip-tips" v-if="svipTip">{{ svipTip }}</div>
          </div>
          <div class="svip-sign-in" @click="clickRichSign" v-else>豪华签到</div>
        </div>
        <div class="no-notice" :class="{ remember: signInRemember }">
          <div class="content" @click.stop="signInRemember = !signInRemember">
            <div class="gou"></div>
            {{ $t('今日不再提醒') }}
          </div>
        </div>
      </div>
      <div class="close-btn" @click="closeSignInPopup()"></div>
    </van-dialog>

    <!-- 任务弹窗 -->
    <task-popup
      :isShow.sync="taskPopup"
      :message="popupContent"
      :tip="popupTip"
      :confirmText="popupConfirmText"
      @confirm="popupConfirm"
    ></task-popup>

    <!-- 引导添加到桌面弹窗 -->
    <van-dialog
      v-model="yindaoPopup"
      :showConfirmButton="false"
      :showCancelButton="false"
      :lock-scroll="false"
      :close-on-click-overlay="true"
      class="yindao-popup"
    >
      <div class="close" @click="closeYinDaoPopup()"></div>
      <div class="text">{{ $t('为了更方便的连续签到领宝箱') }}</div>
      <div class="text">{{ $t('请添加到主屏幕哦') }}</div>
      <div class="down-arrow"></div>
    </van-dialog>

    <!-- 非svip点击签到弹窗 -->
    <van-dialog
      class="no-svip-sign-dialog"
      v-model="noSvipSignShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      :close-on-click-overlay="true"
    >
      <div class="top-icon"></div>
      <div class="no-svip-tip">
        <div class="line">{{ $t('开通SVIP') }}</div>
        <div
          class="small-tips"
          v-for="(item, index) in noSvipText"
          :key="index"
          >{{ item }}</div
        >
      </div>
      <div class="operation">
        <div class="clock-btn btn" @click="clickNormalSign">
          {{ $t('普通签到') }}
          <span>{{ common_gold_text }}</span>
        </div>
        <div class="clock-btn svip btn" @click="clickSvipSign">
          {{ $t('SVIP签到') }}
          <span>{{ svip_gold_text }}</span>
          <div class="svip-tips" v-if="svipTip">{{ svipTip }}</div>
        </div>
      </div>
      <div class="no-notice" :class="{ remember: remember }">
        <div class="content" @click.stop="remember = !remember">
          <div class="gou"></div>
          {{ $t('不再提醒') }}
        </div>
      </div>
      <div class="close-btn" @click="noSvipSignShow = false"></div>
    </van-dialog>

    <!-- 非svip点击补签弹窗 -->
    <van-dialog
      v-model="repairSignTipShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="repair-sign-tip-popup"
      @closed="closeRepairSignTipPopup"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="box-bg">
          <div class="title">{{ noSvipRepairSignTip }}</div>
        </div>
        <div class="btn-box">
          <div class="btn-cancel" @click="repairSignTipShow = false">
            {{ $t('取消') }}</div
          >
          <div class="btn-open" @click="toBuySvip"> {{ $t('立即开通') }}</div>
        </div>
      </div>
    </van-dialog>

    <!-- svip补签提示弹窗 -->
    <van-dialog
      v-model="svipRepairSignTipShow"
      :lock-scroll="false"
      :show-confirm-button="false"
      :close-on-click-overlay="true"
      class="svip-repair-sign-tip-popup"
      @closed="closeRepairSignTipPopup"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="close-btn" @click="svipRepairSignTipShow = false"></div>
        <div class="box-bg">
          <div class="title"> {{ repairSignTip }}</div>
          <div class="btn" @click="repairSigning"> {{ $t('补签') }}</div>
        </div>
      </div>
    </van-dialog>

    <!-- 签到成功弹窗 -->
    <van-dialog
      v-model="signSuccessShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="sign-dialog"
      @closed="openYindaoPopup()"
    >
      <div class="logo-icon"></div>
      <div class="dialog-content">
        <div class="title">恭喜获得</div>
        <div class="reward-box">
          <div class="reward-item" v-if="reward_info.exp">
            <div class="exp-icon"></div>
            <div class="reward-msg">
              <span class="msg-number"> {{ reward_info.exp }}</span>
              <span>经验</span>
            </div>
          </div>
          <div class="reward-item" v-if="reward_info.gold">
            <div class="gold-icon" :class="{ svip: userInfo.is_svip }"></div>
            <div class="reward-msg">
              <span class="msg-number"> {{ reward_info.gold }}</span>
              <span>金币</span>
            </div>
          </div>
          <div class="upgrade" v-if="!userInfo.is_svip">
            <div class="text">升级</div>
            <div class="up-icon"></div>
          </div>
          <div class="reward-item svip-more-gold" v-if="!userInfo.is_svip">
            <div class="gold-icon"></div>
            <div class="reward-msg">
              <span class="msg-number">
                {{ reward_info.gold + reward_info.svip_gold }}</span
              >
              <span>金币</span>
            </div>
          </div>
        </div>
        <div class="svip-tips" v-if="noSvipText && !userInfo.is_svip">
          <div class="tips-title">升级SVIP获得</div>
          <div
            class="small-tips"
            v-for="(item, index) in noSvipText"
            :key="index"
            >{{ item }}</div
          >
        </div>
        <div class="btn-list">
          <div class="btn" @click="closeSignSuccessShowPopup()">开心收下</div>
          <div class="btn" v-if="!userInfo.is_svip" @click="toSvip"
            >升级领取
            <div class="svip-tip" v-if="svipTip">{{ svipTip }}</div></div
          >
        </div>
        <div class="tips" v-if="userInfo.is_svip && reward_info.extra_tip">{{
          reward_info.extra_tip
        }}</div>
        <div
          class="dialog-close-btn"
          @click="closeSignSuccessShowPopup()"
        ></div>
      </div>
    </van-dialog>

    <div class="sign-popup-unset">
      <!-- 补签成功弹窗 -->
      <van-dialog
        v-model="repairSignSuccessShow"
        :show-confirm-button="false"
        :close-on-click-overlay="true"
        :lock-scroll="false"
        class="repair-sign-dialog"
      >
        <div class="logo-icon"></div>
        <div class="dialog-content">
          <div class="title">{{ $t('补签成功') }}</div>
          <div class="msg">
            <span v-if="repairGoldNum">
              <span>+</span>
              <span class="msg-number">{{ repairGoldNum }}</span>
              <span>{{ $t('金币') }}</span>
            </span>
          </div>
          <div class="btn" @click="closeRepairSignSuccessPopup()">{{
            $t('开心收下')
          }}</div>
          <div class="dialog-close-btn" @click="closeRepairSignSuccessPopup()">
          </div>
        </div>
      </van-dialog>
    </div>

    <!-- 连签奖励未完成 -->
    <van-dialog
      v-model="getAwardIncompletePopupShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="award-before-dialog"
    >
      <div class="dialog-content">
        <div class="title" :class="{ fzs: getAwardPopupInfo.gold > 999 }">{{
          getAwardPopupInfo.title
        }}</div>
        <div class="detail">
          <div
            class="double"
            v-if="userInfo.is_svip && getAwardPopupInfo.type != 'svip'"
            >已翻倍</div
          >
          <img :src="getAwardPopupInfo.icon" alt="" />
          <div class="gold-num">{{ getAwardPopupInfo.icon_text }}</div>
        </div>
        <div class="msg">
          <div class="msg-number">{{ getAwardPopupInfo.prompt }}</div>
          <div
            class="msg-number"
            v-if="
              !userInfo.is_svip &&
              getAwardPopupInfo.svip_text &&
              getAwardPopupInfo.type != 'svip'
            "
            >{{ getAwardPopupInfo.svip_text }}</div
          >
        </div>
        <div class="btns">
          <div class="btn" @click="getAwardIncompletePopupShow = false"
            >我知道了</div
          >
          <div class="btn" @click="toSvip" v-if="!userInfo.is_svip"
            >立即开通</div
          >
        </div>
        <div
          class="dialog-close-btn"
          @click="getAwardIncompletePopupShow = false"
        ></div>
      </div>
    </van-dialog>

    <!-- 连签奖励弹窗 -->
    <van-dialog
      v-model="getAwardPopupShow"
      :show-confirm-button="false"
      :lock-scroll="false"
      class="award-dialog"
      @closed="fetchGetGoldBoxAfter"
    >
      <div
        class="logo-icon"
        :class="{ svip: getAwardPopupInfo.type == 'svip' }"
        :style="{ backgroundImage: `url(${getAwardPopupInfo.icon})` }"
      ></div>
      <div class="dialog-content">
        <div class="title">恭喜获得</div>
        <div class="msg">
          <div class="msg-number">{{ getAwardPopupInfo.day_text }}</div>
          <div class="msg-number">{{ getAwardPopupInfo.prize_text }}</div>
          <div
            class="tips"
            v-if="!userInfo.is_svip && getAwardPopupInfo.svip_text"
            >{{ getAwardPopupInfo.svip_text }}</div
          >
        </div>
        <div class="btns">
          <div class="btn" @click="closeGetAwardPopupShow()">开心收下</div>
          <div
            class="btn"
            @click="toSvip"
            v-if="getAwardPopupInfo.type == 'svip'"
            >查看SVIP权益</div
          >
          <div class="btn" @click="toSvip" v-else-if="!userInfo.is_svip"
            >立即开通</div
          >
        </div>
        <div class="dialog-close-btn" @click="closeGetAwardPopupShow()"></div>
      </div>
    </van-dialog>

    <div class="sign-popup-unset">
      <!-- 宝箱分享弹窗 -->
      <van-dialog
        v-model="goldBoxShareShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :closeOnClickOverlay="true"
      >
        <div class="gold-box-share">
          <div class="close btn" @click="goldBoxShareShow = false"></div>
          <div class="top-text">{{ $t('恭喜你') }}</div>
          <div class="title">
            {{ $t('即将获得') }}<span>{{ goldBoxShareData.gold }}</span
            >{{ $t('金币') }}
          </div>
          <div class="tip">{{ goldBoxShareData.showText }}</div>
          <div class="sub-tip">{{ $t('新老用户皆可') }}</div>
          <div class="avatar">
            <img src="@/assets/images/clock-in/no-invite.png" alt="" />
          </div>
          <div class="operate">
            <div class="common-invite btn" @click="handleShare">
              {{ $t('立即邀请') }}
            </div>
            <div class="svip-invite btn" @click="goToSvip">
              {{ $t('SVIP免邀请') }}
            </div>
          </div>
        </div>
      </van-dialog>
    </div>

    <div class="sign-popup-unset">
      <!-- 宝箱分享成功弹窗 -->
      <!-- 2021年12月13日18:39:42 新需求：最后两个宝箱取消分享直接领取。 -->
      <van-dialog
        v-model="goldBoxShareSuccessShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :closeOnClickOverlay="true"
      >
        <div class="gold-box-share">
          <div class="close btn" @click="goldBoxShareSuccessShow = false"></div>
          <div class="top-text">{{ $t('恭喜你') }}</div>
          <div class="get-gold-num">
            {{ $t('获得') }}{{ goldBoxShareSuccessData.gold }}{{ $t('金币') }}
          </div>
          <div class="sub-tip help-man">{{ $t('助力好友') }}</div>
          <div class="avatar avatar-icon">
            <img :src="goldBoxShareSuccessData.avatar" alt="" />
          </div>
          <div class="operate">
            <div class="confirm btn" @click="goldBoxShareSuccessShow = false">
              {{ $t('我知道了') }}
            </div>
          </div>
        </div>
      </van-dialog>
    </div>

    <div class="sign-popup-unset">
      <!-- svip直接领取最后两个宝箱 -->
      <van-dialog
        v-model="goldBoxSvipSuccessShow"
        :lock-scroll="false"
        :show-confirm-button="false"
        :closeOnClickOverlay="true"
      >
        <div class="gold-box-share">
          <div class="close btn" @click="goldBoxSvipSuccessShow = false"></div>
          <div class="top-text orange">{{ $t('恭喜你') }}</div>
          <div class="sub-tip help-svip">
            {{ $t('SVIP无需好友助力即可领取') }}
          </div>
          <div class="get-gold-num svip-num">
            {{ goldBoxSvipSuccessData.gold }}{{ $t('金币') }}
          </div>
          <div class="operate">
            <div class="confirm btn" @click="goldBoxSvipSuccessShow = false">
              {{ $t('我知道了') }}
            </div>
          </div>
        </div>
      </van-dialog>
    </div>

    <!-- 签到规则弹窗 -->
    <van-action-sheet
      v-model="rulePopup"
      :lock-scroll="false"
      get-container="body"
      title=""
      class="rule-popup"
      @touchStart.stop=""
    >
      <div class="popup-content">
        <div class="close" @click="rulePopup = false"></div>
        <div class="popup-title">
          <img
            src="@/assets/images/welfare/welfare-center/clock-in-rule-title1.png"
            alt=""
          />
        </div>
        <div class="rule-content" v-html="rules" @click="clickRules"></div>
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
import { ApiCoinCenterWelfareCenter } from '@/api/views/welfare';
import {
  ApiUserClockIn,
  ApiUserActiveSign,
  ApiUserGetGoldBox,
  ApiUserRepairDeductGold,
  ApiUserRepairSign,
} from '@/api/views/users.js';
import { PageName, handleActionCode } from '@/utils/actionCode.js';
import {
  platform,
  BOX_showActivity,
  BOX_openInNewWindow,
  BOX_openInBrowser,
  BOX_goToGame,
  BOX_login,
  BOX_showActivityByAction,
} from '@/utils/box.uni.js';
import { navigateToGameDetail } from '@/utils/function';
import { getQueryVariable } from '@/utils/function.js';
import {
  ApiMissionGetMissionReward,
  ApiMissionGetAchievementReward,
  ApiMissionCheckToComplete,
} from '@/api/views/mission.js';
import { needGuide } from '@/utils/userAgent';
import TaskPopup from '@/components/task-popup';
import useCollectToast from '@/components/yy-collect-toast/index.js';
import { themeColorLess } from '@/common/styles/_variable.less';
import { mapActions, mapGetters } from 'vuex';
let defaultDate = new Date().getTime();
export default {
  name: 'Welfare',
  components: {
    TaskPopup,
  },
  data() {
    let that = this;
    return {
      navbarOpacity: 0,
      bgStyle: 'transparent-white',
      isToSignIn: false,
      // 签到相关
      goldBoxList: [],
      noSvipText: [],
      svipTip: '',
      common_gold_text: '+10金币',
      svip_gold_text: '+20金币',
      clockInDate: [], // 已签到的日期
      rules: '',
      maxDays: 0, // 本月最长连续签到
      continousDay: 0, // 目前连续签到天数
      surplusRepairNum: 0, // 剩余补签次数
      repairDate: [], // 可补签的日期
      calendarTop: [
        this.$t('周日'),
        this.$t('周一'),
        this.$t('周二'),
        this.$t('周三'),
        this.$t('周四'),
        this.$t('周五'),
        this.$t('周六'),
      ],
      currentDay: new Date(defaultDate).getDate(),
      currentMonth: new Date(defaultDate).getMonth(),
      currentYear: new Date(defaultDate).getFullYear(),
      isOnceSignInPopupShow: false, //是否已经触发签到弹窗
      signInPopupShow: false, // 签到弹窗
      signSuccessShow: false, // 签到成功弹窗
      points: [], // 签到成功信息
      reward_info: {}, // 签到成功信息
      repairSignTipShow: false, // 补签提示弹窗
      svipRepairSignTipShow: false, // svip补签提示弹窗(svip才可补签了)
      noSvipRepairSignTip: '', // 非svip补签提示信息
      repairSignTip: '', // 补签提示信息
      repairSignSuccessShow: false, // 补签成功弹窗
      repairSignDay: '', // 要补签的是哪一天
      // goldPoints: [], // 领取宝箱成功信息
      goldBoxSuccessShow: false, // 连续签到领宝箱弹窗
      goldBoxShareShow: false, // 宝箱分享弹窗
      goldBoxShareData: {}, // 宝箱分享信息
      shareInfo: {}, // 分享给好友的内容
      goldBoxShareSuccessShow: false, // 宝箱分享成功弹窗
      goldBoxShareSuccessData: {}, // 宝箱分享成功弹窗
      repairGoldNum: 0,
      goldBoxSvipSuccessShow: false, // svip免邀请直接领取宝箱的弹窗
      goldBoxSvipSuccessData: {}, // svip免邀请直接领取宝箱的信息
      yindaoPopup: false, //引导到添加主屏幕的弹窗
      noSvipSignShow: false, // 非svip签到弹窗
      signInRemember: false, //开屏弹窗是否记住不再提示
      remember: false, // 是否记住不再提示
      popup_msg_show: true, //是否同意打开消息提示弹窗（用于规则弹窗只弹一次）

      // boxSuccessShow: false, // 领取宝箱弹窗
      // boxPoints: [], // 领取宝箱信息

      calendarOpen: false, // 日历是否展开
      thisWeekList: [], // 今日这周的数组
      weekList: [],
      rulePopup: false,

      getAwardIncompletePopupShow: false,
      getAwardPopupShow: false,
      getAwardPopupInfo: {},

      // 任务区
      achievementTaskList: null,
      dailyTaskList: null,
      newbieTaskList: null,
      taskPopup: false, // 任务弹窗是否显示
      popupContent: '', // 任务弹窗内容
      popupTip: '', // 任务弹窗提示
      popupConfirmText: '', // 任务弹窗确认按钮文案
      isSvip: false,
      ptbUrl: '',
      vipUrl: '',
      isExpand: false,
      isSignPopupJump: false, //是否由签到弹窗打开的弹窗
    };
  },
  async activated() {
    this.init();
  },
  computed: {
    ...mapGetters({
      onActionData: 'system/onActionData',
    }),
    clockInBtnText() {
      let clockDate =
        this.currentDay < 10
          ? '0' + this.currentDay
          : this.currentDay.toString();
      return this.clockInDate.includes(clockDate)
        ? this.$t('已签到')
        : this.userInfo.is_svip
          ? this.$t('豪华签到')
          : this.$t('立即签到');
    },
    // 这个月天数
    currentDays() {
      return new Date(this.currentYear, this.currentMonth + 1, 0).getDate();
    },
    // 上个月天数
    prevDays() {
      return new Date(this.currentYear, this.currentMonth, 0).getDate();
    },
    // 上个月剩余天数
    prevLeftDays() {
      return new Date(this.currentYear, this.currentMonth, 1).getDay();
    },
    // 下个月显示的天数
    nextLeftDays() {
      return 7 - new Date(this.currentYear, this.currentMonth + 1, 1).getDay();
    },
  },
  watch: {
    signInPopupShow(val, oldVal) {
      if (!val) {
        this.handleSignInNotice();
      }
    },

    $route(to, from) {
      if (
        platform == 'android' &&
        from.name == to.name &&
        to.name == 'Welfare'
      ) {
        this.init();
      }
    },
  },
  methods: {
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    clockInExposure() {
      return {
        'event-name': 'sign_in_page_view',
        'property-title': this.$sensorsPageGet(),
        'property-url': this.$route.fullPath,
        'property-referrer': this.$sensorsChainGet(),
      };
    },
    async init() {
      this.SET_USER_INFO(true);
      await this.getThisWeekList();
      this.currentToken = this.userInfo.token;
      this.currentDay = new Date(defaultDate).getDate();
      this.currentMonth = new Date(defaultDate).getMonth();
      this.currentYear = new Date(defaultDate).getFullYear();

      await this.getWelfareIndex();
      if (this.$route.params.type == 1) {
        // 神策埋点 入口点击（进来满足参数才触发）
        this.$sensorsTrack('sign_in_entrence_click');
        await this.toSignIn();
        this.isToSignIn = false;
      }
    },
    toSignIn() {
      this.$nextTick(() => {
        let distance = this.$refs.signInContainer.offsetTop;
        window.scrollTo({
          left: 0,
          top: distance,
          behavior: 'smooth',
        });
      });
    },
    closeSignInPopup() {
      this.signInPopupShow = false;
    },
    closeRepairSignSuccessPopup() {
      this.repairSignSuccessShow = false;
    },
    closeSignSuccessShowPopup() {
      this.signSuccessShow = false;
    },
    closeGetAwardPopupShow() {
      this.getAwardPopupShow = false;
    },
    closeYinDaoPopup() {
      this.yindaoPopup = false;
    },
    async getWelfareIndex() {
      let res = await ApiCoinCenterWelfareCenter();
      let {
        gold,
        tab,
        achievementTasks,
        dailyTasks,
        newbieTasks,
        isSvip,
        ptbUrl,
        vipUrl,
      } = res.data;
      let {
        gold_box_list,
        clock_in_date,
        max_days,
        repair_date,
        surplus_repair_num,
        title,
        text1,
        continuous_day,
        no_svip_text,
        btn_corner_text,
        common_gold_text,
        svip_gold_text,
        is_sign,
        week_info,
      } = gold;
      this.goldBoxList = gold_box_list;
      this.noSvipText = no_svip_text || [];
      this.svipTip = btn_corner_text;
      if (common_gold_text) {
        this.common_gold_text = common_gold_text;
      }
      if (svip_gold_text) {
        this.svip_gold_text = svip_gold_text;
      }
      this.rules = text1;
      this.clockInDate = clock_in_date;
      this.maxDays = max_days;
      this.continousDay = continuous_day;
      this.surplusRepairNum = surplus_repair_num;
      this.repairDate = repair_date;
      this.weekList = week_info;
      this.tabList = tab;
      this.achievementTaskList = achievementTasks;
      this.dailyTaskList = dailyTasks;
      this.newbieTaskList = newbieTasks;
      this.isSvip = isSvip;
      this.ptbUrl = ptbUrl;
      this.vipUrl = vipUrl;
      let OPEN_SIGN_DIALOG_HIDE = this.getCookie('OPEN_SIGN_DIALOG_HIDE');
      if (
        this.userInfo.token &&
        !is_sign &&
        !this.isOnceSignInPopupShow &&
        !OPEN_SIGN_DIALOG_HIDE
      ) {
        this.signInPopupShow = true;
        this.isOnceSignInPopupShow = true;
      }
    },
    openRulePopup() {
      // this.rulePopup = true;
      BOX_openInNewWindow(
        { name: 'ClockInRule' },
        { url: `https://${this.$h5Page.env}game.3733.com/#/clock_in_rule` },
      );
    },
    goToGameCoupon(item) {
      BOX_openInNewWindow(
        { name: 'GameCoupon' },
        { url: `${window.location.origin}/#/game_coupon/${item.id}` },
      );
    },
    getThisWeekList() {
      let date = new Date(defaultDate);
      let now = date.getTime();
      let day = date.getDay();
      let oneDayTime = 24 * 60 * 60 * 1000;
      let MondayTime = now - day * oneDayTime;
      let SundayTime = now + (7 - day - 1) * oneDayTime;
      let thisWeekList = [];
      for (let i = 0; i < 7; i++) {
        let d = new Date(MondayTime + i * oneDayTime);
        thisWeekList.push(d);
      }
      this.thisWeekList = thisWeekList.map(item => {
        return {
          day: item.getDate(),
          month: item.getMonth() + 1,
        };
      });
    },
    // 签到成功跳转svip
    toSvip() {
      this.signSuccessShow = false;
      this.noSvipSignShow = false;
      this.getAwardIncompletePopupShow = false;
      this.getAwardPopupShow = false;
      // this.boxSuccessShow = false;
      this.$nextTick(() => {
        this.goToSvip();
      });
    },
    // 补签跳转svip
    toBuySvip() {
      this.isSignPopupJump = false;
      this.repairSignTipShow = false;
      this.$nextTick(() => {
        this.goToSvip();
      });
    },
    handleRepair(item) {
      if (item.is_repair) {
        this.repairSign(Number(item.day));
        this.signInPopupShow = false;
        this.isSignPopupJump = true;
      }
    },
    // 是否可补签
    async repairSign(item) {
      if (this.canSignIn(item)) {
        // 神策埋点 补签点击
        this.$sensorsTrack('sup_sign_click');

        this.$toast.loading({
          message: this.$t('加载中'),
        });
        const res = await ApiUserRepairDeductGold();
        this.$toast.clear();
        if (res.code == -14) {
          // 非svip点击补签
          this.noSvipRepairSignTip = res.msg;
          this.repairSignTipShow = true;
        }
        if (res.code > 0) {
          this.repairSignTip = res.data.gold_num;
          this.repairSignDay = item;
          this.svipRepairSignTipShow = true;
        }
      }
    },
    // 补签中
    async repairSigning() {
      this.isSignPopupJump = false;
      this.svipRepairSignTipShow = false;
      let date = `${this.currentYear}-${this.currentMonth + 1}-${
        this.repairSignDay
      }`;
      const res = await ApiUserRepairSign({
        signDate: date,
      });
      this.repairGoldNum = res.data.gold_num;
      this.repairSignSuccessShow = true;

      // 神策埋点
      this.$sensorsTrack('sup_sign_success');

      await this.getWelfareIndex();
    },
    // 是否是签到过的
    isSignIn(item) {
      let str = item.toString();
      str = str < 10 ? '0' + str : str;
      if (this.clockInDate.length > 0 && this.clockInDate.includes(str)) {
        return true;
      }
      return false;
    },
    // 是否是可以补签的
    canSignIn(item) {
      let str = item.toString();
      str = str < 10 ? '0' + str : str;
      if (this.repairDate.length > 0 && this.repairDate.includes(str)) {
        return true;
      }
      return false;
    },
    closeRepairSignTipPopup() {
      if (this.isSignPopupJump) {
        this.signInPopupShow = true;
        this.isSignPopupJump = false;
      }
    },
    // 领取宝箱
    async handleGet(item) {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      // 文案由后端返回，所以前端没有判断是否已领取过或者还不能领取，所有宝箱均可点击
      await this.fetchGetGoldBox(item);
      // this.SET_USER_INFO(true);
    },
    fetchGetGoldBoxAfter() {
      this.SET_USER_INFO(true);
    },
    async fetchGetGoldBox(item) {
      try {
        const res = await ApiUserGetGoldBox({
          box_num: item.box_num,
        });
        let {
          need_share,
          gold_box_list,
          clock_in_date,
          is_svip,
          get_box,
          show_text,
          share_info,
          list,
        } = res.data;
        if (res.code > 0) {
          this.$toast.clear();
          this.getAwardPopupInfo = {};
          if (res.data.pop) {
            this.getAwardPopupInfo = res.data.pop;
            this.getAwardIncompletePopupShow = true;
            return false;
          }
          if (res.points) {
            this.getAwardPopupInfo = {
              ...res.data.prize_info,
              svip_text: res.data.svip_text || '',
            };
            this.getAwardPopupShow = true;
            this.signInPopupShow = false;
          }
          await this.getWelfareIndex();
        }

        // 以下两个if为刷新宝箱数据
        if (gold_box_list && gold_box_list.length) {
          this.goldBoxList = gold_box_list;
        }
        if (clock_in_date && clock_in_date.length) {
          this.clockInDate = clock_in_date;
        }

        if (need_share == true) {
          // 需要分享的
          if (is_svip == false) {
            // 非svip才需要助力
            if (get_box == false) {
              // 还需要助力
              this.goldBoxShareData = {
                gold: item.gold,
                showText: show_text,
              };
              if (share_info) {
                this.shareInfo = share_info;
              }
              this.goldBoxShareShow = true;
            } else {
              // 助力成功
              this.goldBoxShareSuccessData = list[0];
              this.goldBoxShareSuccessData.gold = item.gold;
              this.goldBoxShareSuccessShow = true;
            }
          } else {
            // svip直接领取的弹窗
            this.goldBoxSvipSuccessData = {
              gold: item.gold,
            };
            this.goldBoxSvipSuccessShow = true;
          }
        }
      } catch (e) {}
    },
    clickRules(event) {
      event.preventDefault();
      let target = event.target;
      if (target.tagName.toLowerCase() == 'a') {
        let routeName = target.getAttribute('href');
        this.$router.push({ name: routeName });
      }
    },
    // 点击svip签到
    clickSvipSign() {
      this.$sensorsTrack('sign_in_click');
      this.noSvipSignShow = false;
      this.$nextTick(() => {
        this.handlenotice();
        this.handleSignInNotice();
        this.goToSvip();
      });
    },
    // 点击普通签到
    async clickNormalSign() {
      this.$sensorsTrack('sign_in_click');
      this.noSvipSignShow = false;
      this.signInPopupShow = false;
      this.$nextTick(async () => {
        this.handlenotice();
        await this.handleClockIn();
      });
    },
    // 点击弹窗的豪华签到
    async clickRichSign() {
      this.$sensorsTrack('sign_in_click');
      await this.handleClockIn();
    },
    handlenotice() {
      if (this.remember) {
        localStorage.setItem('NO_SVIP_SIGN_DIALOG_HIDE', true);
      }
    },
    handleSignInNotice() {
      if (this.signInRemember) {
        this.setCookieToMidnight('OPEN_SIGN_DIALOG_HIDE', true);
      }
    },
    setCookieToMidnight(name, value) {
      var date = new Date();
      // date.setHours(24, 0, 0, 0); // 设置时间为午夜12点
      date.setDate(date.getDate() + 1);
      date.setHours(0);
      date.setMinutes(0);
      date.setSeconds(0);
      document.cookie = `${name}=${value};expires=${date}; path=/`;
    },
    getCookie(name) {
      let cookies = document.cookie.split('; ');
      let result = '';
      cookies.forEach(cookie => {
        let item = cookie.split('=');
        if (item[0] == name) {
          result = item[1];
        }
      });

      return result;
    },
    // 点击签到
    async clockIn() {
      this.$sensorsTrack('sign_in_click');
      if (!this.userInfo.token) {
        this.$dialog
          .confirm({
            message: '请登录',
            confirmButtonText: this.$t('确定'),
            confirmButtonColor: themeColorLess,
            lockScroll: false,
          })
          .then(() => {
            BOX_login();
            // router.push({ name: 'PhoneLogin' });
          });
        return false;
      }
      let temDay =
        this.currentDay < 10
          ? `0${this.currentDay}`
          : this.currentDay.toString();
      if (!this.clockInDate.includes(temDay)) {
        // // 还没签到的情况
        if (!this.userInfo.is_svip) {
          let no_svip_sign_dialog_hide = localStorage.getItem(
            'NO_SVIP_SIGN_DIALOG_HIDE',
          );
          if (no_svip_sign_dialog_hide) {
            // 不再提示svip直接签到
            await this.handleClockIn();
            return false;
          }
          this.remember = false;
          this.noSvipSignShow = true;
        } else {
          // 是svip的情况
          await this.handleClockIn();
        }
        return false;
      }
      this.$toast(this.$t('今日已签到'));
    },
    async handleClockIn() {
      this.$toast.loading({
        message: this.$t('加载中...'),
      });
      const res = await ApiUserActiveSign();
      this.$toast.clear();
      if (res.data.reward_info) {
        // 弹出签到成功弹窗
        this.reward_info = res.data.reward_info;
        this.signSuccessShow = true;
        this.signInPopupShow = false;

        // 神策埋点
        this.$sensorsTrack('sign_in_success');
      } else {
        this.$toast(res.msg);
      }
      await this.getWelfareIndex();
    },
    handleShare() {
      this.$copyText(this.shareInfo.title_url).then(res => {
        this.$toast(this.$t('复制成功，快去粘贴给好友助力吧~'));
      });
    },
    openYindaoPopup() {
      if (needGuide) {
        this.yindaoPopup = true;
      }
    },
    actionToPage(item) {
      handleActionCode(item);
    },
    handleGo1(item, key) {
      switch (key) {
        case 'sign':
          this.toSignIn();
          // BOX_showActivity({ name: 'ClockIn' }, { page: 'qd' });
          break;
        case 'gold_dial':
          BOX_showActivity({ name: 'TurnTable' }, { page: 'jbzp' });
          break;
        case 'pay':
          this.popupContent =
            item.task_prompt || '每日充值一次平台币即可完成任务哦~';
          this.popupTip = '';
          this.popupConfirmText = '充值平台币';
          this.taskPopup = true;
          break;
        case 'pay100':
        case 'pay500':
          this.popupContent =
            item.task_prompt ||
            `每日充值平台币满${item.need_value}元即可完成任务哦~`;
          this.popupTip = '注：每日累计充值时间为0点至24点';
          this.popupConfirmText = '充值平台币';
          this.taskPopup = true;
          break;
        case 'play_time':
          this.popupContent = '成功登录并畅玩任意游戏30分钟即可完成当前任务哦~';
          this.popupTip = '';
          this.popupConfirmText = '我知道了';
          this.taskPopup = true;
          break;
        default:
          break;
      }
    },
    handleGo2(item, key) {
      switch (key) {
        case 'pay':
          this.popupContent = `累计充值平台币满${item.need_value}元即可完成任务`;
          this.popupConfirmText = this.$t('充值平台币');
          this.taskPopup = true;
          break;
        case 'sign':
          this.toSignIn();
          break;
        default:
          break;
      }
    },
    handleGo3(item, key) {
      switch (key) {
        case 'band_email': // 绑定邮箱
          BOX_showActivity(
            { name: 'ChangeEmail' },
            { page: 'com.a3733.cwbgamebox.ui.mine.BindEmailActivity' },
          );
          break;
        case 'add_assistant':
          BOX_openInNewWindow(
            { name: 'AddAssistant' },
            { url: `https://${this.$h5Page.env}game.3733.com/#/add_assistant` },
          );
          break;
        case 'bind_wx': // 绑定微信
          BOX_openInNewWindow(
            { name: 'BindWeChat' },
            { url: `https://${this.$h5Page.env}game.3733.com/#/bind_we_chat` },
          );
          break;
        case 'follow_gzh': // 关注公众号
          BOX_openInNewWindow(
            { name: 'BindWeChat' },
            { url: `https://${this.$h5Page.env}game.3733.com/#/bind_we_chat` },
          );
          break;
        case 'down_game': // 下载游戏
          BOX_showActivity({ name: 'Category' }, { page: 'qbyx' });
          break;
        case 'first_pay': // 首充
          this.popupContent = this.$t(
            '在平台游戏内使用微信支付/支付宝支付累计充值满6元，即可完成任务哦~',
          );
          this.popupTip = '';
          this.popupConfirmText = this.$t('我知道了');
          this.taskPopup = true;
          break;
        case 'gold_dial':
          BOX_showActivity({ name: 'TurnTable' }, { page: 'jbzp' });
          break;
        case 'mem_info': // 实名认证
        case 'mobile': // 绑定手机
          let set_temp =
            platform == 'android'
              ? 'com.a3733.gamebox.ui.etc.AccountSafeActivity'
              : 'YYAccountAndSecurityViewController';
          BOX_showActivity({ name: 'UserInfo' }, { page: set_temp });
          break;
        case 'pay100': // 充值100元
          this.popupContent = this.$t(
            '在平台游戏内使用微信支付/支付宝支付累计充值满100元，即可完成任务哦~',
          );
          this.popupTip = '';
          this.popupConfirmText = this.$t('我知道了');
          this.taskPopup = true;
          break;
        case 'bookmark': //IOS商店版添加IOS书签版
          if (item.is_diff_users) {
            this.$toast('该设备已完成过任务');
          } else {
            ApiMissionCheckToComplete().then(res => {
              BOX_openInBrowser(
                {
                  h5_url: `https://${
                    this.$h5Page.env
                  }game.3733.com${window.location.search.substring(0)}`,
                },
                {},
              );
            });
          }
          break;
        case 'down_zasq': //下载追爱神器
          if (platform == 'android') {
            BOX_goToGame(
              {
                params: {
                  id: item.jump_game_id,
                },
              },
              { id: item.jump_game_id },
            );
            return false;
          }
          navigateToGameDetail({ id: item.jump_game_id });
          break;
      }
    },
    async handleGet1(item, key) {
      this.$toast.loading({
        message: this.$t('拼命加载中...'),
      });
      const res = await ApiMissionGetMissionReward({
        rule_id: item.rule_id,
      });
      if (res.code == 1) {
        this.$toast.clear();
        let info = '';
        info = this.formatRewards(item);
        useCollectToast(
          {
            title: '任务完成',
            info,
          },
          2000,
        );
      } else {
        this.$toast(res.msg);
      }
      await this.getWelfareIndex();
      this.SET_USER_INFO();
    },
    async handleGet2(item, key) {
      this.$toast.loading({
        message: this.$t('拼命加载中...'),
      });
      const res = await ApiMissionGetAchievementReward({
        mission: key,
        mission_level: item.mission_level,
      });
      if (res.code == 1) {
        this.$toast.clear();
        let info = '';
        info = this.formatRewards(item);
        useCollectToast(
          {
            title: '任务完成',
            info,
          },
          2000,
        );
      } else {
        this.$toast(res.msg);
      }
      await this.getWelfareIndex();
      this.SET_USER_INFO();
    },
    async handleGet3(item, key) {
      this.$toast.loading({
        message: this.$t('拼命加载中...'),
      });
      const res = await ApiMissionGetMissionReward({
        rule_id: item.rule_id,
      });
      if (res.code == 1) {
        this.$toast.clear();
        let info = '';
        info = this.formatRewards(item);
        useCollectToast(
          {
            title: '任务完成',
            info,
          },
          2000,
        );
      } else {
        this.$toast(res.msg);
      }
      await this.getWelfareIndex();
      this.SET_USER_INFO();
    },
    formatRewards(item) {
      let rewards = [];
      if (item?.gold_num) {
        rewards.push('金币+' + item.gold_num);
      }
      if (item?.reward) {
        rewards.push('金币+' + item.reward);
      }
      if (item.coupon) {
        rewards.push(item.coupon);
      }
      if (item.exp_num) {
        rewards.push('经验+' + item.exp_num);
      }
      return rewards.join('、');
    },
    popupConfirm() {
      this.taskPopup = false;
      if (this.popupConfirmText == this.$t('我知道了')) {
        return false;
      }
      BOX_openInNewWindow({ name: 'PlatformCoin' }, { url: this.ptbUrl });
    },
    goToSvip() {
      BOX_openInNewWindow({ name: 'Svip' }, { url: this.$h5Page.svip_url });
    },
    goToGoldCoin() {
      BOX_openInNewWindow({ name: 'TaskDaily' }, { url: h5Page.meirirenwu });
    },
    goToGoldCoinExchange() {
      BOX_openInNewWindow(
        { name: 'GoldCoinExchange' },
        { url: `${window.location.origin}/#/gold_coin_exchange` },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.welfare-center-page {
  .main {
    background-color: #f7f8fa;
    overflow: hidden;

    .placeholder {
      width: 100%;
      height: 20 * @rem;
    }
  }
  .section-container {
    box-sizing: border-box;
    width: 351 * @rem;
    margin: 0 auto;
    border-radius: 12 * @rem;
    position: relative;
  }
  .top-bar-container {
    background: #fff;
    border-radius: 12 * @rem;
    margin-top: 12 * @rem;
    position: relative;
    overflow: hidden;

    .clock-in-rule {
      width: 95 * @rem;
      height: 40 * @rem;
      background: url(~@/assets/images/welfare/welfare-center/clock-in-rule1.png)
        no-repeat;
      background-size: 95 * @rem 40 * @rem;
      position: absolute;
      right: 0;
      top: 0;
      z-index: 3;
    }

    .top-reward {
      box-sizing: border-box;
      padding: 12 * @rem 0 0;
      position: relative;
      z-index: 2;
      .reward-title {
        width: 130 * @rem;
        height: 26 * @rem;
        background: url(~@/assets/images/welfare/welfare-center/task-title-4.png)
          left top no-repeat;
        background-size: 130 * @rem 26 * @rem;
        margin-left: 12 * @rem;
      }
      .reward-subtitle {
        font-size: 11 * @rem;
        line-height: 14 * @rem;
        color: #93999f;
        margin-top: 5 * @rem;
        margin-left: 12 * @rem;
      }
      .reward-list {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 10 * @rem 10 * @rem;
        padding: 0 * @rem 14 * @rem;
        margin-top: 15 * @rem;
        position: relative;
        z-index: 1;
        .reward-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          height: 88 * @rem;
          background: rgba(245, 245, 246, 0.58);
          border-radius: 9 * @rem;
          &.last {
            grid-column: ~'2 / 4';
            padding: 0 18 * @rem;
            position: relative;
            .days {
              margin-right: auto;
              margin-top: 22 * @rem;
            }
            .reward-icon {
              width: 70 * @rem;
              height: 38 * @rem;
              margin-left: auto;
              margin-top: 0;
              background-size: 70 * @rem 38 * @rem;
              position: absolute;
              top: 50%;
              right: 20 * @rem;
              transform: translateY(-50%);
            }
            .coins {
              margin-right: auto;
              margin-top: 11 * @rem;
            }
          }
          .days {
            font-size: 14 * @rem;
            color: #242840;
            font-weight: bold;
            line-height: 14 * @rem;
            margin-top: 12 * @rem;
          }
          .reward-icon {
            width: 42 * @rem;
            height: 24 * @rem;
            .image-bg('~@/assets/images/clock-in/coin-new-2.png');
            background-size: auto 24 * @rem;
            margin-top: 7 * @rem;
            &.svip {
              background-image: url('~@/assets/images/clock-in/svip-new.png');
            }
          }
          .coins {
            padding: 0 5 * @rem;
            height: 18 * @rem;
            line-height: 18 * @rem;
            border-radius: 9 * @rem;
            background-color: #ffffff;
            text-align: center;
            font-size: 10 * @rem;
            font-weight: 500;
            color: #ff8819;
            white-space: nowrap;
            margin-top: 7 * @rem;
            &.can {
              color: rgba(255, 136, 25, 1);
              background-color: rgba(255, 239, 197, 0.7);
            }
            &.had {
              color: rgba(121, 121, 121, 1);
              background-color: rgba(238, 238, 238, 1);
            }
          }
        }
      }
    }

    .clock-in-info {
      padding: 15 * @rem 14 * @rem 0;
      .operate-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .total-days {
          font-size: 11 * @rem;
          color: #797979;
          height: 35 * @rem;
          span {
            font-size: 28 * @rem;
            color: @themeColor;
            font-weight: bold;
            line-height: 35 * @rem;
            margin-right: 4 * @rem;
          }
        }
        .record-item {
          font-size: 14 * @rem;
          font-weight: bold;
          color: #242840;
          line-height: 14 * @rem;
          margin-top: 4 * @rem;
          span {
            color: @themeColor;
            font-weight: bold;
          }
        }
        .clock-in-btn {
          width: 96 * @rem;
          height: 42 * @rem;
          font-size: 14 * @rem;
          font-weight: 600;
          color: #fff;
          display: flex;
          line-height: 38 * @rem;
          justify-content: center;
          .image-bg('~@/assets/images/welfare/welfare-center/clock-in-btn-new1.png');
          background-size: 96 * @rem 42 * @rem;
          margin-top: 15 * @rem;

          &.had {
            .image-bg('~@/assets/images/welfare/welfare-center/clock-in-btn-had-new1.png');
            position: relative;

            span {
              display: block;
              width: 78 * @rem;
              height: 25 * @rem;
              background: url('~@/assets/images/welfare/welfare-center/tomorrow-sign-in1.png')
                no-repeat;
              background-size: 78 * @rem 25 * @rem;
              position: absolute;
              left: 5 * @rem;
              top: -12 * @rem;
            }
          }
        }
      }
      .record-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 15 * @rem;
      }
    }

    .calendar-container {
      width: 100%;
      overflow: hidden;
      background-color: #fff;
      padding-top: 23 * @rem;
      .title {
        font-size: 14 * @rem;
        font-weight: bold;
        color: #000000;
        height: 20 * @rem;
        line-height: 20 * @rem;
        margin: 0 14 * @rem;
        text-align: center;
        position: relative;
        &:before {
          content: '';
          width: 100 * @rem;
          height: 0 * @rem;
          border-top: 0.5 * @rem dashed rgba(44, 67, 128, 0.26);
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
        &:after {
          content: '';
          width: 100 * @rem;
          height: 0 * @rem;
          border-top: 0.5 * @rem dashed rgba(44, 67, 128, 0.26);
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      .content {
        padding: 0 7 * @rem;
        .calendar-top {
          display: flex;
          flex-wrap: wrap;
          padding-top: 24 * @rem;
          .top-item {
            width: 14.28%;
            text-align: center;
            font-size: 12 * @rem;
            line-height: 17 * @rem;
            color: #797979;
          }
        }
        .day-list {
          display: flex;
          flex-wrap: wrap;
          padding: 10 * @rem 0 15 * @rem;
          .day-item {
            width: 14.28%;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 5 * @rem;
            &.day-pre,
            &.day-next {
              .day-btn {
                .num {
                  color: #c1c1c1;
                  font-size: 16 * @rem;
                }
              }
            }
            .day-btn {
              box-sizing: border-box;
              width: 34 * @rem;
              height: 42 * @rem;
              border-radius: 8 * @rem;
              .num {
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16 * @rem;
                line-height: 20 * @rem;
                color: #000000;
                font-weight: 400;
                // width: 34 * @rem;
                margin-top: 4 * @rem;
              }
              .text {
                width: 34 * @rem;
                height: 14 * @rem;
                font-size: 12 * @rem;
                line-height: 14 * @rem;
                font-weight: 400;
                color: @themeColor;
                transform: scale(0.8333);
                transform-origin: center top;
              }

              &.had {
                background-color: #f5f5f6;
                .num {
                  color: #000000;
                }
                .text {
                  transform: unset;
                  background: url(~@/assets/images/clock-in/clock-had.png)
                    center center no-repeat;
                  background-size: 15 * @rem 10 * @rem;
                }
              }
              &.can {
                background-color: #f5f5f6;
              }
              &.today {
                border: 1 * @rem solid @themeColor;
                background: #e3fff6;
                .num {
                  color: @themeColor;
                  font-weight: bold;
                }
              }
            }
          }
        }
      }
      .calendar-open {
        width: 48 * @rem;
        height: 20 * @rem;
        background: url(~@/assets/images/welfare/welfare-center/calendar-open.png)
          left center no-repeat;
        background-size: 48 * @rem 20 * @rem;
        margin: 0 auto;
        &.close {
          background-image: url(~@/assets/images/welfare/welfare-center/calendar-close.png);
        }
      }
    }
  }
  .gold-container {
    background: #fff;
    width: 355 * @rem;
    overflow-x: auto;
    border-radius: 12 * @rem;
    margin: 15 * @rem 10 * @rem 10 * @rem;
    padding-bottom: 8 * @rem;
    &::-webkit-scrollbar {
      display: none;
    }
    .select-list {
      border-radius: 10 * @rem;
      .select-item {
        width: 70 * @rem;
        box-sizing: border-box;
        position: relative;
        height: 100 * @rem;

        background-color: #ffffff;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .select-icon {
          img {
            width: auto;
            height: 48 * @rem;
            display: block;
            margin: 0 auto;
          }
        }
        .select-text {
          box-sizing: border-box;
          .select-name {
            font-size: 13 * @rem;
            line-height: 22 * @rem;
            margin-top: 5 * @rem;
            font-weight: 500;
            color: #000000;
            text-align: center;
          }
        }
      }
    }
    .swiper-scrollbar {
      margin: -5 * @rem auto 0;
      width: 18 * @rem;
      height: 3 * @rem;
      background-color: #e3e5e8;
      position: relative;
      z-index: 2;
    }
    /deep/ .swiper-scrollbar-drag {
      width: 6 * @rem !important;
      background: #93999f;
    }
  }
  .task-container {
    position: relative;
    margin: 12 * @rem auto 0;
    width: 355 * @rem;
    border-radius: 12 * @rem 12 * @rem 12 * @rem 12 * @rem;
    background: #ffffff;
    &:first-of-type {
      margin-top: 0;
    }
    .task-title {
      height: 26 * @rem;
      padding: 12 * @rem 12 * @rem 0;

      img {
        height: 100%;
        width: auto;
      }
    }
    .task-list {
      // padding: 1 * @rem 0 25 * @rem;
      .task-item {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        // width: 331 * @rem;
        height: 70 * @rem;
        /* background: #f5f9ff; */
        /* border-radius: 0.2136rem; */
        // margin: 0 auto;
        padding: 0 14 * @rem;
        &:not(:last-of-type) {
          border-bottom: 0.5 * @rem solid #f4f5f6;
        }
        .icon {
          width: 36 * @rem;
          height: 36 * @rem;
          background-color: #effbf9;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          img {
            width: 22 * @rem;
            height: 22 * @rem;
          }
        }
        .task-info {
          flex: 1;
          min-width: 0;
          margin-left: 10 * @rem;
          .title {
            font-size: 14 * @rem;
            color: #222222;
            height: 20 * @rem;
            line-height: 20 * @rem;
            font-weight: 600;
            text-align: left;
            word-break: break-all;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .subtitle {
            font-size: 10 * @rem;
            color: #7a7a7a;
            line-height: 18 * @rem;
            font-weight: 500;
            text-align: left;
            word-break: break-all;
            margin-top: 4 * @rem;
          }
          .line {
            display: flex;
            align-items: center;
            margin-top: 4 * @rem;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-size: 10 * @rem;
            color: #60666c;
            line-height: 14 * @rem;
            .gold {
              padding-left: 20 * @rem;
              background: url(~@/assets/images/recharge/task-gold.png) left
                center no-repeat;
              background-size: 18 * @rem 18 * @rem;
              line-height: 18 * @rem;
              font-size: 12 * @rem;
              color: #f05f29;
              line-height: 15 * @rem;
              margin-right: 12 * @rem;
            }
            .exp {
              padding-left: 20 * @rem;
              background: url(~@/assets/images/recharge/task-exp.png) left
                center no-repeat;
              background-size: 18 * @rem 18 * @rem;
              line-height: 18 * @rem;
              font-size: 12 * @rem;
              color: #f05f29;
              line-height: 15 * @rem;
              margin-right: 12 * @rem;
            }
            .coupon {
              padding-left: 20 * @rem;
              background: url(~@/assets/images/recharge/task-coupon.png) left
                center no-repeat;
              background-size: 18 * @rem 18 * @rem;
              line-height: 18 * @rem;
              font-size: 12 * @rem;
              color: #f05f29;
              line-height: 15 * @rem;
              margin-right: 12 * @rem;
            }
            .reward-info {
              color: #ff8819;
            }
          }
          .extra {
            font-size: 11 * @rem;
            color: #777777;
            line-height: 14 * @rem;
            margin-top: 6 * @rem;
          }
        }
        .task-btn {
          width: 72 * @rem;
          height: 28 * @rem;
          border-radius: 25 * @rem;
          border: 1 * @rem solid #c7ccd1;
          font-size: 12 * @rem;
          color: #191b1f;
          font-weight: 600;
          display: flex;
          align-items: center;
          justify-content: center;
          &.had {
            color: #c7ccd1;
            border: 1 * @rem solid #e3e5e8;
          }
          &.get {
            color: #fff;
            background: #1cce94;
            border: none;
          }
        }
      }

      .exchange-down {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12 * @rem;
        color: #777777;
        padding: 10 * @rem 0 10 * @rem;
        i {
          display: block;
          width: 10 * @rem;
          height: 7 * @rem;
          margin-left: 4 * @rem;
          background: url(~@/assets/images/down-icon.png) center center
            no-repeat;
          background-size: 10 * @rem 7 * @rem;
        }
        &.up {
          i {
            transform: rotate(180deg);
          }
        }
      }
    }
  }
  .sign-dialog {
    width: 300 * @rem;
    background: transparent;
    overflow: visible;
    .logo-icon {
      width: 164 * @rem;
      height: 97 * @rem;
      .image-bg('~@/assets/images/clock-in/sign-success-logo3.png');
      margin: 0 auto;
      position: relative;
      z-index: 3;
    }
    .dialog-content {
      box-sizing: border-box;
      position: relative;
      background-color: #fff;
      border-radius: 16 * @rem;
      margin-top: -43 * @rem;
      z-index: 2;
      padding: 50 * @rem 23 * @rem 21 * @rem;
      background: #fff url('~@/assets/images/clock-in/sign-success-bg1.png')
        no-repeat 0 0;
      background-size: 300 * @rem 270 * @rem;
      width: 300 * @rem;
      text-align: center;
      .title {
        white-space: nowrap;
        height: 25 * @rem;
        font-weight: normal;
        font-size: 18 * @rem;
        color: #191b1f;
        line-height: 25 * @rem;
        text-align: center;
        font-weight: bold;
      }
      .reward-box {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 21 * @rem;

        .reward-item {
          margin-left: 20 * @rem;

          &:first-of-type {
            margin-left: 0;
          }

          .exp-icon {
            width: 48 * @rem;
            height: 36 * @rem;
            background: url(~@/assets/images/clock-in/new-icon-exp.png)
              no-repeat;
            background-size: 48 * @rem 36 * @rem;
          }

          .gold-icon {
            width: 48 * @rem;
            height: 36 * @rem;
            background: url(~@/assets/images/clock-in/new-icon-coin.png)
              no-repeat;
            background-size: 48 * @rem 36 * @rem;
          }

          .reward-msg {
            display: flex;
            align-items: flex-end;
            justify-content: center;
            margin-top: 2 * @rem;

            span {
              height: 11 * @rem;
              font-weight: bold;
              font-size: 9 * @rem;
              color: #191b1f;
              line-height: 11 * @rem;
            }

            .msg-number {
              height: 14 * @rem;
              font-weight: bold;
              font-size: 14 * @rem;
              color: #191b1f;
              line-height: 14 * @rem;
            }
          }

          &.svip-more-gold {
            margin-left: 7 * @rem;
            .gold-icon {
              background-image: url(~@/assets/images/clock-in/new-icon-coin2.png);
            }

            .reward-msg {
              span {
                color: #ff8819;
              }
              .msg-number {
                color: #ff8819;
              }
            }
          }
        }

        .upgrade {
          margin-left: 13 * @rem;

          .text {
            height: 14 * @rem;
            font-weight: bold;
            font-size: 10 * @rem;
            color: #ff8819;
            line-height: 14 * @rem;
            text-align: center;
          }

          .up-icon {
            width: 20 * @rem;
            height: 21 * @rem;
            background: url(~@/assets/images/clock-in/level-up-icon.png)
              no-repeat;
            background-size: 20 * @rem 21 * @rem;
            margin-top: 2 * @rem;
          }
        }
      }
      .svip-tips {
        width: 224 * @rem;
        padding: 8 * @rem 15 * @rem;
        margin: 13 * @rem auto 0;
        box-sizing: border-box;
        border-radius: 8 * @rem;
        background: linear-gradient(180deg, #effff8 0%, #ffffff 48 * @rem);
        position: relative;

        &::after {
          content: '';
          display: block;
          width: 10 * @rem;
          height: 10 * @rem;
          background: url(~@/assets/images/welfare/welfare-center/triangle-green.png)
            no-repeat;
          background-size: 10 * @rem 10 * @rem;
          position: absolute;
          top: -5 * @rem;
          right: 31 * @rem;
          z-index: 1;
        }

        .tips-title {
          height: 17 * @rem;
          font-weight: bold;
          font-size: 12 * @rem;
          color: #2bbe88;
          line-height: 17 * @rem;
          text-align: left;
          margin-bottom: 1 * @rem;
        }
        .small-tips {
          display: flex;
          align-items: center;
          height: 15 * @rem;
          font-weight: 400;
          font-size: 11 * @rem;
          color: #93999f;
          line-height: 15 * @rem;
          margin-top: 6 * @rem;

          &::before {
            content: '';
            display: block;
            width: 6 * @rem;
            height: 6 * @rem;
            background: url(~@/assets/images/welfare/tips-star-icon.png)
              no-repeat;
            background-size: 6 * @rem 6 * @rem;
            margin-right: 4 * @rem;
          }
        }
      }
      .btn-list {
        display: flex;
        align-content: center;
        justify-content: space-between;
        margin-top: 21 * @rem;
        .btn {
          flex: 1;
          height: 40 * @rem;
          line-height: 40 * @rem;
          background: #ecfbf4;
          border-radius: 30 * @rem;
          font-weight: bold;
          font-size: 15 * @rem;
          color: @themeColor;
          text-align: center;
          margin-right: 14 * @rem;
          position: relative;

          &:last-of-type {
            background: #1cce94;
            color: #fff;
            margin-right: 0;
          }

          .svip-tip {
            width: unset;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 18 * @rem;
            padding: 0 8 * @rem;
            font-weight: bold;
            font-size: 9 * @rem;
            color: #c05c2a;
            line-height: 11 * @rem;
            text-align: center;
            border-radius: 9 * @rem 9 * @rem 9 * @rem 0;
            box-sizing: border-box;
            background: linear-gradient(
              90deg,
              #ffeaab 0%,
              #ffe0dd 54%,
              #ffbdcf 100%
            );
            border: 1 * @rem solid #ffffff;
            position: absolute;
            top: -13 * @rem;
            right: -8 * @rem;
          }
        }
      }
      .tips {
        margin-top: 12 * @rem;
        white-space: nowrap;
        height: 15 * @rem;
        font-weight: bold;
        font-size: 12 * @rem;
        color: @themeColor;
        line-height: 15 * @rem;
        text-align: center;
      }
      .dialog-close-btn {
        position: absolute;
        bottom: -58 * @rem;
        left: 50%;
        transform: translate(-50%, 0);
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: url(~@/assets/images/clock-in/sign-close.png) no-repeat 0 0;
        background-size: 28 * @rem 28 * @rem;
        width: 28 * @rem;
        height: 28 * @rem;
      }
    }
  }
  .repair-sign-dialog {
    width: 300 * @rem;
    background: transparent;
    overflow: visible;
    .logo-icon {
      width: 164 * @rem;
      height: 134 * @rem;
      .image-bg('~@/assets/images/clock-in/sign-success-logo1.png');
      margin: 0 auto;
      position: relative;
      z-index: 3;
    }
    .dialog-content {
      box-sizing: border-box;
      position: relative;
      background-color: #fff;
      border-radius: 16 * @rem;
      margin-top: -67 * @rem;
      z-index: 2;
      padding: 67 * @rem 31 * @rem 25 * @rem;
      background: url('~@/assets/images/clock-in/sign-success-bg1.png')
        no-repeat 0 0;
      background-size: 300 * @rem 270 * @rem;
      width: 300 * @rem;
      height: 224 * @rem;
      text-align: center;
      .title {
        white-space: nowrap;
        height: 31 * @rem;
        font-weight: normal;
        font-size: 22 * @rem;
        color: #333333;
        line-height: 31 * @rem;
        text-align: center;
        font-style: normal;
        text-transform: none;
        font-weight: bold;
      }
      .msg {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 25 * @rem;
        font-weight: bold;
        font-size: 15 * @rem;
        color: @themeColor;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin: 15 * @rem 0 24 * @rem 0;
        .msg-number {
          font-weight: bold;
          font-size: 20 * @rem;
        }
      }
      .btn {
        width: 238 * @rem;
        height: 40 * @rem;
        line-height: 40 * @rem;
        background: #1cce94;
        border-radius: 40 * @rem;
        font-weight: 500;
        font-size: 15 * @rem;
        color: #ffffff;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .tips {
        margin-top: 8 * @rem;
        white-space: nowrap;
        height: 17 * @rem;
        font-weight: 500;
        font-size: 12 * @rem;
        color: #777777;
        line-height: 17 * @rem;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .dialog-close-btn {
        position: absolute;
        bottom: -58 * @rem;
        left: 50%;
        transform: translate(-50%, 0);
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: url(~@/assets/images/clock-in/sign-close.png) no-repeat 0 0;
        background-size: 28 * @rem 28 * @rem;
        width: 28 * @rem;
        height: 28 * @rem;
      }
    }
  }
  .sign-popup-unset {
    /deep/ .van-dialog {
      background-color: unset;
      width: unset;
      border-radius: unset;
    }
  }
  .dialog-close {
    width: 50 * @rem;
    height: 50 * @rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 10 * @rem auto 0;
    img {
      display: block;
      width: 30 * @rem;
      height: 30 * @rem;
    }
  }
  .box-success-dialog {
    width: 306 * @rem;
    overflow: unset;
  }
  .box-success {
    box-sizing: border-box;
    padding-top: 1 * @rem;
    position: relative;
    width: 306 * @rem;
    background: #fff3fc;
    border-radius: 12 * @rem;
    padding-bottom: 18 * @rem;
    overflow: unset;
    background: #fff url(~@/assets/images/exchange-bg.png) center top no-repeat;
    background-size: 306 * @rem 140 * @rem;
    .box-icon {
      width: 111 * @rem;
      height: 66 * @rem;
      background: url(~@/assets/images/clock-in/box-coin-new.png) center top
        no-repeat;
      background-size: 111 * @rem 66 * @rem;
      margin: -33 * @rem auto 0;
    }
    .box-success-text {
      width: 94 * @rem;
      height: 23 * @rem;
      background: url(~@/assets/images/clock-in/box-success-text.png) center
        center no-repeat;
      background-size: 94 * @rem 23 * @rem;
      margin: 8 * @rem auto 0;
    }
    .info-content {
      .info-text {
        font-size: 14 * @rem;
        color: #333333;
        text-align: center;
        margin-top: 0 * @rem;
        line-height: 18 * @rem;
        width: 220 * @rem;
        margin: 0 auto;
        &.red {
          color: @themeColor;
          font-weight: 400;
        }
        &.blue {
          color: #6b7eff;
        }
        &.small {
          width: unset;
          font-size: 11 * @rem;
          line-height: 14 * @rem;
          margin-top: 11 * @rem;
        }
      }
      .box-popup-text {
        font-size: 15 * @rem;
        color: #242840;
        text-align: center;
        margin-top: 0 * @rem;
        line-height: 20 * @rem;
        width: 220 * @rem;
        margin: 10 * @rem auto 0;
        &.blue {
          color: #6b7eff;
        }
        &.small {
          width: unset;
          font-size: 12 * @rem;
          line-height: 15 * @rem;
          margin-top: 20 * @rem;
        }
      }
    }
    .btn-bar {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 18 * @rem auto 0;
      padding: 0 25 * @rem;
      .success-conform,
      .vip-conform {
        flex: 1;
        height: 36 * @rem;
        border-radius: 19 * @rem;
        background: linear-gradient(151deg, #5aa6ff 0%, #6c7aff 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 15 * @rem;
        font-weight: bold;
        color: #ffffff;
        margin: 0 5 * @rem;
      }
      .success-conform {
        background: #e9f4ff;
        color: #686eff;
      }
    }
  }
  .sign-in-popup-dislog {
    width: 300 * @rem;
    background: url(~@/assets/images/welfare/welfare-center/sign-in-popup-top-bg.png)
      no-repeat top center;
    background-size: 300 * @rem 218 * @rem;
    overflow: unset;

    .top-container {
      padding-top: 56 * @rem;
      padding-left: 27 * @rem;

      .popup-title {
        width: 140 * @rem;
        height: 20 * @rem;
        background: url(~@/assets/images/welfare/welfare-center/sign-in-popup-title.png)
          no-repeat;
        background-size: 140 * @rem 20 * @rem;
      }
      .tips {
        font-weight: 400;
        font-size: 12 * @rem;
        color: #304845;
        line-height: 18 * @rem;
        text-align: left;
        margin-top: 9 * @rem;
        overflow: hidden;
        span {
          color: #ff8819;
          line-height: 24 * @rem;
          font-size: 16 * @rem;
          font-weight: bold;
        }
      }
    }
    .reward-list {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 10 * @rem 10 * @rem;
      padding: 0 * @rem 14 * @rem;
      margin-top: 33 * @rem;
      position: relative;
      z-index: 1;
      .reward-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 88 * @rem;
        background: rgba(245, 245, 246, 0.58);
        border-radius: 9 * @rem;
        &.last {
          grid-column: ~'2 / 4';
          padding: 0 18 * @rem;
          position: relative;
          .days {
            margin-right: auto;
            margin-top: 22 * @rem;
          }
          .reward-icon {
            width: 70 * @rem;
            height: 38 * @rem;
            margin-left: auto;
            margin-top: 0;
            background-size: 70 * @rem 38 * @rem;
            position: absolute;
            top: 50%;
            right: 20 * @rem;
            transform: translateY(-50%);
          }
          .coins {
            margin-right: auto;
            margin-top: 11 * @rem;
          }
        }
        .days {
          font-size: 14 * @rem;
          color: #242840;
          font-weight: bold;
          line-height: 14 * @rem;
          margin-top: 12 * @rem;
        }
        .reward-icon {
          width: 42 * @rem;
          height: 24 * @rem;
          .image-bg('~@/assets/images/clock-in/coin-new-2.png');
          background-size: auto 24 * @rem;
          margin-top: 7 * @rem;
          &.svip {
            background-image: url('~@/assets/images/clock-in/svip-new.png');
          }
        }
        .coins {
          padding: 0 5 * @rem;
          height: 18 * @rem;
          line-height: 18 * @rem;
          border-radius: 9 * @rem;
          background-color: #ffffff;
          text-align: center;
          font-size: 10 * @rem;
          font-weight: 500;
          color: #ff8819;
          white-space: nowrap;
          margin-top: 7 * @rem;
          &.can {
            color: rgba(255, 136, 25, 1);
            background-color: rgba(255, 239, 197, 0.7);
          }
          &.had {
            color: rgba(121, 121, 121, 1);
            background-color: rgba(238, 238, 238, 1);
          }
        }
      }
    }
    .popup-main-box {
      margin-top: -129 * @rem;
      padding-top: 129 * @rem;
      background-color: #fff;
      overflow: hidden;
      border-radius: 0 0 16 * @rem 16 * @rem;
    }
    .day-list {
      display: flex;
      align-items: center;
      padding: 14 * @rem 3 * @rem 9 * @rem;
      margin: 12 * @rem 14 * @rem 0;
      background-color: #f9f9fa;

      .day-item {
        flex-shrink: 0;
        position: relative;
        width: 38 * @rem;
        z-index: 2;

        &:last-of-type {
          .day-between-icon {
            display: none;
          }
          .day-line-after {
            display: none;
          }
        }
        &:first-of-type {
          .day-line-before {
            display: none;
          }
        }
        &.gray {
          filter: grayscale(1);

          .text {
            color: #666;
          }
        }
        .day-between-icon {
          width: 5 * @rem;
          height: 2 * @rem;
          background-color: #ededef;
          border-radius: 15 * @rem;
          position: absolute;
          top: 12 * @rem;
          right: -1 * @rem;
        }

        .day-icon {
          display: block;
          width: 28 * @rem;
          height: 27 * @rem;
          background: url(~@/assets/images/welfare/welfare-center/ten-coin-icon.png)
            no-repeat;
          background-size: 28 * @rem 27 * @rem;
          margin: 0 auto;
        }

        .day-dot {
          display: block;
          width: 8 * @rem;
          height: 8 * @rem;
          border-radius: 50%;
          background-color: #e3e3e8;
          margin: 5 * @rem auto 0;
          position: relative;

          &.today {
            background-color: #ff8819;
          }
        }

        .day-line-before {
          display: block;
          width: 30 * @rem;
          height: 2 * @rem;
          background-color: #ededef;
          position: absolute;
          top: 50%;
          left: -30 * @rem;
          transform: translateY(-50%);
        }
        .day-line-after {
          display: block;
          width: 30 * @rem;
          height: 2 * @rem;
          background-color: #ededef;
          position: absolute;
          top: 50%;
          right: -30 * @rem;
          transform: translateY(-50%);
        }

        .text {
          height: 13 * @rem;
          font-size: 10 * @rem;
          color: #242840;
          line-height: 13 * @rem;
          text-align: center;
          margin: 4 * @rem auto 0;

          &.active {
            color: #ff8819;
          }
        }

        &.had {
          z-index: 1;
          .day-icon {
            background-image: url(~@/assets/images/welfare/welfare-center/ten-coin-icon-had.png);
          }
          .day-dot {
            background-color: #ff8819;
          }
          .day-line-before,
          .day-line-after {
            background-color: #ff8819;
          }
        }
      }
    }
    .operation {
      display: flex;
      align-items: center;
      padding: 21 * @rem 20 * @rem 0;
      justify-content: space-between;
      .clock-btn {
        width: 120 * @rem;
        height: 40 * @rem;
        background: #ecfbf4;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 30 * @rem;
        font-size: 15 * @rem;
        line-height: 19 * @rem;
        color: @themeColor;
        font-weight: bold;
        position: relative;

        span {
          font-weight: normal;
          color: @themeColor;
          font-size: 9 * @rem;
          line-height: 12 * @rem;
        }
        &.svip {
          background: #1cce94;
          color: #fff;
          span {
            color: #fff;
          }

          .svip-tips {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 16 * @rem;
            padding: 0 8 * @rem;
            font-weight: bold;
            font-size: 9 * @rem;
            color: #c05c2a;
            text-align: center;
            border-radius: 9 * @rem 9 * @rem 9 * @rem 0;
            box-sizing: border-box;
            background: linear-gradient(
              90deg,
              #ffeaab 0%,
              #ffe0dd 54%,
              #ffbdcf 100%
            );
            border: 1 * @rem solid #ffffff;
            position: absolute;
            top: -13 * @rem;
            right: -7 * @rem;
          }
        }
      }
      .svip-sign-in {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 238 * @rem;
        height: 40 * @rem;
        border-radius: 32 * @rem;
        background-color: #1cce94;
        margin: 0 auto;
        font-weight: 500;
        font-size: 15 * @rem;
        color: #ffffff;
        line-height: 19 * @rem;
        text-align: center;
      }
    }
    .no-notice {
      text-align: center;
      color: #999;
      font-size: 12 * @rem;
      padding: 16 * @rem 0;
      line-height: 12 * @rem;
      .content {
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 12 * @rem;
        .gou {
          width: 12 * @rem;
          height: 12 * @rem;
          background: url(~@/assets/images/welfare/welfare-center/select-no1.png)
            center center no-repeat;
          background-size: 12 * @rem 12 * @rem;
          margin-right: 4 * @rem;
        }
      }
      &.remember {
        .content {
          .gou {
            background-image: url(~@/assets/images/welfare/welfare-center/select-yes1.png);
          }
        }
      }
    }
    .close-btn {
      width: 28 * @rem;
      height: 28 * @rem;
      background: url(~@/assets/images/welfare/welfare-center/popup-close-btn.png)
        no-repeat;
      background-size: 28 * @rem 28 * @rem;
      position: absolute;
      bottom: -48 * @rem;
      left: 50%;
      transform: translateX(-50%);
    }
  }
  /* 签到成功弹窗 */
  .sign-success {
    width: 282 * @rem;
    height: 358 * @rem;
    .image-bg('~@/assets/images/clock-in/sign-success-bg.png');
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    .dialog-close {
      width: 14 * @rem;
      height: 14 * @rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0;
      position: absolute;
      right: 12 * @rem;
      top: 74 * @rem;
      img {
        display: block;
        width: 14 * @rem;
        height: 14 * @rem;
      }
    }
    .success-info {
      box-sizing: border-box;
      padding-top: 17 * @rem;
      width: 252 * @rem;
      height: 184 * @rem;
      margin: 158 * @rem auto 0;
      border-radius: 8 * @rem;
      background-color: #ffffff;
      overflow: hidden;
      position: relative;
      .info-content {
        .info-text {
          font-size: 15 * @rem;
          color: #000000;
          text-align: center;
          margin-top: 8 * @rem;
          line-height: 21 * @rem;
          &.red {
            color: #ff7554;
            font-weight: bold;
          }
          &.small {
            font-size: 12 * @rem;
            line-height: 12 * @rem;
            margin-top: 2 * @rem;
          }
        }
      }
      .success-conform {
        width: 238 * @rem;
        height: 40 * @rem;
        border-radius: 40 * @rem;
        background: linear-gradient(151deg, #5aa6ff 0%, #6c7aff 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 15 * @rem;
        font-weight: 500;
        color: #ffffff;
        position: absolute;
        bottom: 16 * @rem;
        left: 50%;
        transform: translateX(-50%);
      }
      .success-conform-text {
        font-weight: 500;
        font-size: 12 * @rem;
        color: #777777;
        line-height: 17 * @rem;
        height: 17 * @rem;
        text-align: center;
        font-style: normal;
        text-transform: none;
        white-space: nowrap;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
  /*非svip点击补签弹窗*/
  .repair-sign-tip-popup {
    width: 300 * @rem;
    background: transparent;
    overflow: visible;
    .logo-icon {
      position: relative;
      background: url('~@/assets/images/clock-in/repair-sign-tip-icon.png')
        no-repeat -23 * @rem -4 * @rem;
      background-size: 164 * @rem 96 * @rem;
      width: 117 * @rem;
      height: 92 * @rem;
      top: -35 * @rem;
      z-index: 3;
      margin: 0 auto;
    }
    .dialog-content {
      box-sizing: border-box;
      position: relative;
      background-color: #fff;
      border-radius: 16 * @rem;
      margin-top: -83 * @rem;
      z-index: 2;
      width: 300 * @rem;
      height: 192 * @rem;
      text-align: center;
      .box-bg {
        background: url(~@/assets/images/clock-in/repair-sign-tip-bg.png)
          no-repeat 0 0;
        background-size: 300 * @rem 160 * @rem;
        width: 300 * @rem;
        height: 139 * @rem;
        padding: 63 * @rem 31 * @rem 20 * @rem;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .title {
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          width: 187 * @rem;
          height: 40 * @rem;
          font-weight: bold;
          font-size: 16 * @rem;
          color: #242840;
          line-height: 20 * @rem;
          margin: 0 auto;
        }

        .btn {
          width: 238 * @rem;
          height: 40 * @rem;
          background: linear-gradient(151deg, #5aa6ff 0%, #6c7aff 100%);
          border-radius: 40 * @rem;
          font-weight: 500;
          font-size: 15 * @rem;
          color: #ffffff;
          line-height: 21 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .btn-box {
        height: 53 * @rem;
        line-height: 53 * @rem;
        display: flex;
        align-items: center;
        border: 0.5 * @rem solid rgba(161, 163, 173, 0.21);
        .btn-cancel {
          flex: 1;
          width: 51 * @rem;
          height: 20 * @rem;
          font-weight: 400;
          font-size: 16 * @rem;
          color: #93999f;
          line-height: 20 * @rem;
          text-align: center;
        }
        .btn-open {
          border-left: 0.5 * @rem solid rgba(161, 163, 173, 0.21);
          flex: 1;
          height: 20 * @rem;
          font-weight: 500;
          font-size: 16 * @rem;
          color: #191b1f;
          line-height: 20 * @rem;
          text-align: center;
        }
      }
    }
  }
  /* svip补签提示弹窗 */
  .svip-repair-sign-tip-popup {
    width: 300 * @rem;
    background: transparent;
    overflow: visible;
    .logo-icon {
      position: relative;
      background: url('~@/assets/images/clock-in/svip-repair-sign-tip-icon.png')
        no-repeat -23 * @rem -4 * @rem;
      background-size: 164 * @rem 97 * @rem;
      width: 117 * @rem;
      height: 93 * @rem;
      top: -34 * @rem;
      z-index: 3;
      margin: 0 auto;
    }
    .dialog-content {
      box-sizing: border-box;
      position: relative;
      background-color: #fff;
      border-radius: 16 * @rem;
      margin-top: -83 * @rem;
      z-index: 2;
      width: 300 * @rem;
      height: 192 * @rem;
      text-align: center;
      .box-bg {
        background: url(~@/assets/images/clock-in/repair-sign-tip-bg.png)
          no-repeat 0 0;
        background-size: 300 * @rem 160 * @rem;
        width: 300 * @rem;
        height: 192 * @rem;
        padding: 63 * @rem 31 * @rem 20 * @rem;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .title {
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          width: 160 * @rem;
          height: 40 * @rem;
          font-weight: bold;
          font-size: 16 * @rem;
          color: #242840;
          line-height: 20 * @rem;
          margin: 0 auto;
        }
        .btn {
          width: 238 * @rem;
          height: 40 * @rem;
          background: #1cce94;
          border-radius: 40 * @rem;
          font-weight: 500;
          font-size: 15 * @rem;
          color: #ffffff;
          line-height: 21 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
    .close-btn {
      width: 28 * @rem;
      height: 28 * @rem;
      background: url(~@/assets/images/welfare/welfare-center/popup-close-btn.png)
        no-repeat;
      background-size: 28 * @rem 28 * @rem;
      position: absolute;
      bottom: -48 * @rem;
      left: 50%;
      transform: translateX(-50%);
    }
  }
  .repair-sign-tip {
    box-sizing: border-box;
    width: 290 * @rem;
    height: 170 * @rem;
    border-radius: 10 * @rem;
    background-color: #fff;
    padding: 28 * @rem 42 * @rem;
    .tip-text {
      font-size: 15 * @rem;
      color: #333333;
      line-height: 24 * @rem;
      text-align: center;
      margin-top: 6 * @rem;
      letter-spacing: 2 * @rem;
      span {
        font-weight: bold;
        color: #ffb400;
      }
    }
    .repair-btn {
      width: 180 * @rem;
      height: 38 * @rem;
      border-radius: 6 * @rem;
      background-color: #47a83a;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      font-size: 15 * @rem;
      margin: 25 * @rem auto 0;
    }
  }
  /* 补签成功弹窗 */
  .repair-sign-success {
    display: flex;
    flex-direction: column;
    align-items: center;
    .repair-num {
      font-size: 20 * @rem;
      color: #fff;
      font-weight: bold;
      span {
        color: #ffc900;
      }
    }
    .coin-icon {
      width: 123 * @rem;
      height: 124 * @rem;
      margin-top: 18 * @rem;
    }
    .text {
      margin-top: 10 * @rem;
      text-align: center;
      font-size: 15 * @rem;
      color: #ffffff;
      line-height: 24 * @rem;
    }
  }
  /* 连续签到领宝箱弹窗 */
  .gold-box-success {
    box-sizing: border-box;
    width: 290 * @rem;
    height: 150 * @rem;
    border-radius: 10 * @rem;
    background-color: #fff;
    padding: 28 * @rem 10 * @rem;
    overflow: hidden;
    .success-text {
      font-size: 15 * @rem;
      color: #333333;
      text-align: center;
      margin-top: 20 * @rem;
      span {
        color: #ffb400;
        font-weight: bold;
      }
    }
    .confirm {
      display: flex;
      justify-content: flex-end;
      font-size: 16 * @rem;
      color: #40b640;
      margin-top: 30 * @rem;
      padding: 10 * @rem;
      float: right;
      letter-spacing: 2 * @rem;
    }
  }
  /* 宝箱分享弹窗 */
  .gold-box-share {
    box-sizing: border-box;
    position: relative;
    width: 300 * @rem;
    background-color: #fff;
    border-radius: 10 * @rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 24 * @rem 13 * @rem 30 * @rem;
    .close {
      width: 40 * @rem;
      height: 40 * @rem;
      background: url(~@/assets/images/clock-in/close-share.png) center center
        no-repeat;
      background-size: 21 * @rem 21 * @rem;
      position: absolute;
      right: 0;
      top: 0;
    }
    .top-text {
      font-size: 20 * @rem;
      color: #333333;
      &.orange {
        color: #ff641d;
      }
    }
    .title {
      font-size: 25 * @rem;
      font-weight: bold;
      color: #000000;
      margin-top: 10 * @rem;
      span {
        color: #ff641d;
        font-weight: bold;
      }
    }
    .tip {
      font-size: 16 * @rem;
      color: #ff8549;
      margin-top: 10 * @rem;
    }
    .sub-tip {
      font-size: 14 * @rem;
      color: #999999;
      margin-top: 10 * @rem;
    }
    .help-man {
      margin-top: 40 * @rem;
    }
    .help-svip {
      font-size: 18 * @rem;
      color: #000000;
      margin-top: 20 * @rem;
    }
    .avatar {
      width: 50 * @rem;
      height: 50 * @rem;
      border-radius: 50%;
      overflow: hidden;
      margin-top: 35 * @rem;
    }
    .avatar-icon {
      margin-top: 15 * @rem;
    }
    .operate {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 48 * @rem;
      .common-invite {
        width: 130 * @rem;
        height: 43 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16 * @rem;
        border-radius: 5 * @rem;
        border: 1 * @rem solid #bfbfbf;
        color: #666666;
      }
      .svip-invite {
        width: 130 * @rem;
        height: 43 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16 * @rem;
        border-radius: 5 * @rem;
        background-color: #ffa820;
        color: #fff;
      }
      .confirm {
        width: 200 * @rem;
        height: 43 * @rem;
        border-radius: 5 * @rem;
        background-color: #ffa820;
        color: #fff;
        font-size: 16 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
      }
    }
    .get-gold-num {
      font-size: 28 * @rem;
      color: #ff8a00;
      font-weight: bold;
      margin-top: 15 * @rem;
    }
    .svip-num {
      font-size: 28 * @rem;
      color: #ff8a00;
      margin-top: 60 * @rem;
    }
  }
  .yindao-popup {
    width: 250 * @rem;
    padding: 40 * @rem 35 * @rem 20 * @rem;
    background-color: #fff;
    border-radius: 10 * @rem;
    line-height: 28 * @rem;
    .close {
      position: absolute;
      top: 15 * @rem;
      right: 15 * @rem;
      width: 16 * @rem;
      height: 16 * @rem;
      background-image: url(~@/assets/images/close-black.png);
      background-size: 100%;
      background-repeat: no-repeat;
    }
    .text {
      text-align: center;
      font-size: 15 * @rem;
    }
    .down-arrow {
      margin: 15 * @rem auto 0;
      width: 16 * @rem;
      height: 20 * @rem;
      background-image: url(~@/assets/images/clock-in/down-arrow.png);
      background-size: 100%;
      background-repeat: no-repeat;
      -webkit-animation: downward 0.8s ease-in-out infinite;
      animation: downward 0.8s ease-in-out infinite;
    }
  }
  .award-before-dialog {
    width: 300 * @rem;
    background: transparent;
    overflow: visible;
    .dialog-content {
      box-sizing: border-box;
      position: relative;
      border-radius: 16 * @rem;
      z-index: 2;
      padding: 32 * @rem 12 * @rem 20 * @rem;
      background: #fff url('~@/assets/images/clock-in/sign-success-bg1.png')
        no-repeat 0 0;
      background-size: 300 * @rem 264 * @rem;
      width: 300 * @rem;
      text-align: center;
      .title {
        white-space: nowrap;
        height: 31 * @rem;
        font-weight: normal;
        font-size: 22 * @rem;
        color: #191b1f;
        line-height: 31 * @rem;
        text-align: center;
        font-style: normal;
        text-transform: none;
        font-weight: bold;
      }
      .detail {
        width: 80 * @rem;
        height: 80 * @rem;
        position: relative;
        margin: 11 * @rem auto;

        img {
          width: 100%;
          height: 100%;
        }
        .double {
          height: 16 * @rem;
          background: #ff674c;
          border-radius: 8 * @rem 8 * @rem 8 * @rem 2 * @rem;
          padding: 0 5 * @rem;
          font-weight: 500;
          font-size: 10 * @rem;
          color: #ffffff;
          line-height: 16 * @rem;
          text-align: center;
          position: absolute;
          top: 6 * @rem;
          right: -28 * @rem;
        }
        .gold-num {
          height: 20 * @rem;
          font-weight: bold;
          font-size: 14 * @rem;
          color: #191b1f;
          line-height: 16 * @rem;
          position: absolute;
          bottom: 4 * @rem;
          left: 53 * @rem;
          white-space: nowrap;
        }

        .svip-num {
          height: 20 * @rem;
          font-weight: bold;
          font-size: 14 * @rem;
          color: 191B1F;
          line-height: 16 * @rem;
          position: absolute;
          bottom: 4 * @rem;
          left: 53 * @rem;
          white-space: nowrap;
        }
      }
      .msg {
        font-weight: 500;
        font-size: 12 * @rem;
        color: #93999f;
        text-align: center;
        line-height: 17 * @rem;
        margin-top: 4 * @rem;
        overflow: hidden;
        .msg-number {
          margin-top: 4 * @rem;
        }
      }
      .btns {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 20 * @rem 11 * @rem 0;
      }
      .btn {
        width: 100%;
        max-width: 238 * @rem;
        height: 40 * @rem;
        line-height: 40 * @rem;
        background: #f0f1f5;
        border-radius: 30 * @rem;
        font-weight: 500;
        font-size: 15 * @rem;
        color: #93999f;
        text-align: center;
        margin: 0 auto;
        margin-right: 14 * @rem;

        &:last-of-type {
          background: #1cce94;
          color: #fff;
          margin-right: auto;
        }
      }
      .dialog-close-btn {
        position: absolute;
        bottom: -58 * @rem;
        left: 50%;
        transform: translate(-50%, 0);
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: url(~@/assets/images/clock-in/sign-close.png) no-repeat 0 0;
        background-size: 28 * @rem 28 * @rem;
        width: 28 * @rem;
        height: 28 * @rem;
      }
    }
  }
  .award-dialog {
    width: 300 * @rem;
    background: transparent;
    overflow: visible;
    .logo-icon {
      width: 164 * @rem;
      height: 97 * @rem;
      .image-bg('~@/assets/images/clock-in/sign-success-logo3.png');
      margin: 0 auto;
      position: relative;
      z-index: 3;
      &.svip {
        background-image: url('~@/assets/images/clock-in/sign-success-logo5.png');
      }
    }
    .dialog-content {
      box-sizing: border-box;
      position: relative;
      border-radius: 16 * @rem;
      z-index: 2;
      padding: 49 * @rem 12 * @rem 20 * @rem;
      margin-top: -48 * @rem;
      background: #fff url('~@/assets/images/clock-in/sign-success-bg1.png')
        no-repeat 0 0;
      background-size: 300 * @rem 264 * @rem;
      width: 300 * @rem;
      text-align: center;
      .title {
        white-space: nowrap;
        height: 31 * @rem;
        font-weight: normal;
        font-size: 22 * @rem;
        color: @themeColor;
        line-height: 31 * @rem;
        text-align: center;
        font-style: normal;
        text-transform: none;
        font-weight: bold;
      }
      .msg {
        font-size: 15 * @rem;
        color: #191b1f;
        text-align: center;
        line-height: 19 * @rem;
        margin-top: 10 * @rem;
        overflow: hidden;
        .msg-number {
          margin-top: 3 * @rem;
          font-weight: bold;
        }
      }
      .tips {
        height: 15 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        color: @themeColor;
        line-height: 15 * @rem;
        text-align: center;
        margin-top: 20 * @rem;
      }
      .btns {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 14 * @rem 11 * @rem 0;
      }
      .btn {
        width: 100%;
        max-width: 238 * @rem;
        height: 40 * @rem;
        line-height: 40 * @rem;
        background: #ecfbf4;
        border-radius: 30 * @rem;
        font-weight: 500;
        font-size: 15 * @rem;
        color: @themeColor;
        text-align: center;
        margin: 0 auto;
        margin-right: 14 * @rem;

        &:last-of-type {
          background: #1cce94;
          color: #fff;
          margin-right: auto;
        }
      }
      .dialog-close-btn {
        position: absolute;
        bottom: -58 * @rem;
        left: 50%;
        transform: translate(-50%, 0);
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: url(~@/assets/images/clock-in/sign-close.png) no-repeat 0 0;
        background-size: 28 * @rem 28 * @rem;
        width: 28 * @rem;
        height: 28 * @rem;
      }
    }
  }
  .no-svip-sign-dialog {
    background: #fff url(~@/assets/images/welfare/welfare-popup-bg-blue.png)
      no-repeat top;
    background-size: 100% auto;
    width: 300 * @rem;
    border-radius: 16 * @rem;
    padding-top: 50 * @rem;
    overflow: unset;
    .top-icon {
      width: 164 * @rem;
      height: 105 * @rem;
      .image-bg('~@/assets/images/welfare/popup-top-svip-icon.png');
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: -58 * @rem;
    }
    .no-svip-tip {
      .line {
        height: 25 * @rem;
        font-weight: bold;
        font-size: 18 * @rem;
        color: #191b1f;
        line-height: 25 * @rem;
        text-align: center;
        margin-bottom: 20 * @rem;
      }
      .small-tips {
        display: flex;
        align-items: center;
        height: 17 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #93999f;
        line-height: 17 * @rem;
        margin-top: 7 * @rem;
        padding-left: 62 * @rem;

        &::before {
          content: '';
          display: block;
          width: 6 * @rem;
          height: 6 * @rem;
          background: url(~@/assets/images/welfare/tips-star-icon.png) no-repeat;
          background-size: 6 * @rem 6 * @rem;
          margin-right: 4 * @rem;
        }
      }
    }
    .no-notice {
      text-align: center;
      color: #999;
      font-size: 12 * @rem;
      padding: 16 * @rem 0 21 * @rem;
      line-height: 12 * @rem;
      .content {
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 12 * @rem;
        .gou {
          width: 12 * @rem;
          height: 12 * @rem;
          background: url(~@/assets/images/welfare/select-no.png) center center
            no-repeat;
          background-size: 12 * @rem 12 * @rem;
          margin-right: 4 * @rem;
        }
      }
      &.remember {
        .content {
          .gou {
            background-image: url(~@/assets/images/welfare/select-yes.png);
          }
        }
      }
    }
    .operation {
      display: flex;
      align-items: center;
      padding: 0 22 * @rem;
      justify-content: space-between;
      margin: 29 * @rem auto 0;
      .clock-btn {
        width: 120 * @rem;
        height: 40 * @rem;
        background: #ecfbf4;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 30 * @rem;
        font-size: 15 * @rem;
        line-height: 19 * @rem;
        color: @themeColor;
        font-weight: bold;
        position: relative;

        span {
          font-weight: normal;
          color: @themeColor;
          font-size: 9 * @rem;
          line-height: 12 * @rem;
        }
        &.svip {
          background: #1cce94;
          color: #fff;
          span {
            color: #fff;
          }

          .svip-tips {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 18 * @rem;
            padding: 0 8 * @rem;
            font-weight: bold;
            font-size: 9 * @rem;
            color: #c05c2a;
            line-height: 11 * @rem;
            text-align: center;
            border-radius: 9 * @rem 9 * @rem 9 * @rem 0;
            box-sizing: border-box;
            background: linear-gradient(
              90deg,
              #ffeaab 0%,
              #ffe0dd 54%,
              #ffbdcf 100%
            );
            border: 1 * @rem solid #ffffff;
            position: absolute;
            top: -13 * @rem;
            right: -7 * @rem;
          }
        }
      }
    }
    .close-btn {
      width: 28 * @rem;
      height: 28 * @rem;
      background: url(~@/assets/images/welfare/popup-bottom-close-btn.png)
        no-repeat;
      background-size: 28 * @rem 28 * @rem;
      position: absolute;
      bottom: -48 * @rem;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
.rule-popup {
  background: #fff;
  overflow: hidden;
  .popup-content {
    padding: 14 * @rem 0 0;

    .popup-title {
      width: 112 * @rem;
      height: 40 * @rem;
      margin: 0 auto;
    }
    /deep/ .rule-content {
      max-height: 400 * @rem;
      font-size: 14 * @rem;
      line-height: 24 * @rem;
      color: #60666c;
      margin-top: 6 * @rem;
      padding: 0 18 * @rem 10 * @rem;
      overflow-y: auto;
      a {
        color: @themeColor !important;
      }
    }
  }
  .close {
    width: 14 * @rem;
    height: 14 * @rem;
    background: url('~@/assets/images/welfare/welfare-center/clock-in-close-btn1.png')
      no-repeat center center;
    background-size: 14 * @rem 14 * @rem;
    position: absolute;
    right: 14 * @rem;
    top: 14 * @rem;
  }
}
</style>
