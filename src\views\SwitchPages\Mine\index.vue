<template>
  <rubber-band :topColor="'#FBFBFC'" :bottomColor="'#f5f5f6'">
    <div class="mine-page">
      <nav-bar-2
        title=""
        bgStyle="transparent"
        :placeholder="false"
        :bgColor="`rgba(255,255,255, ${navbarOpacity})`"
        :border="navbarOpacity ? true : false"
      >
        <template #left>
          <div class="page-title">{{ navbarOpacity ? $t('我的') : '' }}</div>
        </template>
        <template #right>
          <div class="top-icon-list">
            <div @click="goToSetting" class="icon setting"></div>
            <div @click="goToNotice" class="icon message">
              <div class="dot-red" v-if="unreadCount.sum > 0"></div>
              <!-- <div class="dot" v-if="unreadCount.sum > 0">
                {{ unreadCount.sum > 99 ? '99+' : unreadCount.sum }}
              </div> -->
            </div>
            <div @click="goToKefu" class="icon kefu">
              <div class="dot-red" v-if="unReadMesNumber > 0">{{
                unReadMesNumber > 99 ? '99+' : unReadMesNumber
              }}</div>
            </div>
          </div>
        </template>
      </nav-bar-2>
      <div class="top-user-info section">
        <div class="top">
          <status-bar></status-bar>
          <div class="icon-list"></div>
        </div>
        <div class="center">
          <div
            class="user-info"
            v-if="userInfo.username"
            @click.stop="goToUserInfo"
          >
            <user-avatar class="avatar"></user-avatar>
            <div class="detail">
              <div class="big-text">
                <em>{{ userInfo.nickname }}</em>
                <span></span>
              </div>
              <div class="small-text">
                {{ $t('账号') }}:{{ userInfo.username }}
              </div>
              <div class="tag">
                <span
                  @click.stop="goToSvip"
                  class="item item3"
                  v-if="userInfo.is_svip"
                >
                  <img
                    src="@/assets/images/mine/new/svip_level_icon.png"
                    alt=""
                  />
                  <em>SVIP</em>
                </span>
                <span @click.stop="goToExpHelp" class="item item1">
                  <img :src="userInfo.exp_level_img" alt="" />
                  <em>{{ userInfo.exp_level_name }}</em>
                </span>
                <span @click.stop="goToPayHelp" class="item item2">
                  <img
                    src="@/assets/images/mine/new/pay_level_icon.png"
                    alt=""
                  />
                  <em>{{ userInfo.pay_level_name }}</em>
                </span>
              </div>
            </div>
            <div
              @click.stop="handleActionCode(sectionList[0].info)"
              v-if="sectionList[0].type == 'sign'"
              class="sign-in"
            ></div>
          </div>
          <div
            class="user-info no-login"
            v-if="!userInfo.username"
            @click="goToLogin"
          >
            <user-avatar class="avatar"></user-avatar>
            <div class="detail">
              <div class="big-text"
                ><em>{{ $t('登录/注册') }}</em></div
              >
              <!-- <div class="small-text">{{
                $t('新用户注册立送64800元礼券')
              }}</div> -->
            </div>
            <div
              @click.stop="handleActionCode(sectionList[0].info)"
              v-if="sectionList[0].type == 'sign'"
              class="sign-in"
            ></div>
          </div>
        </div>
        <div class="bottom"></div>
      </div>
      <template v-for="(section, sectionIndex) in sectionList">
        <div
          class="svip-container section"
          :key="sectionIndex"
          v-if="section.type == 'svip'"
        >
          <div class="left">
            <div class="svip-img"></div>
            <div class="svip-right">
              {{
                userInfo.is_svip
                  ? '已享12+项SVIP会员特权'
                  : '开通SVIP，尊享金币兑换等12+项特权'
              }}
            </div>
          </div>
          <div class="right">
            <div class="svip-btn btn" @click="goToSvip">
              {{ userInfo.is_svip ? '立即续费' : '立即开通' }}
              <div class="discount" v-if="section.svip_zhekou_txt">
                <marquee-text :text="section.svip_zhekou_txt"></marquee-text>
              </div>
            </div>
            <div v-if="userInfo.sviptime" class="small-text">
              {{ svipTime }}到期
            </div>
          </div>
        </div>
        <div
          class="property-container section"
          :class="{ mb44: section.act_info }"
          :key="sectionIndex"
          v-if="section.type == 'my_asset'"
        >
          <div class="ptb-cont">
            <div class="left">
              <div class="property-title">{{ section.title }}</div>
              <div class="ptb-num" @click="goToPlatformCoinDetail">
                <span v-if="section.ptb == '-'" class="none-ptb">{{
                  section.ptb
                }}</span>
                <span v-else :class="{ fzs: section.ptb.length > 10 }">{{
                  section.ptb
                }}</span>
                <i v-if="section.ptb != '-'">
                  <template v-if="section.ptb_fake">
                    (含{{ section.ptb_fake }}绑定)
                  </template>
                  平台币</i
                >
                <!-- <i v-else>平台币</i> -->
              </div>
            </div>
            <div class="ptb-recharge" @click="goToPlatformCoin">{{
              $t('充值平台币')
            }}</div>
          </div>
          <div class="coupon-info" :class="{ h81: !userInfo.sqk_remain_days }">
            <div class="coupon-item" @click="goToMyCoupon">
              <span>{{ section.coupon_count }}</span>
              <em>{{ $t('代金券') }}</em>
            </div>
            <!-- <div class="coupon-item" @click="goToMyGift">
              <span>{{ section.card_count }}</span>
              礼包
            </div> -->
            <div class="coupon-item" @click="goToWelfare">
              <span>{{ section.gold }}</span>
              <em>{{ $t('金币') }}</em>
            </div>
            <div
              class="coupon-item savings-card-item"
              :class="{
                notBg: !section.sqk_day_text,
              }"
              @click="goToSavingsCard"
            >
              <template v-if="section.sqk_day_text">
                <span>{{ $t('省钱卡') }}</span>
                <em>{{ section.sqk_day_text }}</em>
              </template>
              <div class="savings-card-img" v-else>
                <img :src="section.sqk_img" alt="" />
              </div>
            </div>
          </div>
          <div
            class="recharge-activity"
            v-if="section.act_info"
            @click="goToPlatformCoin"
          >
            <span>{{ section.act_info.publicize_text }}</span>
          </div>
        </div>
        <div
          v-if="section.type == 'user_ad'"
          class="banner-info section"
          @click="handleActionCode(section.user_ad)"
          :key="sectionIndex"
        >
          <img :src="section.user_ad.banner_url" />
        </div>
        <div
          v-if="section.type == 'modular' && section.list.length"
          class="modular-container section"
          :key="sectionIndex"
        >
          <div class="modular-title">{{ section.title }}</div>
          <div class="modular-list">
            <div
              class="modular-item"
              v-for="modular in section.list"
              :key="modular.action"
              @click="goToFunction(modular)"
            >
              <img :src="modular.icon" alt="" />
              <span>{{ modular.title }}</span>
            </div>
          </div>
        </div>
        <div
          class="function-section section"
          v-if="section.type == 'modular_across' && section.list.length"
          :key="sectionIndex"
        >
          <div class="function-list">
            <div
              class="function-item"
              v-for="(item, index) in section.list"
              :key="index"
              @click="goToFunction(item)"
            >
              <div class="icon">
                <img :src="item.icon" alt="" />
              </div>
              <div class="center-text">
                <div class="text">{{ item.title }}</div>
                <div class="desc" v-if="item.subtitle">{{ item.subtitle }}</div>
              </div>
              <div class="right-icon"></div>
            </div>
          </div>
        </div>
      </template>
      <!-- 每日金币弹窗 -->
      <van-popup
        v-model="dailyGoldPopup"
        position="center"
        :lock-scroll="false"
        round
        class="daily-gold-container-popup"
        :close-on-popstate="true"
      >
        <div class="daily-gold-container">
          <div class="daily-gold-bg">
            <img src="@/assets/images/mine/daily-gold-bg.png" alt="" />
          </div>
          <div @click="closeDailyGoldPopup()" class="close btn"></div>
          <div class="title">今日任务尚未完成</div>
          <div class="desc">
            完成任务可得
            <div class="gold-icon"></div>
            <span>{{ totalDayGold }}</span
            >金币
          </div>
          <div class="gold-btn" @click="goToTaskDaily">马上领取</div>
        </div>
      </van-popup>
      <change-href :show="userInfo && userInfo.is_official"></change-href>
      <div
        v-if="test"
        @click="
          toPage('Activity', {
            url: 'http://***********:9000/#/25_new_year_activity',
          })
        "
        class="test"
      >
        这里是测试路口
      </div>
      <!-- <div v-if="test" @click="downloadH5Game" class="test">这里是测试路口</div> -->
      <!-- <a href="http://d2.xz3733.com/test/69730.mobileconfig">测试游戏</a> -->
    </div>
  </rubber-band>
</template>

<script>
import { downloadH5Game } from '@/utils/function.js';
import StatusBar from '@/components/status-bar';
import rubberBand from '@/components/rubber-band';
import { mapActions, mapGetters } from 'vuex';
import { BOX_openInNewNavWindow } from '@/utils/box.uni.js';
import { handleActionCode } from '@/utils/actionCode.js';
import {
  ApiUserMyModular,
  ApiUserMyModularV2,
  ApiUserMyModularV3,
  ApiUserGetUserDayGoldTotal,
  ApiUserGetSingleWealth,
} from '@/api/views/users.js';
import { isWebApp } from '@/utils/userAgent.js';
export default {
  name: 'Mine',
  components: {
    StatusBar,
    rubberBand,
  },
  data() {
    return {
      isWebApp,
      navbarOpacity: 0,
      modularVersion: 0,
      sectionList: [
        {
          type: 'svip',
        },
        {
          type: 'my_asset',
          ptb: '0',
          ptb_fake: '0',
          gold: '-',
          card_count: '-',
          coupon_count: '-',
          title: '我的资产',
        },
      ],
      //icon列表
      iconList: [],
      functionList: [],
      bannerInfo: {},
      iconListShow: true, //是否显示完全iconList
      isEditing: false,
      functionCode: {
        1: ['Requite'], // 六倍返还
        2: ['Recycle'], // 小号回收
        3: ['Rebate'], // 返利申请
        4: ['MyGift'], // 我的礼包
        5: ['TurnTable'], // 金币转盘
        6: ['Invite'], // 邀请赚佣金
        7: ['GoldMall'], // 金币商城
        8: ['Zhuanyou'], // 转游中心
        9: ['MyGame'], // 我的游戏
        10: ['XiaohaoManage'], // 小号管理
        11: ['Kefu'], // 联系客服
        12: ['Collection'], // 我的收藏
        13: ['Iframe', { title: '使用指南', url: this.$h5Page.shiyongzhinan }], // 使用指南
        // 14: [""], // 我的问答
        // 15: [""], // 我的发布
        16: ['Feedback'], // 投诉反馈
        17: ['BindWeChat'], // 微信提醒
        18: ['RoleTransfer'], // 交易
        19: ['MyRebate'], // 我的返利
        20: ['Questionnaire'], // 有奖调研
        21: ['PayHelp'], // 财富特权
        22: ['UpGame'], //我要Up
        23: ['FllowUp'], //关注Up主
        24: ['UpMine'], //我的主页(Up)
        25: ['ClockChallenge'], //打卡挑战
        26: ['OrderRecord'], //订单记录
        28: ['Svip'], // SVIP
        29: ['SavingsCard'], //省钱卡
        30: ['AddAssistant'], //添加福利官
        31: ['GoldCoinCenter'], //金币中心
        32: ['CloudHangup'], //云挂机
        33: ['PlatformCoin'], //平台币
      },

      dailyGoldPopup: false, // 每日金币弹窗
      totalDayGold: 0, // 每日金币数量
      rechargeActivity: {
        title: '元旦特惠！充值30返利20元，快去充值吧~',
      },
      currentLevel: {},
      limitLevel: 0,
    };
  },
  computed: {
    ...mapGetters({
      unreadCount: 'system/unreadCount',
      showTransaction: 'system/showTransaction',
      configs: 'system/configs',
      initData: 'system/initData',
    }),
    // 实名制状态
    ifAuthStatus() {
      let res;
      switch (parseInt(this.userInfo.auth_status)) {
        case 0:
          res = this.$t('未实名');
          break;
        case 1:
          res = this.$t('审核中');
          break;
        case 2:
          res = this.$t('已认证');
          break;
        case 3:
          res = this.$t('认证未通过');
          break;
        default:
          res = this.$t('未实名');
          break;
      }
      return res;
    },
    // svip剩余时间
    svipTime() {
      const time = this.$handleTimestamp(this.userInfo.sviptime);
      return `${time.year}-${time.date}`;
    },
    // 测试路口
    test() {
      if (process.env.NODE_ENV == 'development') {
        return true;
      } else {
        return false;
      }
    },
  },
  mounted() {
    window.addEventListener('scroll', this.handleScroll);
    this.$dragging.$on('dragged', value => {});
    this.$dragging.$on('dragend', value => {});
  },
  async activated() {

    // 神策埋点
    this.$sensorsTrack('my_profile_page_view');

    this.getUserLevel();
    await this.getFunctionList();
    await this.checkDailyGold();
  },
  beforeRouteLeave(to, from, next) {
    document.getElementsByTagName('body')[0].style.background = '';
    next(true);
  },
  methods: {
    handleActionCode,
    downloadH5Game,
    closeDailyGoldPopup() {
      this.dailyGoldPopup = false;
    },
    goToSetting() {
      this.CLICK_EVENT('M5_SETTING');
      this.toPage('UserInfo');
    },
    goToNotice() {
      this.CLICK_EVENT('M5_MSG');
      this.toPage('Notice');
    },
    goToKefu() {
      this.CLICK_EVENT('M5_KF');
      // this.toPage('Kefu');
      this.connectQiYu(); //6.5.3改为直接拉起客服
    },
    goToUserInfo() {
      this.CLICK_EVENT('M5_SELF');
      this.toPage('UserInfo');
    },
    goToLogin() {
      this.CLICK_EVENT('M5_LOGIN');
      this.toPage('UserInfo');
    },
    goToExpHelp() {
      this.CLICK_EVENT('M5_LEVEL');
      this.toPage('ExpHelp');
    },
    goToPayHelp() {
      this.CLICK_EVENT('M5_MONEY');
      this.toPage('PayHelp');
    },
    goToGoldCoinCenter() {
      this.CLICK_EVENT('M5_SIGNIN');
      this.toPage('GoldCoinCenter');
    },
    goToPlatformCoinDetail() {
      this.CLICK_EVENT('M5_PTBDTL');
      this.toPage('PlatformCoinDetail');
    },
    goToPlatformCoin() {
      this.CLICK_EVENT('M5_PAYPTB');
      this.toPage('PlatformCoin');
    },
    goToMyCoupon() {
      this.CLICK_EVENT('M5_COUPON');
      this.toPage('MyCoupon');
    },
    goToGoldCoin() {
      this.CLICK_EVENT('M5_GOLDDTL');
      this.toPage('GoldCoin');
    },
    goToGoldCoinExchange() {
      this.CLICK_EVENT('M5_USEGOLD');
      this.toPage('GoldCoinExchange');
    },
    goToMyGift() {
      this.CLICK_EVENT('M5_GIFT');
      this.toPage('MyGift');
    },
    goToSvip() {
      this.CLICK_EVENT('M5_SVIP');
      this.toPage('Svip');
    },
    goToSavingsCard() {
      this.CLICK_EVENT('M5_SAVING');
      this.toPage('SavingsCard');
    },
    goToWelfare() {
      this.CLICK_EVENT('M5_GOLDDTL');
      this.toPage('Welfare');
    },
    clickGoldAd() {
      this.CLICK_EVENT('M5_GOLDADV');
      this.toPage('GoldCoinExchange');
    },
    goToFunction(item) {
      if (item.click_id) {
        this.CLICK_EVENT(item.click_id);
      }
      this.toPage(...this.functionCode[item.action]);
    },
    async checkDailyGold() {
      if (this.userInfo.token) {
        await this.SET_USER_INFO(true);
        let nowDate = new Date().getDate();
        if (nowDate != localStorage.getItem('dailyGoldPopup')) {
          const res = await ApiUserGetUserDayGoldTotal();
          this.totalDayGold = res.data.total_day_gold;
          if (this.totalDayGold) {
            this.dailyGoldPopup = true;
            localStorage.setItem('dailyGoldPopup', nowDate);
          }
        }
      }
    },
    async getFunctionList() {
      const res = await ApiUserMyModularV3();
      this.sectionList = res.data.list;
      this.functionList = res.data.list_v2;
      this.bannerInfo = res.data.user_ad || {};
    },
    // 点击测试链接
    clickTest() {
      this.toPage('WeeklyRewards');
    },
    ...mapActions({
      SET_USER_INFO: 'user/SET_USER_INFO',
    }),
    toPage(name, params = {}) {
      switch (name) {
        case 'UpMine':
          params = { mem_id: this.userInfo.user_id };
          break;
      }
      this.$router.push({ name: name, params: params });
    },
    handleScroll() {
      let scrollTop =
        document.documentElement.scrollTop ||
        window.pageYOffset ||
        document.body.scrollTop;
      if (scrollTop > 50) {
        this.navbarOpacity = 1;
      } else {
        this.navbarOpacity = 0;
      }
    },
    goToTaskDaily() {
      this.dailyGoldPopup = false;
      this.$nextTick(() => {
        this.toPage('Welfare');
      });
    },
    async getUserLevel() {
      const res = await ApiUserGetSingleWealth();
      this.currentLevel = res.data.level;
      this.limitLevel = res.data.level_limit;
    },
    // 打开客服
    connectQiYu() {
      // 财富等级未达标
      if (this.currentLevel.level_id >= this.limitLevel) {
        this.openKefu({ is_zx: 1 });
      } else {
        this.openKefu();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.mine-page {
  flex: 1;
  flex-shrink: 0;
  background: #f7f8fa;
  overflow: hidden;
  .page-title {
    font-size: 18 * @rem;
    color: #000000;
  }
  .top-icon-list {
    display: flex;
    flex-direction: row-reverse;
    .icon {
      width: 24 * @rem;
      height: 24 * @rem;
      margin-left: 10 * @rem;
      background-size: 100%;
      background-repeat: no-repeat;
      &.kefu {
        background-image: url(~@/assets/images/mine/icon_kefu_new.png);
        position: relative;

        .dot-red {
          display: block;
          padding: 0 3 * @rem;
          height: 11 * @rem;
          background: #f44040;
          border-radius: 6 * @rem;
          border: 1px solid #ffffff;
          font-weight: 600;
          font-size: 9 * @rem;
          color: #ffffff;
          line-height: 11 * @rem;
          text-align: center;
          position: absolute;
          top: -2 * @rem;
          left: 16 * @rem;
          white-space: nowrap;
        }
      }
      &.setting {
        background-image: url(~@/assets/images/mine/icon_setting_new.png);
      }
      &.message {
        background-image: url(~@/assets/images/mine/icon_message_new.png);
        position: relative;
        .dot {
          font-variant-numeric: tabular-nums;
          position: absolute;
          left: 50%;
          top: -5 * @rem;
          padding: 0 5 * @rem;
          height: 14 * @rem;
          border-radius: 7 * @rem;
          background-color: #fe4a55;
          color: #fff;
          font-size: 10 * @rem;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .dot-red {
          width: 5 * @rem;
          height: 5 * @rem;
          border-radius: 50%;
          border: 1 * @rem solid #fff;
          background-color: #ff0000;
          position: absolute;
          top: 2 * @rem;
          right: 2 * @rem;
        }
      }
    }
  }
  .section {
    margin: 0 12 * @rem 16 * @rem;
  }
  .top-user-info {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 218 * @rem;
    background-image: url(~@/assets/images/mine/new/bg_user_new.png);
    background-size: 100% 218 * @rem;
    background-repeat: no-repeat;
    margin: 0;
    padding-left: 12 * @rem;
    .icon-list {
      height: 44 * @rem;
    }
    .center {
      .user-info {
        width: 100%;
        display: flex;
        align-items: center;
        &.no-login {
          .detail {
            .big-text {
              margin-bottom: 10 * @rem;
            }
          }
        }
        .avatar {
          width: 68 * @rem;
          height: 68 * @rem;
          border-radius: 50%;
          border: 2 * @rem solid rgba(255, 255, 255, 0.4);
          box-sizing: border-box;
        }
        .detail {
          flex: 1;
          min-width: 0;
          margin-left: 12 * @rem;
          color: #333333;
          .big-text {
            display: flex;
            align-items: center;
            em {
              font-size: 18 * @rem;
              line-height: 25 * @rem;
              white-space: nowrap;
              font-weight: bold;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            span {
              flex-shrink: 0;
              width: 15 * @rem;
              height: 13 * @rem;
              background: url(~@/assets/images/mine/new/pen.png) no-repeat;
              background-size: 15 * @rem 13 * @rem;
              margin-left: 2 * @rem;
            }
          }
          .small-text {
            font-size: 11 * @rem;
            line-height: 15 * @rem;
            margin-top: 4 * @rem;
            color: #666666;
          }
          .tag {
            display: flex;
            align-items: center;
            height: 16 * @rem;
            margin-top: 8 * @rem;
            .item {
              display: flex;
              align-items: center;
              flex-shrink: 0;
              height: 16 * @rem;
              margin-left: 6 * @rem;
              position: relative;
              background: linear-gradient(
                90deg,
                rgba(230, 232, 240, 0.37) 0%,
                rgba(230, 232, 240, 0.75) 50%,
                #e6e8f0 100%
              );
              border-radius: 8 * @rem;
              padding: 0 6 * @rem 0 19 * @rem;

              img {
                position: absolute;
                top: 0;
                left: 0;
                height: 16 * @rem;
                width: auto;
              }

              em {
                font-weight: 500;
                font-size: 11 * @rem;
                line-height: 16 * @rem;
                color: #333333;
              }

              &::after {
                content: '';
                display: block;
                width: 6 * @rem;
                height: 6 * @rem;
                background: url(~@/assets/images/mine/new/small_right_arrow.png)
                  no-repeat left center;
                background-size: 3 * @rem 6 * @rem;
                margin-left: 3 * @rem;
              }

              &:first-of-type {
                margin-left: 0;
              }

              &.item2 {
                margin-left: 12 * @rem;
                padding-left: 22 * @rem;

                img {
                  left: -6 * @rem;
                }
              }
              &.item3 {
                padding-left: 15 * @rem;

                img {
                  left: -5 * @rem;
                }
              }
            }
          }
        }
      }
      .sign-in {
        flex: 0 0 84 * @rem;
        width: 84 * @rem;
        height: 27 * @rem;
        background-image: url(~@/assets/images/mine/new/pic_sign_in.png);
        background-size: 100%;
        background-repeat: no-repeat;
      }
    }
    .bottom {
      width: 100%;
      height: 30 * @rem;
    }
  }
  .svip-container {
    display: flex;
    align-items: center;
    height: 79 * @rem;
    background: url(~@/assets/images/mine/new/bg-svip.png) no-repeat;
    background-size: 100% 79 * @rem;
    padding: 0 18 * @rem;
    box-sizing: border-box;
    margin-top: -30 * @rem;

    .left {
      flex: 1;
      min-width: 0;

      .svip-img {
        width: 103 * @rem;
        height: 15 * @rem;
        background: url(~@/assets/images/mine/new/svip-icon-new.png) no-repeat;
        background-size: 103 * @rem 15 * @rem;
      }
      .svip-right {
        height: 15 * @rem;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 12 * @rem;
        color: rgba(255, 226, 185, 0.75);
        line-height: 15 * @rem;
        text-align: left;
        margin-top: 11 * @rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .right {
      flex-shrink: 0;
      margin-left: 10 * @rem;

      .svip-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 68 * @rem;
        height: 26 * @rem;
        background: linear-gradient(150deg, #fff4d3 0%, #ffd093 100%);
        border-radius: 18 * @rem;
        font-weight: 600;
        font-size: 12 * @rem;
        color: #43271d;
        line-height: 15 * @rem;
        text-align: center;
        margin: 0 auto;

        .discount {
          max-width: 50 * @rem;
          height: 15 * @rem;
          line-height: 15 * @rem;
          font-weight: 600;
          font-size: 9 * @rem;
          color: #ffffff;
          text-shadow: 0 * @rem 1 * @rem 0 * @rem rgba(211, 81, 0, 0.91);
          text-align: center;
          padding: 0 5 * @rem;
          background: linear-gradient(
            128deg,
            #ffbc79 0%,
            #ff775c 48%,
            #ff4281 100%
          );
          border: 1 * @rem solid rgba(255, 255, 255, 0.84);
          position: absolute;
          top: -12 * @rem;
          right: -9 * @rem;
          border-radius: 14 * @rem 14 * @rem 14 * @rem 0;
        }
      }
      .small-text {
        height: 13 * @rem;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 10 * @rem;
        color: rgba(255, 226, 185, 0.75);
        line-height: 13 * @rem;
        text-align: right;
        margin-top: 8 * @rem;
      }
    }
  }
  .tab-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    .tab-item {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      width: 162 * @rem;
      height: 67 * @rem;
      background: #fff no-repeat;
      background-size: 162 * @rem 67 * @rem;
      border-radius: 12 * @rem;
      padding-left: 16 * @rem;
      margin-bottom: 16 * @rem;
      box-sizing: border-box;

      &:nth-last-of-type(-n + 2) {
        margin-bottom: 0;
      }

      .left {
        flex: 1;
        min-width: 0;

        .tab-title {
          height: 20 * @rem;
          font-weight: bold;
          font-size: 17 * @rem;
          color: #333333;
          line-height: 20 * @rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .tab-subtitle {
          height: 17 * @rem;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #979797;
          line-height: 17 * @rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-top: 8 * @rem;
        }
      }

      .right {
        width: 67 * @rem;
        height: 67 * @rem;
      }
    }
  }
  .property-container {
    background-color: #fff;
    border-radius: 12 * @rem;
    position: relative;
    z-index: 1;

    &.mb44 {
      margin-bottom: 44 * @rem;
    }
    .ptb-cont {
      display: flex;
      align-items: center;
      height: 91 * @rem;
      position: relative;
      border-bottom: 0.5px solid #f0f1f5;
      margin: 0 16 * @rem;

      .left {
        flex: 1;
        min-width: 0;
      }
      .property-title {
        height: 16 * @rem;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 600;
        font-size: 16 * @rem;
        color: #333333;
        line-height: 16 * @rem;
      }
      .ptb-num {
        display: flex;
        align-items: flex-end;
        margin-top: 10 * @rem;

        span {
          height: 30 * @rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: bold;
          font-size: 30 * @rem;
          color: #333333;
          line-height: 30 * @rem;

          &.fzs {
            font-size: 28 * @rem;
          }
          &.none-ptb {
            font-size: 20 * @rem;
            text-indent: 5 * @rem;
          }
        }
        i {
          height: 11 * @rem;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 11 * @rem;
          color: #979797;
          line-height: 11 * @rem;
          margin-bottom: 3 * @rem;
          margin-left: 2 * @rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .ptb-recharge {
        flex-shrink: 0;
        width: 80 * @rem;
        height: 26 * @rem;
        background: #ffe5c8;
        border-radius: 22 * @rem;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 600;
        font-size: 12 * @rem;
        color: #ca6a00;
        line-height: 15 * @rem;
        text-align: center;
        position: absolute;
        top: 20 * @rem;
        right: 0;
      }
    }
    .coupon-info {
      width: 100%;
      height: 69 * @rem;
      display: flex;
      justify-content: space-around;
      align-items: center;
      text-align: center;
      border-radius: 12 * @rem;
      background-color: #fff;
      position: relative;
      z-index: 1;

      &.h81 {
        height: 81 * @rem;
      }
      .coupon-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 69 * @rem;
        position: relative;
        padding: 0 6 * @rem;
        white-space: nowrap;
        &.savings-card-item {
          &::after {
            content: '';
            display: block;
            position: absolute;
            bottom: 8 * @rem;
            right: 0;
            z-index: 1;
            width: 48 * @rem;
            height: 47 * @rem;
            background: url(~@/assets/images/mine/saving-card-bg.png) no-repeat;
            background-size: 48 * @rem 47 * @rem;
          }
          &.notBg::after {
            display: none;
          }
        }

        &:not(:first-of-type) {
          &::before {
            content: '';
            width: 1 * @rem;
            height: 15 * @rem;
            border-radius: 0.5 * @rem;
            background-color: #f0f1f5;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
        span {
          display: block;
          cursor: pointer;
          color: #333;
          font-weight: bold;
          font-family: PingFang SC, PingFang SC;
          font-size: 16 * @rem;
          line-height: 20 * @rem;
          margin-bottom: 4 * @rem;
          position: relative;
          z-index: 2;
        }
        em {
          color: #93999f;
          font-size: 12 * @rem;
          position: relative;
          font-family: PingFang SC, PingFang SC;
          z-index: 2;
        }
        i {
          color: @themeColor;
        }
        .savings-card-img {
          width: 92 * @rem;
          height: 66 * @rem;
        }
      }
    }
    .recharge-activity {
      display: flex;
      align-items: center;
      width: 100%;
      height: 40 * @rem;
      padding: 12 * @rem 10 * @rem 0;
      background: #ffe5c8;
      box-sizing: border-box;
      border-radius: 0 0 12 * @rem 12 * @rem;
      position: absolute;
      bottom: -28 * @rem;
      left: 0;

      &::after {
        flex-shrink: 0;
        content: '';
        display: block;
        width: 5 * @rem;
        height: 8 * @rem;
        background: url(~@/assets/images/mine/new/right-arrow.png) no-repeat;
        background-size: 5 * @rem 8 * @rem;
        margin-left: 2 * @rem;
      }

      span {
        height: 12 * @rem;
        font-weight: 400;
        font-size: 12 * @rem;
        color: #ca6a00;
        line-height: 12 * @rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .modular-container {
    padding: 20 * @rem 16 * @rem;
    background-color: #fff;
    border-radius: 12 * @rem;
    .modular-title {
      height: 16 * @rem;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 600;
      font-size: 16 * @rem;
      color: #333333;
      line-height: 16 * @rem;
    }
    .modular-list {
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      .modular-item {
        width: 25%;
        flex-shrink: 0;
        margin-top: 16 * @rem;

        img {
          width: 40 * @rem;
          height: 40 * @rem;
          margin: 0 auto;
        }
        span {
          display: block;
          width: 100%;
          height: 15 * @rem;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 12 * @rem;
          color: #333333;
          line-height: 15 * @rem;
          text-align: center;
          overflow: hidden;
          padding: 0 5 * @rem;
          box-sizing: border-box;
          margin-top: 3 * @rem;
        }
      }
    }
  }
  .function-section {
    background-color: #fff;
    border-radius: 12 * @rem;
    .function-list {
      padding: 10 * @rem;
      .function-item {
        padding: 2 * @rem 0;
        display: flex;
        align-items: center;
        &:not(:last-of-type) {
          border-bottom: 0.5 * @rem solid #f3f3f8;
        }
        .icon {
          width: 46 * @rem;
          height: 46 * @rem;
          position: relative;

          .dot-red {
            display: block;
            padding: 0 4 * @rem;
            height: 11 * @rem;
            background: #f44040;
            border-radius: 6 * @rem;
            border: 1px solid #ffffff;
            font-weight: 600;
            font-size: 10 * @rem;
            color: #ffffff;
            line-height: 11 * @rem;
            text-align: center;
            position: absolute;
            top: 6 * @rem;
            left: 24 * @rem;
            white-space: nowrap;
          }
        }
        .center-text {
          flex: 1;
          min-width: 0;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-left: 6 * @rem;
          margin-right: 10 * @rem;
          .text {
            font-size: 15 * @rem;
            color: #555555;
          }
          .desc {
            color: #979797;
            font-size: 12 * @rem;
          }
        }
        .right-icon {
          width: 12 * @rem;
          height: 12 * @rem;
          margin-right: 8 * @rem;
          .image-bg('~@/assets/images/mine/new/function-right-icon.png');
        }
      }
    }
  }
}

.test {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44 * @rem;
  font-size: 14 * @rem;
  text-decoration: underline;
  color: #a4a4a4;
}

.daily-gold-container-popup {
  .daily-gold-container {
    position: relative;
    padding-bottom: 20 * @rem;
    .daily-gold-bg {
      width: 246 * @rem;
      height: 115 * @rem;
    }
    .close {
      position: absolute;
      top: 10 * @rem;
      right: 10 * @rem;
      width: 24 * @rem;
      height: 24 * @rem;
      .image-bg('~@/assets/images/close-black-oo.png');
    }
    .title {
      font-size: 16 * @rem;
      color: #333333;
      line-height: 20 * @rem;
      margin-top: 7 * @rem;
      font-weight: 600;
      text-align: center;
    }
    .desc {
      font-size: 15 * @rem;
      color: #8d8d8d;
      line-height: 19 * @rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 10 * @rem;
      .gold-icon {
        width: 18 * @rem;
        height: 18 * @rem;
        background: url('~@/assets/images/mine/daily-gold-icon.png') no-repeat;
        background-size: 18 * @rem 18 * @rem;
        margin-left: 6 * @rem;
      }
      span {
        color: @themeColor;
        font-weight: 600;
        margin-left: 4 * @rem;
      }
    }
    .gold-btn {
      margin: 28 * @rem auto 0;
      width: 205 * @rem;
      height: 36 * @rem;
      border-radius: 18 * @rem;
      background: @themeBg;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16 * @rem;
      color: #ffffff;
      font-weight: 500;
    }
  }
}
</style>
