<template>
  <div class="page exchange-welfare-page">
    <nav-bar-2 :title="$t('兑换福利')" :border="true">
      <template #right>
        <!-- <div class="kefu-btn" @click="toPage('Kefu', {}, 1)"></div> -->
      </template>
    </nav-bar-2>
    <div class="main">
      <div class="top-content">
        <div class="game-info" v-if="gameInfo && gameInfo.title">
          <game-item-2 :gameInfo="gameInfo"></game-item-2>
          <div
            class="game-right"
            @click="toPage('GameDetail', { id: gameInfo.id })"
          >
            <div class="score-title">{{ $t('评分') }}</div>
            <div class="game-score">{{ gameInfo.score10 }}</div>
            <div class="to-detail">
              {{ $t('查看详情') }} <span class="right-icon"></span>
            </div>
          </div>
        </div>

        <div class="coupon-info">
          <div class="title">{{ $t('转游权益') }}</div>
          <div class="number">
            {{ $t('共') }}<span>{{ gameInfo.card_count }}</span
            >个礼包
          </div>
          <div class="money">
            {{ $t('总价值') }}<span>¥</span
            ><span>{{ gameInfo.sum_worth }}</span>
          </div>
        </div>
      </div>
      <div class="zhuanyoudian">
        <div class="desc" v-html="text1"></div>
      </div>
      <load-more v-model="loading" :finished="finished" @loadMore="loadMore">
        <div class="coupon-list">
          <div
            class="coupon-item"
            v-for="(item, index) in couponList"
            :key="index"
            @click="toGiftDetail(item)"
          >
            <div class="coupon-info">
              <div class="coupon-icon"></div>
              <div class="center">
                <div class="title">{{ item.title }}</div>
                <div class="desc">{{ item.cardbody }}</div>
                <div class="worth">{{ item.zy_worth }}</div>
              </div>
            </div>
            <div class="zhuanyou-info">
              <div class="fee">
                {{ $t('消耗') }}<span>{{ item.zy_int }}</span
                >{{ $t('转换点') }}
              </div>
              <div class="operate">
                <div class="operate-btn btn" v-if="!item.is_task">
                  {{ $t('兑换') }}
                </div>
                <div class="operate-btn had btn" v-else>
                  {{ $t('已兑换') }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </load-more>
    </div>
  </div>
</template>

<script>
import { handleTimestamp } from '@/utils/datetime.js';
import { mapGetters, mapMutations } from 'vuex';
import { ApiZhuanyouGameCard } from '@/api/views/zhuanyou.js';
export default {
  name: 'ExchangeWelfare',
  data() {
    return {
      gameId: 0,
      text1: '',
      couponList: [],
      gameInfo: {},
      page: 1,
      listRows: 10,
      loading: false,
      finished: false,
      refreshing: false,
    };
  },
  async created() {
    let { id, gameInfo } = this.$route.params;
    this.gameId = id;
    if (gameInfo) {
      this.gameInfo = gameInfo;
    }
  },
  methods: {
    async handleRefresh() {
      this.refreshing = true;
      await this.getCouponList();
      setTimeout(() => {
        this.refreshing = false;
      }, 1000);
    },
    async getCouponList(action = 1) {
      if (action === 1) {
        this.page = 1;
      } else {
        if (this.finished) {
          return;
        }
        this.page++;
      }
      const res = await ApiZhuanyouGameCard({
        gameId: this.gameId,
        page: this.page,
        listRows: this.listRows,
      });
      let { text1, card_list, game_info } = res.data;

      this.gameInfo = game_info;

      this.text1 = text1.replace(/\n/g, '<br>');

      if (action === 1 || this.page === 1) {
        this.couponList = [];
      }
      this.couponList.push(...card_list);
      if (card_list.length < this.listRows) {
        this.finished = true;
      } else {
        if (this.finished === true) {
          this.finished = false;
        }
      }
    },
    async refresh() {
      await this.getCouponList();
    },
    async loadMore() {
      if (!this.couponList.length) {
        await this.getCouponList();
      } else {
        await this.getCouponList(2);
      }

      this.loading = false;
    },
    toGiftDetail(item) {
      this.toPage('GiftDetail', {
        gift_id: item.id,
        game_id: this.gameInfo.id,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.exchange-welfare-page {
  background-color: #f5f5f6;
  .kefu-btn {
    width: 20 * @rem;
    height: 20 * @rem;
    padding: 10 * @rem 0;
    background: url(~@/assets/images/mine/icon_kefu_black.png) right center
      no-repeat;
    background-size: 20 * @rem 20 * @rem;
  }
  .main {
    background-color: #f5f5f6;
    padding-bottom: calc(10 * @rem + @safeAreaBottom);
    padding-bottom: calc(10 * @rem + @safeAreaBottomEnv);
    .top-content {
      background-color: #fff;
      .game-info {
        box-sizing: border-box;
        padding: 10 * @rem 20 * @rem;
        display: flex;
        align-items: center;
        .game-right {
          box-sizing: border-box;
          width: 70 * @rem;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          padding-right: 4 * @rem;
          .score-title {
            font-size: 12 * @rem;
            color: #000000;
          }
          .game-score {
            font-size: 20 * @rem;
            color: @themeColor;
            font-weight: 600;
            margin-top: 5 * @rem;
          }
          .to-detail {
            font-size: 12 * @rem;
            color: #9a9a9a;
            margin-top: 7 * @rem;
            display: flex;
            align-items: center;
            .right-icon {
              display: block;
              width: 6 * @rem;
              height: 11 * @rem;
              .image-bg('~@/assets/images/zhuanyou/right-icon-black.png');
              margin-left: 2 * @rem;
            }
          }
          .game-name {
            font-size: 15 * @rem;
            color: #333333;
            font-weight: bold;
          }
          .game-text {
            font-size: 12 * @rem;
            color: #999999;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            margin-top: 8 * @rem;
          }
        }
      }

      .coupon-info {
        display: flex;
        align-items: center;
        border-top: 0.5 * @rem solid #ebebeb;
        height: 44 * @rem;
        margin: 0 20 * @rem;
        .title {
          font-size: 13 * @rem;
          color: #929292;
          flex: 1;
          line-height: 44 * @rem;
        }
        .money {
          font-size: 13 * @rem;
          color: #000000;
          margin-left: 12 * @rem;
          line-height: 44 * @rem;
          span {
            font-weight: 600;
            font-size: 12 * @rem;
            color: @themeColor;
            &:nth-of-type(2) {
              font-size: 18 * @rem;
            }
          }
        }
        .number {
          font-size: 13 * @rem;
          color: #000000;
          line-height: 44 * @rem;
          padding-left: 20 * @rem;
          background: url(~@/assets/images/zhuanyou/gift-icon-new.png) left
            center no-repeat;
          background-size: 13 * @rem 14 * @rem;
          span {
            color: @themeColor;
          }
        }
      }
    }
    .zhuanyoudian {
      padding: 10 * @rem 11 * @rem;
      .my {
        font-size: 15 * @rem;
        color: #333333;
        display: flex;
        align-items: center;
        span {
          font-size: 17 * @rem;
          color: #ff7e38;
          font-weight: bold;
          &.refresh-icon {
            display: block;
            width: 16 * @rem;
            height: 16 * @rem;
            background: url(~@/assets/images/zhuanyou/zhuanyoudian-refresh.png)
              center center no-repeat;
            background-size: 15 * @rem 15 * @rem;
            padding: 5 * @rem;
            margin-left: 4 * @rem;
            margin-top: -2 * @rem;
            &.refreshing {
              transform: rotateZ(720deg);
              transition: 1s ease-in-out;
            }
          }
        }
      }
      .desc {
        font-size: 12 * @rem;
        color: #666666;
        line-height: 21 * @rem;
      }
    }
    .coupon-list {
      .coupon-item {
        box-sizing: border-box;
        padding: 0 15 * @rem;
        width: 355 * @rem;
        height: 133 * @rem;
        background: #ffffff;
        border-radius: 12 * @rem;
        margin: 0 auto;
        &:not(:first-of-type) {
          margin-top: 10 * @rem;
        }

        .coupon-info {
          display: flex;
          height: 86 * @rem;
          align-items: center;
          border-bottom: 0.5 * @rem solid #ebebeb;
          .coupon-icon {
            width: 44 * @rem;
            height: 44 * @rem;
            background: url(~@/assets/images/zhuanyou/gift-icon.png) no-repeat;
            background-size: 44 * @rem 44 * @rem;
          }
          .center {
            flex: 1;
            min-width: 0;
            margin-left: 10 * @rem;
            .title {
              font-size: 16 * @rem;
              color: #111111;
              font-weight: 500;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
            .desc {
              font-size: 11 * @rem;
              color: #9a9a9a;
              margin-top: 6 * @rem;
              line-height: 15 * @rem;
              height: 15 * @rem;
              hanging-punctuation: 15 * @rem;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
            .worth {
              font-size: 11 * @rem;
              line-height: 15 * @rem;
              color: #9a9a9a;
              margin-top: 6 * @rem;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
          }
        }
        .zhuanyou-info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 42 * @rem;
          .fee {
            font-size: 12 * @rem;
            color: #000000;
            span {
              color: @themeColor;
            }
          }
          .operate {
            .operate-btn {
              width: 54 * @rem;
              height: 28 * @rem;
              font-size: 13 * @rem;
              color: #ffffff;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 6 * @rem;
              background-color: @themeColor;
              &.had {
                background-color: #c1c1c1;
              }
            }
          }
        }
      }
    }
  }
}
</style>
